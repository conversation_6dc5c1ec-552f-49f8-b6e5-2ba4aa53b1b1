/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.formatadas;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Funcion;
import SasBeans.FuncionFaltas;
import SasBeans.FuncionFerias;
import SasBeans.Pessoa;
import SasBeans.PstInspecao;
import SasBeans.RHPontoDet;
import SasBeans.RastrearEW;
import SasBeansCompostas.LogsSatMobEW;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 *
 * <AUTHOR>
 */
public class LogsSatMobEWDao {

    //private ArquivoLog logexecucao;
    //private ArquivoLog arquivohtml;    
    //private String caminho;
    public List<PstInspecao> listaBoletimTrabalho(String data, String codFil, BigDecimal codigoPessoa, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            List<PstInspecao> Retorno = new ArrayList<>();

            sql = "Select PstInspecao.Sequencia, Clientes.Nred, Clientes.Ende, Clientes.Bairro, Clientes.Cidade, Clientes.Estado,  Clientes.Latitude, \n"
                    + " Clientes.Longitude, PstInspecao.Pergunta, sum(Convert(Int,PstInspecao.Resposta)) Resposta\n"
                    + " From PstInspecao (Nolock)\n"
                    + " Left join PstServ (Nolock)  on PstServ.Secao = PstInspecao.Secao\n"
                    + "                             and PstServ.CodFil = PstInspecao.CodFil\n"
                    + " Left join Clientes (Nolock)  on Clientes.Codigo = PstServ.CodCli\n"
                    + "                             and Clientes.CodFil = PstServ.CodFil\n"
                    + " Left join Pessoa (Nolock)  on Pessoa.Codigo = PstInspecao.CodOperador\n"
                    + " where codinspecao in (Select Codigo from Inspecoes (Nolock) where Descricao = 'RESUMO DO TRABALHO DE CAMPO')\n"
                    + "   and PstInspecao.Pergunta <> 'RESPONSÁVEL'\n"
                    + "   and PstInspecao.Data   = ?\n"
                    + "   and PstInspecao.CodFil = ?\n";

            if (null != codigoPessoa
                    && codigoPessoa != BigDecimal.ZERO) {
                sql += "   and PstInspecao.CodOperador = ?\n";
            }
            sql += " Group by PstInspecao.Sequencia, Clientes.Nred, Clientes.Ende, Clientes.Bairro, Clientes.Cidade, Clientes.Estado, \n"
                    + " Clientes.Latitude, Clientes.Longitude, PstInspecao.Pergunta";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(data);
            consulta.setString(codFil);

            if (null != codigoPessoa
                    && codigoPessoa != BigDecimal.ZERO) {
                consulta.setBigDecimal(codigoPessoa);
            }

            consulta.select();

            PstInspecao pstInspecao;

            while (consulta.Proximo()) {
                pstInspecao = new PstInspecao();

                pstInspecao.setLocal(consulta.getString("Nred"));
                pstInspecao.setLatitude(consulta.getString("Latitude"));
                pstInspecao.setLongitude(consulta.getString("Longitude"));
                pstInspecao.setPergunta(consulta.getString("Pergunta"));
                pstInspecao.setResposta(consulta.getString("Resposta"));

                Retorno.add(pstInspecao);
            }
            consulta.close();
            return Retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.listaBoletimTrabalho - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<Clientes> listaBoletimTrabalhoClientes(String data, String codFil, BigDecimal codigoPessoa, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            List<Clientes> Retorno = new ArrayList<>();

            sql = "Select PstInspecao.Hr_Alter, Clientes.Nred, Clientes.Ende, Clientes.Bairro, Clientes.Cidade, Clientes.Estado,  Clientes.Latitude, Clientes.Longitude\n"
                    + " From PstInspecao (Nolock)\n"
                    + " Left join PstServ (Nolock)  on PstServ.Secao = PstInspecao.Secao\n"
                    + "                             and PstServ.CodFil = PstInspecao.CodFil\n"
                    + " Left join Clientes (Nolock)  on Clientes.Codigo = PstServ.CodCli\n"
                    + "                             and Clientes.CodFil = PstServ.CodFil\n"
                    + " where codinspecao in (Select Codigo from Inspecoes (Nolock) where Descricao = 'BOLETIM DE TRABALHO DE CAMPO')\n"
                    + "   and PstInspecao.Data        = ?\n"
                    + "   and PstInspecao.CodFil      = ?\n";
            if (null != codigoPessoa
                    && codigoPessoa != BigDecimal.ZERO) {
                sql += "   and PstInspecao.CodOperador = ?\n";
            }
            sql += " Group by PstInspecao.Hr_Alter, Clientes.Nred, Clientes.Latitude, Clientes.Longitude, Clientes.Ende, Clientes.Bairro, Clientes.Cidade, Clientes.Estado";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(data);
            consulta.setString(codFil);

            if (null != codigoPessoa
                    && codigoPessoa != BigDecimal.ZERO) {
                consulta.setBigDecimal(codigoPessoa);
            }

            consulta.select();

            Clientes cliente;

            while (consulta.Proximo()) {
                cliente = new Clientes();

                cliente.setNRed(consulta.getString("Nred"));
                cliente.setEnde(consulta.getString("Ende"));
                cliente.setBairro(consulta.getString("Bairro"));
                cliente.setCidade(consulta.getString("Cidade"));
                cliente.setEstado(consulta.getString("Estado"));
                cliente.setLatitude(consulta.getString("Latitude"));
                cliente.setLongitude(consulta.getString("Longitude"));
                cliente.setHr_Alter(consulta.getString("Hr_Alter"));

                Retorno.add(cliente);
            }

            consulta.close();

            return Retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.listaBoletimTrabalhoClientes - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<RastrearEW> listaUltimaComunicacao(String data, BigDecimal codigoPessoa, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            List<RastrearEW> Retorno = new ArrayList<>();

            sql = "SELECT \n"
                    + " Pessoa.Nome,\n"
                    + " RastrearEW.Data,\n"
                    + " RastrearEW.Hora,\n"
                    + " RastrearEW.Latitude,\n"
                    + " RastrearEW.Longitude\n"
                    + " FROM RastrearEW\n"
                    + " JOIN (SELECT\n"
                    + "       X.CodPessoa,\n"
                    + "       MAX(X.Sequencia) Sequencia\n"
                    + "       FROM RastrearEW AS X\n"
                    + "       WHERE  X.Data = ?\n"
                    + "       AND    X.CodPessoa <> 0\n"
                    + "       GROUP BY X.CodPessoa) AS UltimaComunicacao\n"
                    + "   ON RastrearEW.Sequencia = UltimaComunicacao.Sequencia\n"
                    + " JOIN Pessoa\n"
                    + "   ON RastrearEW.CodPessoa = Pessoa.Codigo\n"
                    + " WHERE RastrearEW.Data = ?\n";
            if (null != codigoPessoa
                    && codigoPessoa != BigDecimal.ZERO) {
                sql += "   AND Pessoa.Codigo = ?\n";
            }
            sql += " ORDER BY Pessoa.Nome";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(data);
            consulta.setString(data);

            if (null != codigoPessoa
                    && codigoPessoa != BigDecimal.ZERO) {
                consulta.setBigDecimal(codigoPessoa);
            }

            consulta.select();

            RastrearEW rastrearEW;

            while (consulta.Proximo()) {
                rastrearEW = new RastrearEW();

                rastrearEW.setNome(consulta.getString("Nome"));
                rastrearEW.setData(consulta.getString("Data"));
                rastrearEW.setHora(consulta.getString("Hora"));
                rastrearEW.setLatitude(consulta.getString("Latitude"));
                rastrearEW.setLongitude(consulta.getString("Longitude"));

                Retorno.add(rastrearEW);
            }

            consulta.close();

            return Retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.listaUltimaComunicacao - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<Pessoa> listaBoletimTrabalhoPessoas(String data, String codFil, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            List<Pessoa> Retorno = new ArrayList<>();

            sql = "Select Pessoa.Codigo, Pessoa.Nome\n"
                    + " From PstInspecao (Nolock)\n"
                    + " Left join Pessoa (Nolock)  on Pessoa.Codigo = PstInspecao.CodOperador\n"
                    + " where codinspecao in (Select Codigo from Inspecoes (Nolock) where Descricao = 'BOLETIM DE TRABALHO DE CAMPO')\n"
                    + "   and PstInspecao.Data   = ?\n"
                    + "   and PstInspecao.CodFil = ?\n"
                    + " Group by Pessoa.Codigo, Pessoa.Nome";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(data);
            consulta.setString(codFil);

            consulta.select();

            Pessoa pessoa;

            while (consulta.Proximo()) {
                pessoa = new Pessoa();

                pessoa.setCodigo(consulta.getString("Codigo"));
                pessoa.setNome(consulta.getString("Nome"));

                Retorno.add(pessoa);
            }

            consulta.close();
            return Retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.listaBoletimTrabalhoPessoas - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<Pessoa> listaUltimaComunicacaoPessoas(String data, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            List<Pessoa> Retorno = new ArrayList<>();

            sql = "SELECT \n"
                    + " Pessoa.Nome,\n"
                    + " Pessoa.Codigo\n"
                    + " FROM RastrearEW\n"
                    + " JOIN (SELECT\n"
                    + "       X.CodPessoa,\n"
                    + "       MAX(X.Sequencia) Sequencia\n"
                    + "       FROM RastrearEW AS X\n"
                    + "       WHERE  X.Data = ?\n"
                    + "       AND    X.CodPessoa <> 0\n"
                    + "       GROUP BY X.CodPessoa) AS UltimaComunicacao\n"
                    + "   ON RastrearEW.Sequencia = UltimaComunicacao.Sequencia\n"
                    + " JOIN Pessoa\n"
                    + "   ON RastrearEW.CodPessoa = Pessoa.Codigo\n"
                    + " WHERE RastrearEW.Data = ?\n";
            sql += " ORDER BY Pessoa.Nome";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(data);
            consulta.setString(data);

            consulta.select();

            Pessoa pessoa;

            while (consulta.Proximo()) {
                pessoa = new Pessoa();

                pessoa.setCodigo(consulta.getString("Codigo"));
                pessoa.setNome(consulta.getString("Nome"));

                Retorno.add(pessoa);
            }

            consulta.close();
            return Retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.listaUltimaComunicacaoPessoas - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<Funcion> listaFuncionarios(String codFil, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            List<Funcion> Retorno = new ArrayList<>();

            sql = "SELECT\n"
                    + " Matr,\n"
                    + " Nome\n"
                    + " FROM Funcion\n"
                    + " WHERE Situacao <> 'D'\n"
                    + " AND   CodFil   = ?\n"
                    + " ORDER BY Nome";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(codFil);

            consulta.select();

            Funcion funcion;

            while (consulta.Proximo()) {
                funcion = new Funcion();

                funcion.setMatr(consulta.getString("Matr"));
                funcion.setNome(consulta.getString("Nome"));

                Retorno.add(funcion);
            }

            consulta.close();
            return Retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.listaFuncionarios - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<RHPontoDet> listaAtrasos(String codFil, String data, BigDecimal codPessoaAut, BigDecimal matr, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            List<RHPontoDet> Retorno = new ArrayList<>();

            sql = "Select \n"
                    + " Funcion.Matr, Funcion.Nome, \n"
                    + " DateDiff(mi, (Convert(time,(Case when datepart(dw, RhPonto.dtcompet) = 1 then RhHorario.Hora101\n"
                    + "	 		when datepart(dw, RhPonto.dtcompet) = 2 then RhHorario.Hora201\n"
                    + "	 		when datepart(dw, RhPonto.dtcompet) = 3 then RhHorario.Hora301\n"
                    + "	 		when datepart(dw, RhPonto.dtcompet) = 4 then RhHorario.Hora401\n"
                    + "	 		when datepart(dw, RhPonto.dtcompet) = 5 then RhHorario.Hora501\n"
                    + "	 		when datepart(dw, RhPonto.dtcompet) = 6 then RhHorario.Hora601\n"
                    + "	 		when datepart(dw, RhPonto.dtcompet) = 7 then RhHorario.Hora701 end))), Convert(time,RHPonto.Hora)) Atraso, \n"
                    + " RhPonto.Hora HrRegistro,\n"
                    + " (Case when datepart(dw, RhPonto.dtcompet) = 1 then RhHorario.Hora101\n"
                    + " 			when datepart(dw, RhPonto.dtcompet) = 2 then RhHorario.Hora201\n"
                    + "	 		when datepart(dw, RhPonto.dtcompet) = 3 then RhHorario.Hora301\n"
                    + "	 		when datepart(dw, RhPonto.dtcompet) = 4 then RhHorario.Hora401\n"
                    + "	 		when datepart(dw, RhPonto.dtcompet) = 5 then RhHorario.Hora501\n"
                    + "	 		when datepart(dw, RhPonto.dtcompet) = 6 then RhHorario.Hora601\n"
                    + "	 		when datepart(dw, RhPonto.dtcompet) = 7 then RhHorario.Hora701 end) HrPrevista\n"
                    + " from RhPonto\n"
                    + " Left join Funcion  on RhPonto.Matr = Funcion.Matr\n"
                    + " Left join RhHorario  on RhHorario.Codigo = Funcion.Horario\n"
                    + "                     and RhHorario.CodFil = Funcion.CodFil\n"
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                      AND RHPontoDet.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN pstserv ON pstserv.secao = RHPontoDet.secao \n"
                    + "                   AND pstserv.codfil = Funcion.codfil \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " Where RHPonto.DtCompet  = ?\n"
                    + "   and RhPonto.Batida = 1\n"
                    + "   and Funcion.CodFil = ?\n"
                    + "   and ABS(DateDiff(mi, (Convert(time,(Case when datepart(dw, RhPonto.dtcompet) = 1 then RhHorario.Hora101\n"
                    + " 			when datepart(dw, RhPonto.dtcompet) = 2 then RhHorario.Hora201\n"
                    + " 			when datepart(dw, RhPonto.dtcompet) = 3 then RhHorario.Hora301\n"
                    + " 			when datepart(dw, RhPonto.dtcompet) = 4 then RhHorario.Hora401\n"
                    + " 			when datepart(dw, RhPonto.dtcompet) = 5 then RhHorario.Hora501\n"
                    + " 			when datepart(dw, RhPonto.dtcompet) = 6 then RhHorario.Hora601\n"
                    + " 			when datepart(dw, RhPonto.dtcompet) = 7 then RhHorario.Hora701 end))), Convert(time,RHPonto.Hora))) >= 15";
            if (null != matr && matr != BigDecimal.ZERO) {
                sql += " and Funcion.Matr = ?";
            }
            sql += " AND Case when datepart(dw, RhPonto.dtcompet) = 1 then RhHorario.Hora101\n"
                    + " 			when datepart(dw, RhPonto.dtcompet) = 2 then RhHorario.Hora201\n"
                    + "	 		when datepart(dw, RhPonto.dtcompet) = 3 then RhHorario.Hora301\n"
                    + "	 		when datepart(dw, RhPonto.dtcompet) = 4 then RhHorario.Hora401\n"
                    + "	 		when datepart(dw, RhPonto.dtcompet) = 5 then RhHorario.Hora501\n"
                    + "	 		when datepart(dw, RhPonto.dtcompet) = 6 then RhHorario.Hora601\n"
                    + "	 		when datepart(dw, RhPonto.dtcompet) = 7 then RhHorario.Hora701 end <> ''";
            sql += " ORDER BY Funcion.Nome";

            Consulta consulta = new Consulta(sql, persistencia);

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            consulta.setString(data);
            consulta.setString(codFil);
            if (null != matr && matr != BigDecimal.ZERO) {
                consulta.setBigDecimal(matr);
            }
            consulta.select();

            RHPontoDet rhPontoDet;

            while (consulta.Proximo()) {
                rhPontoDet = new RHPontoDet();

                rhPontoDet.setMatr(consulta.getString("Matr"));
                rhPontoDet.setNome(consulta.getString("Nome"));
                rhPontoDet.setAtraso(consulta.getString("Atraso"));
                rhPontoDet.setHoraRegistro(consulta.getString("HrRegistro"));
                rhPontoDet.setHoraPrevista(consulta.getString("HrPrevista"));

                Retorno.add(rhPontoDet);
            }

            consulta.close();
            return Retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.listaAtrasos - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<FuncionFerias> listaFerias(String codFil, String data, BigDecimal matr, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            List<FuncionFerias> Retorno = new ArrayList<>();

            sql = "DECLARE @DtRef DATE;\n"
                    + " DECLARE @CodFil VARCHAR(10);\n"
                    + " SET @DtRef = ?;\n"
                    + " SET @CodFil = ?;\n"
                    + " IF OBJECT_ID('tempdb.dbo.#tmpFuncionFerias', 'U') IS NOT NULL\n"
                    + "    DROP TABLE #tmpFuncionFerias;\n"
                    + " \n"
                    + " Select \n"
                    + " Funcion.Matr, \n"
                    + " Funcion.Nome, \n"
                    + " y.DtInicioFer, \n"
                    + " y.DtFinalFer, \n"
                    + " NULL Data, \n"
                    + " '' Matr_Subs, \n"
                    + " '' Nome_Subs, \n"
                    + " 2 ordem\n"
                    + " INTO #tmpFuncionFerias\n"
                    + " from Funcion (Nolock)                     \n"
                    + " join (Select \n"
                    + "       min(z.DtInicioFer) DtInicioFer, \n"
                    + "       max(z.DtFinalFer) DtFinalFer,\n"
                    + "       CodFil,\n"
                    + "       Matr\n"
                    + "       From FuncionFerias z (Nolock)\n"
                    + "       where z.DtInicioFer >= DATEADD(Day,-30, @DtRef)\n"
                    + "        and z.DtFinalFer  <= DATEADD(Day,30, @DtRef)\n"
                    + "      Group by z.Matr, Z.CodFil ) y  \n"
                    + "   on y.Matr   = Funcion.Matr\n"
                    + "  AND y.CodFil = Funcion.CodFil\n"
                    + " where Funcion.CodFil = @CodFil\n";
            if (null != matr && matr != BigDecimal.ZERO) {
                sql += " and Funcion.Matr = ?";
            }
            sql += ";\n"
                    + " Select \n"
                    + " Funcion.Matr, \n"
                    + " Funcion.Nome, \n"
                    + " CtrOperv.Data DtInicioFer, \n"
                    + " CtrOperv.Data DtFinalFer, \n"
                    + " CtrOperv.Data, \n"
                    + " Subs.Matr Matr_Subs, \n"
                    + " Subs.Nome Nome_Subs, \n"
                    + " 1 ordem\n"
                    + " from Funcion (Nolock)\n"
                    + " Inner join CtrOperv (Nolock) on CtrOperv.FuncAus = Funcion.Matr                                         \n"
                    + " Left join Funcion Subs  on Subs.Matr = CtrOperv.FuncSubs\n"
                    + " Left join (Select \n"
                    + "            min(z.Data) DtIninicioFer, \n"
                    + "            max(z.Data) DtFinalFer, \n"
                    + "            z.FuncAus \n"
                    + "            from CtrOperv z (Nolock)\n"
                    + "            where z.data >= DATEADD(Day,-30, @DtRef)\n"
                    + "              and z.data <= DATEADD(Day,30, @DtRef)\n"
                    + "              and z.Flag_Excl <> '*'\n"
                    + "            Group by z.FuncAus ) y  \n"
                    + "         on y.FuncAus = Funcion.Matr\n"
                    + " where CtrOperv.Data = @DtRef\n";
            if (null != matr && matr != BigDecimal.ZERO) {
                sql += " and Funcion.Matr = ?";
            }
            sql += " AND CtrOperv.Motivo_Aus = '4' \n"
                    + " and CtrOperv.Flag_Excl <> '*'\n"
                    + " AND Funcion.CodFil = @CodFil\n"
                    + " \n"
                    + " UNION\n"
                    + "                 \n"
                    + " SELECT * FROM #tmpFuncionFerias\n"
                    + "       \n"
                    + " UNION\n"
                    + "                 \n"
                    + " Select \n"
                    + " Funcion.Matr, \n"
                    + " Funcion.Nome, \n"
                    + " y.DtInicioFer, \n"
                    + " y.DtFinalFer, \n"
                    + " NULL, \n"
                    + " '', \n"
                    + " '', \n"
                    + " 3\n"
                    + " from Funcion (Nolock)                     \n"
                    + " join (Select \n"
                    + "       min(z.Dt_Ini) DtInicioFer, \n"
                    + "       max(z.Dt_Fim) DtFinalFer,\n"
                    + "       Matr\n"
                    + "       From F3_Afast z (Nolock)\n"
                    + "       where z.Dt_Ini >= DATEADD(Day,-30, @DtRef)\n"
                    + "        and z.Dt_Fim  <= DATEADD(Day,30, @DtRef)\n"
                    + "      Group by z.Matr ) y  \n"
                    + "   on y.Matr   = Funcion.Matr\n"
                    + " where Funcion.CodFil = @CodFil\n"
                    + " and   Funcion.Situacao = 'R'";
            if (null != matr && matr != BigDecimal.ZERO) {
                sql += " and Funcion.Matr = ?";
            }
            sql += " AND   Funcion.Matr NOT IN(SELECT Matr FROM #tmpFuncionFerias)\n"
                    + " ORDER BY Nome, ordem";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codFil);

            if (null != matr && matr != BigDecimal.ZERO) {
                consulta.setBigDecimal(matr);
                consulta.setBigDecimal(matr);
                consulta.setBigDecimal(matr);
            }
            consulta.select();

            FuncionFerias funcionFerias;

            while (consulta.Proximo()) {
                funcionFerias = new FuncionFerias();

                funcionFerias.setMatr(consulta.getString("Matr"));
                funcionFerias.setNome(consulta.getString("Nome"));
                funcionFerias.setDtInicioFer(consulta.getString("DtInicioFer"));
                funcionFerias.setDtFinalFer(consulta.getString("DtFinalFer"));
                funcionFerias.setData(consulta.getString("Data"));
                funcionFerias.setMatr_Subs(consulta.getString("Matr_Subs"));
                funcionFerias.setNome_Subs(consulta.getString("Nome_Subs"));

                Retorno.add(funcionFerias);
            }

            consulta.close();
            return Retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.listaFerias - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<FuncionFaltas> listaFaltas(String codFil, String data, BigDecimal matr, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            List<FuncionFaltas> Retorno = new ArrayList<>();
            Consulta consulta = new Consulta(sql, persistencia);

            /* sql = "Declare @databatida varchar(10);\n"
                    + " Declare @data Date;\n"
                    + " Declare @codfil int;\n"
                    + " Declare @matr int;\n"
                    + " Declare @qtde int;\n"
                    + " Declare @pst varchar(14);\n"
                    + " \n"
                    + " set @databatida = ?\n"
                    + " set @data = convert(Date, GetDate());\n"
                    + " set @codfil = ?;\n"
                    + " \n"
                    + " SELECT Funcion.* into #relat \n"
                    + " FROM Funcion\n"
                    + " Left Join RHPonto on RHponto.Matr = Funcion.Matr\n"
                    + "                  and RHPonto.DtBatida = @databatida\n"
                    + " where RHponto.DtBatida is null AND Funcion.Situacao <> 'D';\n"
                    + " \n"
                    + " WHILE (SELECT count(*) FROM #relat (nolock)) > 0 BEGIN\n"
                    + "	Select top 01 @matr = matr, @pst = Secao from #relat;\n"
                    + "	Select  @qtde = Count(*) from CtrOperv where FuncAus = @matr and Data = @data;\n"
                    + "	if(@qtde = 0) begin\n"
                    + "	  Insert into CtrOperv(Numero, CodFil, Data, Periodo, Posto, Mesario, FuncAus, Motivo_Aus,Operador, Dt_Alter, Hr_Alter, Flag_Excl) Values(\n"
                    + "	  (Select isnull(Max(Numero)+1,0) from CtrOperv where CodFil = @codfil), @codfil, @data,'D', @pst,  'Mob',  @matr, 1,'Mob', @data, SUBSTRING( convert(varchar, getdate(),108),1,5), '');\n"
                    + "	end      \n"
                    + "	Delete from #relat where matr = @matr;\n"
                    + "	 \n"
                    + "	IF (SELECT count(*) FROM #relat (nolock)) = 0\n"
                    + "		BREAK  \n"
                    + " ELSE  \n"
                    + "	CONTINUE  \n"
                    + " END\n"
                    + " drop Table #relat;";

            consulta.setString(data);
            consulta.setString(codFil);
            consulta.insert();*/
            sql = "Declare @databatida varchar(10);\n"
                    + " Declare @data Date;\n"
                    + " Declare @codfil int;\n"
                    + " Declare @matr int;\n"
                    + " Declare @qtde int;\n"
                    + " Declare @pst varchar(14);\n"
                    + " \n"
                    + " set @databatida = ?\n"
                    + " set @data = convert(Date, GetDate());\n"
                    + " set @codfil = ?;\n"
                    + " \n"
                    + " Select Funcion.matr, Funcion.Nome, CtrOperv.Data, CtrOperv.Posto, PstServ.Local, Funcsubs.Matr MatrCober, FuncSubs.Nome NomeSubs from CtrOperv \n"
                    + " Left Join Funcion on Funcion.Matr = CtrOperv.FuncAus\n"
                    + "                  and Funcion.Codfil = CtrOperv.CodFil\n"
                    + " Left Join Funcion FuncSubs on FuncSubs.Matr = CtrOperv.FuncSubs\n"
                    + "                  and FuncSubs.Codfil = CtrOperv.CodFil\n"
                    + " Left Join PstServ on PStServ.Secao = CtrOperv.Posto\n"
                    + "                  and PStServ.CodFil = CtrOperv.Codfil\n"
                    + " where CtrOperv.Data = @databatida and CtrOperv.codfil = @codfil and CtrOperv.Motivo_Aus = '1' AND Funcion.Situacao <> 'D'";

            if (null != matr && matr != BigDecimal.ZERO) {
                sql += " and Funcion.Matr = ?";
            }
            sql += " ORDER BY Funcion.Nome;";

            consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codFil);
            if (null != matr && matr != BigDecimal.ZERO) {
                consulta.setBigDecimal(matr);
            }
            consulta.select();

            FuncionFaltas funcionFaltas;

            while (consulta.Proximo()) {
                funcionFaltas = new FuncionFaltas();

                funcionFaltas.setMatr(consulta.getString("matr"));
                funcionFaltas.setNome(consulta.getString("Nome"));
                funcionFaltas.setData(consulta.getString("Data"));
                funcionFaltas.setPosto(consulta.getString("Posto"));
                funcionFaltas.setLocal(consulta.getString("Local"));
                funcionFaltas.setMatrCober(consulta.getString("MatrCober"));
                funcionFaltas.setNomeSubs(consulta.getString("NomeSubs"));

                Retorno.add(funcionFaltas);
            }

            consulta.close();
            return Retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.listaFaltas - " + e.getMessage() + "\r\n" + sql);
        }
    }

    /**
     * Lista todos os logs por data Tipos de relatório: 1 - Batida de Ponto 2 -
     * Relatório 3 - Relatório Supervisor 4 - Resumo Ronda 5 - Check Ins
     * Supervisores 6 - Rondas 7 - Rondas completas 8 - Check Ins Prestadores 9
     * - Resumo Rota Prestador 10 - Relatórios Prestador
     *
     * @param primeiro
     * @param linhas
     * @param data
     * @param codFil
     * @param matricula
     * @param codPessoa
     * @param secao
     * @param tipoRonda
     * @param filtroWeb
     * @param codPessoaAut
     * @param tipoRelatorio
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<LogsSatMobEW> obterLogs(int primeiro, int linhas, String data, String codFil, String matricula,
            String codPessoa, String secao, String tipoRonda, String filtroWeb, BigDecimal codPessoaAut, String tipoRelatorio,
            Persistencia persistencia) throws Exception {
        try {
            if (persistencia.getEmpresa().contains("DELTA")) {
                secao = "";
            }
            List<LogsSatMobEW> retorno = new ArrayList<>();
            // Ponto Nao sera visualizado pelo cliente somente relatorios e nao privados Carlos 23/08/2023
            if (tipoRelatorio.equals("") || tipoRelatorio == null) {
                if (null != codPessoaAut) {
                    tipoRelatorio = "1";
                }
            }
            String sql = " SELECT RowConstrainedResult.* FROM ( SELECT *, ROW_NUMBER() OVER ( ORDER BY Data DESC, Hora DESC ) RowNum \n"
                    + " From (\n"
                    + "(SELECT '1' Tipo, CONCAT(RHPonto.DtCompet,';',convert(Bigint,RHPonto.Matr),';',RHPonto.Batida,';',Funcion.codfil,';') Chave,\n"
                    + " RHPontoDet.secao Secao, \n"
                    + " pstserv.local Posto, Funcion.nome Funcionario, RHPonto.DtCompet Data, RHPonto.Hora Hora, RHPontoGEO.latitude Latitude, \n"
                    + " RHPontoGEO.longitude Longitude, '' Titulo, '' Detalhes, CONVERT (varchar, RHPonto.Batida) Fotos,\n"
                    + " Funcion.codfil, CAST(CAST(dbo.fun_calcdistancia(clientes.Latitude, clientes.Longitude, RHPontoGEO.Latitude, RHPontoGEO.Longitude) AS DECIMAL(18,3)) AS VARCHAR(30)) DistanciaKm, Cargos.descricao Cargo, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao Turno \n"
                    //+ " Funcion.codfil, 0 as DistanciaKm, Cargos.descricao Cargo, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao Turno \n"
                    + " FROM RHPonto \n"
                    + " Left Join Funcion on Funcion.Matr = RHPonto.Matr \n"
                    + " LEFT JOIN RHHorario ON Funcion.Horario = RHHorario.Codigo \n"
                    + "                    AND Funcion.CodFil = RHHorario.CodFil \n"
                    + " Left Join Cargos  on Funcion.CodCargo = Cargos.Codigo \n"
                    + " Left Join RHPontoGEO on RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "                      and RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "                      and RHPontoGEO.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                      AND RHPontoDet.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN pstserv ON pstserv.secao = RHPontoDet.secao \n"
                    + "                   AND pstserv.codfil = Funcion.codfil \n"
                    + " LEFT JOIN clientes ON clientes.codigo = pstserv.CodCli AND clientes.codfil = pstserv.codfil";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            // Alteracao Carlos 04/08/2022
            if (persistencia.getEmpresa().contains("CONFE") || persistencia.getEmpresa().contains("DELTA")) {
                sql += " WHERE RHPonto.DtCompet = ? And RHPonto.Batida < 9000 \n"
                        + " and RHPonto.matr = " + matricula;
            } else {
                sql += " WHERE RHPonto.DtCompet = ? And RHPonto.Batida < 9000 \n";
            }

            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and RHPonto.matr = ? \n";
            } else if (matricula.contains("IN")) {
                sql += " and RHPonto.matfr " + matricula + "\n";
            }
            if (!secao.equals("") && !persistencia.getEmpresa().contains("DELTA")) {
                sql += " and pstserv.Secao LIKE '%" + secao + "%' \n";
            }
            sql += ") Union ( \n"
                    + " Select '2' Tipo, CONCAT(tmktdetpst.sequencia,';',tmktdetpst.codfil,';') Chave, pstserv.secao Secao, pstserv.local Posto, \n"
                    + " pessoa.nome Funcionario, tmktdetpst.data Data, tmktdetpst.hora Hora,  tmktdetpst.latitude Latitude, tmktdetpst.longitude Longitude, \n"
                    + " tmktdetpst.historico Titulo, convert(varchar(max),tmktdetpst.detalhes) Detalhes, tmktdetpst.Fotos Fotos,\n"
                    + " tmktdetpst.codfil, CAST(CAST(dbo.fun_calcdistancia(clientes.Latitude, clientes.Longitude, tmktdetpst.Latitude, tmktdetpst.Longitude) AS DECIMAL(18,3)) AS VARCHAR(30)) DistanciaKm, Cargos.descricao Cargo, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao Turno  \n"
                    //+ " tmktdetpst.codfil, 0 as DistanciaKm, Cargos.descricao Cargo, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao Turno  \n"
                    + " from tmktdetpst \n"
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia \n"
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao \n"
                    + "                    and pstserv.codfil = tmktdetpst.codfil \n"
                    + " left join pessoa\n"
                    + "  on tmktdetpst.codPessoa = pessoa.codigo\n"
                    + " left join funcion\n"
                    + "  on pessoa.matr = funcion.matr\n"
                    + " LEFT JOIN RHHorario ON Funcion.Horario = RHHorario.Codigo \n"
                    + "                    AND Funcion.CodFil = RHHorario.CodFil \n"
                    + " Left Join Cargos  on Funcion.CodCargo = Cargos.Codigo \n"
                    + " left join pstserv AS pstServBase\n"
                    + "  on funcion.secao = pstServBase.secao\n"
                    + " left join clientes\n"
                    + "  on pstServBase.codCli = clientes.codigo\n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            // Carlos 04/08/2022
            if (persistencia.getEmpresa().contains("CONFE") || persistencia.getEmpresa().contains("DELTA")) {
                sql += " WHERE tmktdetpst.data = ? \n"
                        + " and RHPonto.matr = " + matricula;
            } else {
                sql += " WHERE tmktdetpst.data = ? \n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and pessoa.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and pessoa.matr " + matricula + "\n";
            }
            if (filtroWeb.equals("1")) {
                sql += " and tmktdetpst.filtroweb = 1 \n";
            }
            if (!codFil.equals("")) {
                sql += " and tmktdetpst.codfil = ? \n";
            }
            if (!codPessoa.equals("")) {
                sql += " and tmktdetpst.codpessoa = ? \n";
            }
            if (!secao.equals("") && !persistencia.getEmpresa().contains("DELTA")) {
                sql += " and pstserv.Secao LIKE '%" + secao + "%' \n";
            }
            sql += " and tmktdetpst.tipocont = 'R') UNION  \n"
                    + " (Select '3' Tipo, CONCAT(tmktdetpst.sequencia,';',tmktdetpst.codfil,';') Chave, pstserv.secao Secao, pstserv.local Posto, \n"
                    + " pessoa.nome Funcionario, tmktdetpst.data Data, tmktdetpst.hora Hora,  tmktdetpst.latitude Latitude, tmktdetpst.longitude Longitude, \n"
                    + " tmktdetpst.historico Titulo, convert(varchar(max),tmktdetpst.detalhes) Detalhes, tmktdetpst.Fotos Fotos,\n"
                    + " tmktdetpst.codfil, '0' AS DistanciaKm, Cargos.descricao Cargo, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao Turno \n"
                    + " from tmktdetpst \n"
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia \n"
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao \n"
                    + "                    and pstserv.codfil = tmktdetpst.codfil \n"
                    + " left join pessoa on pessoa.codigo = tmktdetpst.codpessoa \n"
                    + " left join funcion on funcion.matr = pessoa.matr \n"
                    + "                   and funcion.codfil = tmktdetpst.codfil \n"
                    + " LEFT JOIN RHHorario ON Funcion.Horario = RHHorario.Codigo \n"
                    + "                    AND Funcion.CodFil = RHHorario.CodFil \n"
                    + " Left Join Cargos  on Funcion.CodCargo = Cargos.Codigo \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            if (persistencia.getEmpresa().contains("CONFE") || persistencia.getEmpresa().contains("DELTA")) {
                sql += " WHERE tmktdetpst.data = ? \n"
                        + " and RHPonto.matr = " + matricula;
            } else {
                sql += " WHERE tmktdetpst.data = ? \n";
            }

            if (filtroWeb.equals("1")) {
                sql += " and tmktdetpst.filtroweb = 1 \n";
            }
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!codPessoa.equals("")) {
                sql += " and tmktdetpst.codpessoa = ? \n";
            }
            if (!secao.equals("") && !persistencia.getEmpresa().contains("DELTA")) {
                sql += " and pstserv.Secao LIKE '%" + secao + "%' \n";
            }
            sql += " and tmktdetpst.tipocont = 'RS') UNION (\n"
                    + " select '4' Tipo, CONCAT(rondas.secao,';',CONVERT(varchar,rondas.data,112),';',rondas.hora,';',rondas.codfil,';') Chave, \n"
                    + " rondas.secao Secao, \n"
                    + " pstserv.local Posto, funcion.nome Funcionario, rondas.data Data, max(rondas.hr_alter) Hora, '' Latitude, '' Longitude,'' Titulo, \n"
                    + " CONCAT(min(rondas.hr_alter),';',max(rondas.hr_alter),';',count(rondas.hora),';',\n"
                    + "     (select count(*) from pstdepenronda \n"
                    + "         where pstdepenronda.hora = rondas.hora\n"
                    + "               and pstdepenronda.secao = rondas.secao \n"
                    + "               and pstdepenronda.codfil = rondas.codfil\n"
                    + "               and pstdepenronda.tipo = ?),';') Detalhes, '0' Fotos, \n"
                    + " rondas.codfil, '0' AS DistanciaKm, Cargos.descricao Cargo, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao Turno \n"
                    + " from rondas \n"
                    + " left join funcion on funcion.matr = rondas.matr \n"
                    + "                   and funcion.codfil = rondas.codfil \n"
                    + " LEFT JOIN RHHorario ON Funcion.Horario = RHHorario.Codigo \n"
                    + "                    AND Funcion.CodFil = RHHorario.CodFil \n"
                    + " Left Join Cargos  on Funcion.CodCargo = Cargos.Codigo \n"
                    + " left join pstserv on pstserv.secao = rondas.secao \n"
                    + "                   and pstserv.codfil = rondas.codfil \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE rondas.Data = ? \n";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and rondas.matr = ? \n";
            } else if (matricula.contains("IN")) {
                sql += " and rondas.matr " + matricula + "\n";
            }
            if (!secao.equals("") && !persistencia.getEmpresa().contains("DELTA")) {
                sql += " and pstserv.Secao LIKE '%" + secao + "%' \n";
            }
            sql += " group by rondas.hora, rondas.matr, rondas.secao, rondas.codfil, rondas.data, pstserv.local, funcion.nome, Cargos.descricao, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao )\n"
                    + " UNION (SELECT '5' Tipo, CONCAT(RHPonto.DtCompet,';',convert(Bigint,RHPonto.Matr),';',RHPonto.Batida,';',Funcion.codfil,';') Chave,\n"
                    + " RHPontoDet.secao Secao, \n"
                    + " pstserv.local Posto, Funcion.nome Funcionario, RHPonto.DtBatida Data, RHPonto.Hora Hora, RHPontoGEO.latitude Latitude, \n"
                    + " RHPontoGEO.longitude Longitude, '' Titulo, '' Detalhes, CONVERT (varchar, RHPonto.Batida) Fotos,\n"
                    + " Funcion.codfil, '0' AS DistanciaKm, Cargos.descricao Cargo, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao Turno \n"
                    + " FROM RHPonto \n"
                    + " Left Join Funcion on Funcion.Matr = RHPonto.Matr \n"
                    + " LEFT JOIN RHHorario ON Funcion.Horario = RHHorario.Codigo \n"
                    + "                    AND Funcion.CodFil = RHHorario.CodFil \n"
                    + " Left Join Cargos  on Funcion.CodCargo = Cargos.Codigo \n"
                    + " Left Join RHPontoGEO on RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "                      and RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "                      and RHPontoGEO.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                      AND RHPontoDet.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN pstserv ON pstserv.secao = RHPontoDet.secao \n"
                    + "                   AND pstserv.codfil = Funcion.codfil \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            if (persistencia.getEmpresa().contains("CONFE") || persistencia.getEmpresa().contains("DELTA")) {
                sql += " WHERE RHPonto.DtCompet = ? And RHPonto.Batida > 9000 \n"
                        + " and RHPonto.matr = " + matricula;
            } else {
                sql += " WHERE RHPonto.DtCompet = ? And RHPonto.Batida > 9000 \n";
            }
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and RHPonto.matr = ? \n";
            } else if (matricula.contains("IN")) {
                sql += " and RHPonto.matr " + matricula + "\n";
            }
            if (!secao.equals("") && !persistencia.getEmpresa().contains("DELTA")) {
                sql += " and pstserv.Secao LIKE '%" + secao + "%' \n";
            }
            sql += ") \n";
            if (filtroWeb.equals("0") || filtroWeb.equals("1")) {
                sql += " UNION (\n"
                        + " select '7' Tipo, Convert(Varchar, PstInspecao.Codigo) Chave, \n"
                        + " PstInspecao.Secao Secao, \n"
                        + " pstserv.local Posto, funcion.nome Funcionario, Convert(VarChar,PstInspecao.Data, 112) Data, max(PstInspecao.hr_alter) Hora, \n"
                        + " PstInspecao.Latitude Latitude, PstInspecao.Longitude Longitude,\n"
                        + " Inspecoes.Descricao Titulo, Pessoa.Nome Detalhes, (SELECT TOP 1 X.CaminhoImagem FROM pstinspecao AS X WHERE X.Matr = PstInspecao.Matr AND X.Secao = PstInspecao.Secao AND X.CodFil = PstInspecao.CodFil AND X.Data =PstInspecao.Data AND X.Codigo =PstInspecao.Codigo AND X.CaminhoImagem IS NOT NULL AND X.CaminhoImagem <> '') Fotos, \n"
                        + " PstInspecao.codfil, (SELECT CAST(COUNT(*) AS VARCHAR(30)) FROM PstInspecao AS Y WHERE Y.CaminhoImagem IS NOT NULL AND Y.CaminhoImagem <> '' AND Y.Sequencia NOT IN(10,41) AND Y.Codigo = PstInspecao.Codigo AND Y.CodFil = PstInspecao.CodFil) AS DistanciaKm, Cargos.descricao Cargo, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao Turno \n"
                        + " FROM PstInspecao \n"
                        + " LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao \n"
                        + "                 AND PstServ.Codfil = PstInspecao.CodFil \n"
                        + " LEFT JOIN Funcion ON Funcion.Matr = PstInspecao.Matr \n"
                        + "                AND Funcion.Codfil = PstInspecao.Codfil \n"
                        + " LEFT JOIN RHHorario ON Funcion.Horario = RHHorario.Codigo \n"
                        + "                    AND Funcion.CodFil = RHHorario.CodFil \n"
                        + " Left Join Cargos  on Funcion.CodCargo = Cargos.Codigo \n"
                        + " Left join Pessoa on Pessoa.Codigo = PstInspecao.CodOperador \n"
                        + " LEFT JOIN Inspecoes ON Inspecoes.Codigo = PstInspecao.CodInspecao \n";
                if (codPessoaAut != null) {
                    sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                            + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                            + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                            + "                        and pessoacliaut.flag_excl <> '*' \n";
                }
                sql += " WHERE PstInspecao.Data = ? \n";
                if (!codFil.equals("")) {
                    sql += " AND PstInspecao.CodFil = ? \n";
                }
                if (persistencia.getEmpresa().contains("CONFE") || persistencia.getEmpresa().contains("DELTA")) {
                    sql += " and PstInspecao.matr = " + matricula;
                } else {
                }
                if (!matricula.equals("") && !matricula.contains("IN")) {
                    sql += " AND PstInspecao.Matr = ? \n";
                } else if (matricula.contains("IN")) {
                    sql += " and PstInspecao.matr " + matricula + "\n";
                }
                if (!secao.equals("") && !persistencia.getEmpresa().contains("DELTA")) {
                    sql += " and PstServ.Secao LIKE '%" + secao + "%' \n";

                }

                if (codPessoaAut != null) {
                    sql += " and pessoacliaut.codigo is not null ";
                }

                sql += " GROUP BY PstInspecao.Codigo, Pessoa.Nome, Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, PstInspecao.Data, PstInspecao.Matr, \n"
                        + " PstInspecao.CodInspecao, PstServ.Local, Funcion.Nome, PstInspecao.Latitude, PstInspecao.Longitude, Cargos.descricao, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao ) \n"
                        + " UNION \n"
                        + "( SELECT '9' Tipo, CONCAT(PessoaTrajeto.CodPessoa,';',CONVERT(varchar,PessoaTrajeto.DtCompet,112),';') Chave, '' Secao,\n"
                        + " (SELECT CASE WHEN Contatos.Nome IS NOT NULL THEN Contatos.Nome\n"
                        + "       WHEN Clientes.NRed IS NOT NULL THEN Clientes.NRed END Posto\n"
                        + "FROM PessoaTrajeto p \n"
                        + "LEFT JOIN Contatos ON Contatos.Codigo = p.CodContato\n"
                        + "                 AND Contatos.CodFil = p.CodFil\n"
                        + "LEFT JOIN Clientes ON Clientes.Codigo = p.CodCli\n"
                        + "                 AND Clientes.CodFil = p.CodFil\n"
                        + "WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                    AND p.CodPessoa = PessoaTrajeto.CodPessoa\n"
                        + "                    AND p.Ordem = Max(PessoaTrajeto.Ordem)) Posto, \n"
                        + " Pessoa.Nome Funcionario, CONVERT(Varchar,PessoaTrajeto.DtCompet,112) Data, \n"
                        + " (SELECT TOP 1 (SELECT MAX(h) FROM (VALUES(HrCheg), (HrSaida)) AS VALUE(h)) Hora\n"
                        + "    FROM PessoaTrajeto p WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                            AND CodPessoa = PessoaTrajeto.CodPessoa ORDER BY Hora desc) Hora,\n"
                        + "(SELECT CASE WHEN Contatos.Latitude IS NOT NULL THEN Contatos.Latitude\n"
                        + "       WHEN Clientes.Latitude IS NOT NULL THEN Clientes.Latitude END Latitude\n"
                        + "FROM PessoaTrajeto p \n"
                        + "LEFT JOIN Contatos ON Contatos.Codigo = p.CodContato\n"
                        + "                 AND Contatos.CodFil = p.CodFil\n"
                        + "LEFT JOIN Clientes ON Clientes.Codigo = p.CodCli\n"
                        + "                 AND Clientes.CodFil = p.CodFil\n"
                        + "WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                    AND p.CodPessoa = PessoaTrajeto.CodPessoa\n"
                        + "                    AND p.Ordem = Max(PessoaTrajeto.Ordem) ) Latitude,\n"
                        + "(SELECT CASE WHEN Contatos.Longitude IS NOT NULL THEN Contatos.Longitude\n"
                        + "       WHEN Clientes.Longitude IS NOT NULL THEN Clientes.Longitude END Longitude\n"
                        + "FROM PessoaTrajeto p \n"
                        + "LEFT JOIN Contatos ON Contatos.Codigo = p.CodContato\n"
                        + "                 AND Contatos.CodFil = p.CodFil\n"
                        + "LEFT JOIN Clientes ON Clientes.Codigo = p.CodCli\n"
                        + "                 AND Clientes.CodFil = p.CodFil\n"
                        + "WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                    AND p.CodPessoa = PessoaTrajeto.CodPessoa\n"
                        + "                    AND p.Ordem = Max(PessoaTrajeto.Ordem) ) Longitude, \n"
                        + " '' Titulo,\n"
                        + " CONCAT(min(PessoaTrajeto.HrCheg),';',"
                        + "(SELECT TOP 1 (SELECT MAX(h) FROM (VALUES(HrCheg), (HrSaida)) AS VALUE(h)) Hora\n"
                        + "    FROM PessoaTrajeto p WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                            AND CodPessoa = PessoaTrajeto.CodPessoa ORDER BY Hora desc),';') Detalhes, '0' Fotos, '' CodFil, '0' AS DistanciaKm, '' Cargo, '' Matr, '' Funcao, '' Escala, '' Turno \n"
                        + " FROM PessoaTrajeto\n"
                        + " LEFT JOIN Pessoa on Pessoa.Codigo = PessoaTrajeto.CodPessoa\n"
                        + " WHERE DtCompet = ? \n";
                if (!codPessoa.equals("")) {
                    sql += " AND CodPessoa = ? \n";
                }
                sql += " GROUP BY PessoaTrajeto.CodPessoa, DtCompet, Pessoa.Nome) \n"
                        + " UNION (Select '10' Tipo, CONCAT(tmktdet.sequencia,';',tmktdet.codfil,';') Chave, CONVERT(Varchar, tmktdet.CodCont) Secao, \n"
                        + "    contatos.Nome Posto, pessoa.nome Funcionario, CONVERT(Varchar,tmktdet.data,112) Data, tmktdet.hora Hora, \n"
                        + "    tmktdet.latitude Latitude, tmktdet.longitude Longitude, tmktdet.historico Titulo, convert(varchar(max),tmktdet.detalhes) \n"
                        + "    Detalhes, tmktdet.Fotos Fotos,\n"
                        + "    tmktdet.codfil, '0' AS DistanciaKm, '' Cargo, '' Matr, '' Funcao, '' Escala, '' Turno \n"
                        + "    from tmktdet \n"
                        + "    left join pessoa on pessoa.codigo = tmktdet.codpessoa \n"
                        + "    left join contatos on contatos.codigo = tmktdet.CodCont\n"
                        + "                      and contatos.codfil = tmktdet.codfil\n"
                        + "    WHERE tmktdet.data = ?\n";
                if (persistencia.getEmpresa().contains("CONFE") || persistencia.getEmpresa().contains("DELTA")) {
                    sql += " and Pessoa.matr = " + matricula;
                } else {
                }
                if (!codPessoa.equals("")) {
                    sql += " AND tmktdet.CodPessoa = ? \n";
                }
                sql += ")\n";
            }
            sql += ") a\n";
            if (!tipoRelatorio.equals("")) {
                sql += " WHERE Tipo = ? \n";
            }
            sql += ") AS RowConstrainedResult \n"
                    + " WHERE RowNum >= ? AND RowNum < ? \n";
            sql += " ORDER BY RowNum ";
            Consulta consulta = new Consulta(sql, persistencia);

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                consulta.setString(matricula);
            }
            /*if (!secao.equals("")) {
                consulta.setString(secao);
            }*/

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }
            /*if (!secao.equals("")) {
                consulta.setString(secao);
            }*/

//            if (codPessoaAut != null) {
//                consulta.setBigDecimal(codPessoaAut);
//            }
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }
            /*if (!secao.equals("")) {
                consulta.setString(secao);
            }*/

            consulta.setString(tipoRonda);
//            if (codPessoaAut != null) {
//                consulta.setBigDecimal(codPessoaAut);
//            }
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                consulta.setString(matricula);
            }
            /*if (!secao.equals("")) {
                consulta.setString(secao);
            }*/

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            // }
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                consulta.setString(matricula);
            }
            /*if (!secao.equals("")) {
                consulta.setString(secao);
            }*/

            if (filtroWeb.equals("0") || filtroWeb.equals("1")) {
                consulta.setString(data);
                if (!codFil.equals("")) {
                    consulta.setString(codFil);
                }
                if (!matricula.equals("") && !matricula.contains("IN")) {
                    consulta.setString(matricula);
                }
                /*if (!secao.equals("")) {
                    consulta.setString(secao);
                }*/

                consulta.setString(data);
                if (!codPessoa.equals("")) {
                    consulta.setString(codPessoa);
                }

                consulta.setString(data);
                if (!codPessoa.equals("")) {
                    consulta.setString(codPessoa);
                }
            }

            if (!tipoRelatorio.equals("")) {
                consulta.setString(tipoRelatorio);
            }

            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);

            consulta.select();
            LogsSatMobEW logsSatMobEW;
            while (consulta.Proximo()) {
                logsSatMobEW = new LogsSatMobEW();
                logsSatMobEW.setCodfil(consulta.getString("CodFil"));
                logsSatMobEW.setTipo(consulta.getString("Tipo"));
                logsSatMobEW.setChave(consulta.getString("Chave"));
                logsSatMobEW.setPosto(consulta.getString("Posto"));
                logsSatMobEW.setSecao(consulta.getString("Secao"));
                logsSatMobEW.setFuncionario(consulta.getString("Funcionario"));
                logsSatMobEW.setData(consulta.getString("Data"));
                logsSatMobEW.setHora(consulta.getString("Hora"));
                logsSatMobEW.setLatitude(consulta.getString("Latitude"));
                logsSatMobEW.setLongitude(consulta.getString("Longitude"));
                logsSatMobEW.setTitulo(consulta.getString("Titulo"));
                logsSatMobEW.setDetalhes(consulta.getString("Detalhes"));
                logsSatMobEW.setFotos(consulta.getString("Fotos"));
                logsSatMobEW.setCargo(consulta.getString("Cargo"));

                logsSatMobEW.setMatricula(consulta.getString("Matr"));
                logsSatMobEW.setFuncao(consulta.getString("Funcao"));
                logsSatMobEW.setEscala(consulta.getString("Escala"));
                logsSatMobEW.setTurno(consulta.getString("Turno"));

                if (!consulta.getString("Tipo").equals("7")) {
                    logsSatMobEW.setDistancia_km(consulta.getString("DistanciaKm"));
                    logsSatMobEW.setQtdeFotos("0");
                } else {
                    logsSatMobEW.setDistancia_km("0");
                    logsSatMobEW.setQtdeFotos(consulta.getString("DistanciaKm"));
                }
                retorno.add(logsSatMobEW);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.obterLogs - " + e.getMessage());
        }
    }

    public List<LogsSatMobEW> obterLogs(int primeiro, int linhas, String data1, String data2, String codFil, String matricula, String nomeColaborador,
            String codPessoa, String secao, String tipoRonda, String filtroWeb, BigDecimal codPessoaAut, String tipoRelatorio, String ordem,
            Set<String> resumoCategoria, boolean paginacaoDesabilitada, 
            Persistencia persistencia) throws Exception {
        try {
            if (persistencia.getEmpresa().contains("DELTA")) {
                secao = "";
            }
            List<LogsSatMobEW> retorno = new ArrayList<>();
            if (null == secao || persistencia.getEmpresa().contains("DELTA")) {
                secao = "";
            }

            // Ponto Nao sera visualizado pelo cliente somente relatorios e nao privados Carlos 23/08/2023
            if (tipoRelatorio.equals("") || tipoRelatorio == null) {
                if (null != codPessoaAut) {
                    tipoRelatorio = "1";
                }
            }
            if (null == filtroWeb) {
                filtroWeb = "0";
            }

            if (null == codPessoa) {
                codPessoa = "";
            }

            if (null == nomeColaborador) {
                nomeColaborador = "";
            }

            if (null == matricula) {
                matricula = "";
            }

            String sql = "";
            Consulta consulta;

            try {
                sql = "UPDATE RHPontoGeo\n"
                        + "SET Latitude  = (SELECT TOP 1 Y.Latitude  FROM RHPontoGeo AS Y WHERE Y.Matr = RHPontoGeo.Matr AND Y.Batida IN(1,2,3,4) AND Y.Latitude  NOT IN('[object Position]','[object Geoposition]') AND Y.DtCompet = (SELECT MAX(X.DtCompet) FROM RHPontoGeo AS X WHERE X.Matr = RHPontoGeo.Matr AND X.DtCompet <= RHPontoGeo.DtCompet AND X.Batida IN(1,2,3,4) AND X.Latitude  NOT IN('[object Position]','[object Geoposition]'))),\n"
                        + "    Longitude = (SELECT TOP 1 Y.Longitude FROM RHPontoGeo AS Y WHERE Y.Matr = RHPontoGeo.Matr AND Y.Batida IN(1,2,3,4) AND Y.Longitude <> ''                                              AND Y.DtCompet = (SELECT MAX(X.DtCompet) FROM RHPontoGeo AS X WHERE X.Matr = RHPontoGeo.Matr AND X.DtCompet <= RHPontoGeo.DtCompet AND X.Batida IN(1,2,3,4) AND X.Longitude <> ''))\n"
                        + "FROM RHPontoGeo\n"
                        + "WHERE Latitude IN('[object Position]','[object Geoposition]');";

                consulta = new Consulta(sql, persistencia);
                consulta.update();
            } catch (Exception ex) {
                String erroEx = ex.getMessage();
            }

            try {
                sql = "UPDATE clientes\n"
                        + "SET latitude = REPLACE(latitude, ',','.')\n"
                        + "WHERE CHARINDEX(',', latitude) IS NOT NULL\n"
                        + "AND   CHARINDEX(',', latitude)  > 0;\n"
                        + "\n"
                        + "UPDATE clientes\n"
                        + "SET longitude = REPLACE(longitude, ',','.')\n"
                        + "WHERE CHARINDEX(',', longitude) IS NOT NULL\n"
                        + "AND   CHARINDEX(',', longitude)  > 0;\n"
                        + "UPDATE funcion \n"
                        + "SET codcargo = CONVERT(FLOAT, Cargo)\n"
                        + "WHERE codcargo IS NULL\n"
                        + "AND   cargo is not null \n"
                        + "AND   cargo <> '';\n"
                        + "\n"
                        + "UPDATE PstInspecao\n"
                        + "SET PstInspecao.Matr = Pessoa.Matr\n"
                        + "FROM PstInspecao\n"
                        + "JOIN Pessoa \n"
                        + "  ON PstInspecao.CodOperador = Pessoa.Codigo\n"
                        + "WHERE PstInspecao.Matr = 0\n"
                        + "AND   PstInspecao.CodOperador IS NOT NULL \n"
                        + "AND   PstInspecao.CodOperador > 0;";

                Consulta updLat = new Consulta(sql, persistencia);
                updLat.update();
            } catch (Exception ex) {

            }

            if (null == ordem) {
                ordem = "";
            }

            if (ordem.equals("C")) {
                ordem = "ASC";
            } else {
                ordem = "DESC";
            }

            sql = " SELECT RowConstrainedResult.* FROM ( SELECT *, ROW_NUMBER() OVER ( ORDER BY Data " + ordem + ", Hora " + ordem + " ) RowNum \n"
                    + " From (\n"
                    + "(SELECT '1' Tipo, CONCAT(RHPonto.DtCompet,';',convert(Bigint,RHPonto.Matr),';',RHPonto.Batida,';',Funcion.codfil,';') Chave,\n"
                    + " RHPontoDet.secao Secao, \n"
                    + " pstserv.secao + ' - ' + pstserv.local AS Posto, Funcion.nome Funcionario, RHPonto.DtCompet Data, RHPonto.Hora Hora, RHPontoGEO.latitude Latitude, \n"
                    + " RHPontoGEO.longitude Longitude, '' Titulo, '' Detalhes, CONVERT (varchar, RHPonto.Batida) Fotos,\n"
                    + " Funcion.codfil, CAST(CAST(dbo.fun_calcdistancia(clientes.Latitude, clientes.Longitude, RHPontoGEO.Latitude, RHPontoGEO.Longitude) AS DECIMAL(18,3)) AS VARCHAR(30)) DistanciaKm, Cargos.descricao Cargo, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao Turno, RHPontoImagem.URL \n"
                    //+ " Funcion.codfil, 0 as DistanciaKm, Cargos.descricao Cargo, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao Turno \n"
                    + " FROM RHPonto \n"
                    + " Left Join Funcion on Funcion.Matr = RHPonto.Matr \n"
                    + " LEFT JOIN RHHorario ON Funcion.Horario = RHHorario.Codigo \n"
                    + "                    AND Funcion.CodFil = RHHorario.CodFil \n"
                    + " Left Join Cargos  on Funcion.CodCargo = Cargos.Codigo \n"
                    + " Left Join RHPontoGEO on RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "                      and RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "                      and RHPontoGEO.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                      AND RHPontoDet.Batida = RHPonto.Batida \n"
                    + " LEFT join RHPontoImagem ON RHPontoImagem.matr = RHPonto.matr "
                    + "                         AND RHPontoImagem.dtcompet = RHPonto.dtcompet "
                    + "                         AND RHPontoImagem.batida = RHPonto.batida "
                    + " LEFT JOIN pstserv ON pstserv.secao = RHPontoDet.secao \n"
                    + "                   AND pstserv.codfil = Funcion.codfil \n"
                    + " LEFT JOIN clientes ON clientes.codigo = pstserv.CodCli AND clientes.codfil = pstserv.codfil";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }

            sql += " WHERE RHPonto.DtCompet BETWEEN '" + data1 + "' AND '" + data2 + "' And RHPonto.Batida < 9000 \n";

            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and RHPonto.matr = ? \n";
            } else if (matricula.contains("IN")) {
                sql += " and pstserv.secao " + matricula + "\n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.local LIKE '%" + secao + "%' \n";
            }

            if (!nomeColaborador.equals("")) {
                sql += " and Funcion.nome LIKE '%" + nomeColaborador.toUpperCase() + "%' \n";
            }

            sql += ") Union ( \n"
                    + " Select '2' Tipo, CONCAT(tmktdetpst.sequencia,';',tmktdetpst.codfil,';') Chave, ISNULL(pstserv.secao, pstServBase.secao) Secao, ISNULL(pstserv.secao, pstServBase.secao) + ' - '+ ISNULL(pstserv.local, pstServBase.local) AS Posto, \n"
                    + " pessoa.nome Funcionario, tmktdetpst.data Data, tmktdetpst.hora Hora,  tmktdetpst.latitude Latitude, tmktdetpst.longitude Longitude, \n"
                    + " tmktdetpst.historico Titulo, convert(varchar(max),tmktdetpst.detalhes) Detalhes, tmktdetpst.Fotos Fotos,\n"
                    + " tmktdetpst.codfil, CAST(CAST(dbo.fun_calcdistancia(clientes.Latitude, clientes.Longitude, tmktdetpst.Latitude, tmktdetpst.Longitude) AS DECIMAL(18,3)) AS VARCHAR(30)) DistanciaKm, Cargos.descricao Cargo, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao Turno, '' URL  \n"
                    //+ " tmktdetpst.codfil, 0 as DistanciaKm, Cargos.descricao Cargo, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao Turno  \n"
                    + " from tmktdetpst \n"
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia \n"
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao \n"
                    + "                    and pstserv.codfil = tmktdetpst.codfil \n"
                    + " left join pessoa\n"
                    + "  on tmktdetpst.codPessoa = pessoa.codigo\n"
                    + " left join Funcion\n"
                    + "  on pessoa.matr = Funcion.matr\n"
                    + " LEFT JOIN RHHorario ON Funcion.Horario = RHHorario.Codigo \n"
                    + "                    AND Funcion.CodFil = RHHorario.CodFil \n"
                    + " Left Join Cargos  on Funcion.CodCargo = Cargos.Codigo \n"
                    + " left join pstserv AS pstServBase\n"
                    + "  on funcion.secao = pstServBase.secao\n"
                    + " and pstServBase.codfil = tmktdetpst.codfil\n"
                    + " left join clientes\n"
                    + "  on pstServBase.codCli = clientes.codigo\n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE tmktdetpst.Data BETWEEN '" + data1 + "' AND '" + data2 + "' \n";

            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and pessoa.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and pstserv.Secao " + matricula + "\n";
            }

            if (filtroWeb.equals("1")) {
                sql += " and tmktdetpst.filtroweb = 1 \n";
            }
            if (!codFil.equals("")) {
                sql += " and tmktdetpst.codfil = ? \n";
            }
            if (!codPessoa.equals("")) {
                sql += " and tmktdetpst.codpessoa = ? \n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.posto LIKE '%" + secao + "%' \n";
            }
            if (!nomeColaborador.equals("")) {
                sql += " and pessoa.nome LIKE '%" + nomeColaborador.toUpperCase() + "%' \n";
            }
            sql += " and tmktdetpst.tipocont = 'R') UNION  \n"
                    + " (Select '3' Tipo, CONCAT(tmktdetpst.sequencia,';',tmktdetpst.codfil,';') Chave, pstserv.secao Secao, pstserv.secao + ' - ' +  pstserv.local AS Posto, \n"
                    + " pessoa.nome Funcionario, tmktdetpst.data Data, tmktdetpst.hora Hora,  tmktdetpst.latitude Latitude, tmktdetpst.longitude Longitude, \n"
                    + " tmktdetpst.historico Titulo, convert(varchar(max),tmktdetpst.detalhes) Detalhes, tmktdetpst.Fotos Fotos,\n"
                    + " tmktdetpst.codfil, '0' AS DistanciaKm, Cargos.descricao Cargo, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao Turno, '' URL \n"
                    + " from tmktdetpst \n"
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia \n"
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao \n"
                    + "                    and pstserv.codfil = tmktdetpst.codfil \n"
                    + " left join pessoa on pessoa.codigo = tmktdetpst.codpessoa \n"
                    + " left join funcion\n"
                    + "  on pessoa.matr = funcion.matr\n"
                    + " LEFT JOIN RHHorario ON Funcion.Horario = RHHorario.Codigo \n"
                    + "                    AND Funcion.CodFil = RHHorario.CodFil \n"
                    + " Left Join Cargos  on Funcion.CodCargo = Cargos.Codigo \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE tmktdetpst.Data BETWEEN '" + data1 + "' AND '" + data2 + "' \n";

            if (filtroWeb.equals("1")) {
                sql += " and tmktdetpst.filtroweb = 1 \n";
            }
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!codPessoa.equals("")) {
                sql += " and tmktdetpst.codpessoa = ? \n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.Secao LIKE '%" + secao + "%' \n";
            }
            if (!nomeColaborador.equals("")) {
                sql += " and pessoa.nome LIKE '%" + nomeColaborador.toUpperCase() + "%' \n";
            }
            sql += " and tmktdetpst.tipocont = 'RS') UNION (\n"
                    + " select '4' Tipo, CONCAT(rondas.secao,';',CONVERT(varchar,rondas.data,112),';',rondas.hora,';',rondas.codfil,';') Chave, \n"
                    + " rondas.secao Secao, \n"
                    + " pstserv.secao + ' - ' + pstserv.local AS Posto, funcion.nome Funcionario, rondas.data Data, max(rondas.hr_alter) Hora, '' Latitude, '' Longitude,'' Titulo, \n"
                    + " CONCAT(min(rondas.hr_alter),';',max(rondas.hr_alter),';',count(rondas.hora),';',\n"
                    + "     (select count(*) from pstdepenronda \n"
                    + "         where pstdepenronda.hora = rondas.hora\n"
                    + "               and pstdepenronda.secao = rondas.secao \n"
                    + "               and pstdepenronda.codfil = rondas.codfil\n"
                    + "               and pstdepenronda.tipo = ?),';') Detalhes, '0' Fotos, \n"
                    + " rondas.codfil, '0' AS DistanciaKm, Cargos.descricao Cargo, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao Turno, '' URL \n"
                    + " from rondas \n"
                    + " left join funcion on funcion.matr = rondas.matr \n"
                    + "                   and funcion.codfil = rondas.codfil \n"
                    + " LEFT JOIN RHHorario ON Funcion.Horario = RHHorario.Codigo \n"
                    + "                    AND Funcion.CodFil = RHHorario.CodFil \n"
                    + " Left Join Cargos  on Funcion.CodCargo = Cargos.Codigo \n"
                    + " left join pstserv on pstserv.secao = rondas.secao \n"
                    + "                   and pstserv.codfil = rondas.codfil \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE rondas.Data BETWEEN '" + data1 + "' AND '" + data2 + "' \n";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and rondas.matr = ? \n";
            } else if (matricula.contains("IN")) {
                sql += " and pstserv.Secao " + matricula + "\n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.Secao LIKE '%" + secao + "%' \n";
            }
            if (!nomeColaborador.equals("")) {
                sql += " and funcion.nome LIKE '%" + nomeColaborador.toUpperCase() + "%' \n";
            }
            sql += " group by rondas.hora, rondas.matr, rondas.secao, rondas.codfil, rondas.data, pstserv.secao, pstserv.local, funcion.nome, Cargos.descricao, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao )\n"
                    + " UNION (SELECT '5' Tipo, CONCAT(RHPonto.DtCompet,';',convert(Bigint,RHPonto.Matr),';',RHPonto.Batida,';',Funcion.codfil,';') Chave,\n"
                    + " RHPontoDet.secao Secao, \n"
                    + " pstserv.Secao + ' - ' + pstserv.local AS Posto, Funcion.nome Funcionario, RHPonto.DtBatida Data, RHPonto.Hora Hora, RHPontoGEO.latitude Latitude, \n"
                    + " RHPontoGEO.longitude Longitude, '' Titulo, '' Detalhes, CONVERT (varchar, RHPonto.Batida) Fotos,\n"
                    + " Funcion.codfil, '0' AS DistanciaKm, Cargos.descricao Cargo, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao Turno, '' URL \n"
                    + " FROM RHPonto \n"
                    + " Left Join Funcion on Funcion.Matr = RHPonto.Matr \n"
                    + " LEFT JOIN RHHorario ON Funcion.Horario = RHHorario.Codigo \n"
                    + "                    AND Funcion.CodFil = RHHorario.CodFil \n"
                    + " Left Join Cargos  on Funcion.CodCargo = Cargos.Codigo \n"
                    + " Left Join RHPontoGEO on RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "                      and RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "                      and RHPontoGEO.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                      AND RHPontoDet.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN pstserv ON pstserv.secao = RHPontoDet.secao \n"
                    + "                   AND pstserv.codfil = Funcion.codfil \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE RHPonto.DtCompet BETWEEN '" + data1 + "' AND '" + data2 + "' And RHPonto.Batida > 9000 \n";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and RHPonto.matr = ? \n";
            } else if (matricula.contains("IN")) {
                sql += " and pstserv.Secao " + matricula + "\n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.Secao LIKE '%" + secao + "%' \n";
            }
            if (!nomeColaborador.equals("")) {
                sql += " and funcion.nome LIKE '%" + nomeColaborador.toUpperCase() + "%' \n";
            }
            sql += ") \n";
            if (filtroWeb.equals("0") || filtroWeb.equals("1")) {
                sql += " UNION (\n"
                        + " select '7' Tipo, Convert(Varchar, PstInspecao.Codigo) Chave, \n"
                        + " PstInspecao.Secao Secao, \n"
                        + " pstserv.secao + ' - ' + pstserv.local AS Posto, funcion.nome Funcionario, Convert(VarChar,PstInspecao.Data, 112) Data, max(PstInspecao.hr_alter) Hora, \n"
                        + " PstInspecao.Latitude Latitude, PstInspecao.Longitude Longitude,\n"
                        + " Inspecoes.Descricao Titulo, Pessoa.Nome Detalhes, (SELECT TOP 1 X.CaminhoImagem FROM pstinspecao AS X WHERE X.Matr = PstInspecao.Matr AND X.Secao = PstInspecao.Secao AND X.CodFil = PstInspecao.CodFil AND X.Data =PstInspecao.Data AND X.Codigo =PstInspecao.Codigo AND X.CaminhoImagem IS NOT NULL AND X.CaminhoImagem <> '') Fotos, \n"
                        + " PstInspecao.codfil, (SELECT CAST(COUNT(*) AS VARCHAR(30)) FROM PstInspecao AS Y WHERE Y.CaminhoImagem IS NOT NULL AND Y.CaminhoImagem <> '' AND Y.Sequencia NOT IN(10,41) AND Y.Codigo = PstInspecao.Codigo AND Y.CodFil = PstInspecao.CodFil) AS DistanciaKm, Cargos.descricao Cargo, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao Turno, '' URL \n"
                        + " FROM PstInspecao \n"
                        + " LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao \n"
                        + "                 AND PstServ.Codfil = PstInspecao.CodFil \n"
                        + " LEFT JOIN Funcion ON Funcion.Matr = PstInspecao.Matr \n"
                        + "                AND Funcion.Codfil = PstInspecao.Codfil \n"
                        + " LEFT JOIN RHHorario ON Funcion.Horario = RHHorario.Codigo \n"
                        + "                    AND Funcion.CodFil = RHHorario.CodFil \n"
                        + " Left Join Cargos  on Funcion.CodCargo = Cargos.Codigo \n"
                        + " Left join Pessoa on Pessoa.Codigo = PstInspecao.CodOperador \n"
                        + " LEFT JOIN Inspecoes ON Inspecoes.Codigo = PstInspecao.CodInspecao \n";
                if (codPessoaAut != null) {
                    sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                            + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                            + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                            + "                        and pessoacliaut.flag_excl <> '*' \n";
                }

                sql += " WHERE PstInspecao.Data BETWEEN '" + data1 + "' AND '" + data2 + "' \n";
                if (!codFil.equals("")) {
                    sql += " AND PstInspecao.CodFil = ? \n";
                }
                if (!matricula.equals("") && !matricula.contains("IN")) {
                    sql += " AND PstInspecao.Matr = ? \n";
                } else if (matricula.contains("IN")) {
                    sql += " and PstServ.Secao " + matricula + "\n";
                }
                if (!secao.equals("")) {
                    sql += " and PstServ.Secao LIKE '%" + secao + "%' \n";
                }
                if (!nomeColaborador.equals("")) {
                    sql += " and funcion.nome LIKE '%" + nomeColaborador.toUpperCase() + "%' \n";
                }
                if (codPessoaAut != null) {
                    sql += " and pessoacliaut.codigo is not null ";
                }
                
                sql += " GROUP BY PstInspecao.Codigo, Pessoa.Nome, Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, PstInspecao.Data, PstInspecao.Matr, \n"
                        + " PstInspecao.CodInspecao, PstServ.Secao, PstServ.Local, Funcion.Nome, PstInspecao.Latitude, PstInspecao.Longitude, Cargos.descricao, Funcion.Matr, Funcion.Funcao, Funcion.Escala, RHHorario.Descricao ) \n"
                        + " UNION \n"
                        + "( SELECT '9' Tipo, CONCAT(PessoaTrajeto.CodPessoa,';',CONVERT(varchar,PessoaTrajeto.DtCompet,112),';') Chave, '' Secao,\n"
                        + " (SELECT CASE WHEN Contatos.Nome IS NOT NULL THEN Contatos.Nome\n"
                        + "       WHEN Clientes.NRed IS NOT NULL THEN Clientes.NRed END Posto\n"
                        + "FROM PessoaTrajeto p \n"
                        + "LEFT JOIN Contatos ON Contatos.Codigo = p.CodContato\n"
                        + "                 AND Contatos.CodFil = p.CodFil\n"
                        + "LEFT JOIN Clientes ON Clientes.Codigo = p.CodCli\n"
                        + "                 AND Clientes.CodFil = p.CodFil\n"
                        + "WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                    AND p.CodPessoa = PessoaTrajeto.CodPessoa\n"
                        + "                    AND p.Ordem = Max(PessoaTrajeto.Ordem)) Posto, \n"
                        + " Pessoa.Nome Funcionario, CONVERT(Varchar,PessoaTrajeto.DtCompet,112) Data, \n"
                        + " (SELECT TOP 1 (SELECT MAX(h) FROM (VALUES(HrCheg), (HrSaida)) AS VALUE(h)) Hora\n"
                        + "    FROM PessoaTrajeto p WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                            AND CodPessoa = PessoaTrajeto.CodPessoa ORDER BY Hora desc) Hora,\n"
                        + "(SELECT CASE WHEN Contatos.Latitude IS NOT NULL THEN Contatos.Latitude\n"
                        + "       WHEN Clientes.Latitude IS NOT NULL THEN Clientes.Latitude END Latitude\n"
                        + "FROM PessoaTrajeto p \n"
                        + "LEFT JOIN Contatos ON Contatos.Codigo = p.CodContato\n"
                        + "                 AND Contatos.CodFil = p.CodFil\n"
                        + "LEFT JOIN Clientes ON Clientes.Codigo = p.CodCli\n"
                        + "                 AND Clientes.CodFil = p.CodFil\n"
                        + "WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                    AND p.CodPessoa = PessoaTrajeto.CodPessoa\n"
                        + "                    AND p.Ordem = Max(PessoaTrajeto.Ordem) ) Latitude,\n"
                        + "(SELECT CASE WHEN Contatos.Longitude IS NOT NULL THEN Contatos.Longitude\n"
                        + "       WHEN Clientes.Longitude IS NOT NULL THEN Clientes.Longitude END Longitude\n"
                        + "FROM PessoaTrajeto p \n"
                        + "LEFT JOIN Contatos ON Contatos.Codigo = p.CodContato\n"
                        + "                 AND Contatos.CodFil = p.CodFil\n"
                        + "LEFT JOIN Clientes ON Clientes.Codigo = p.CodCli\n"
                        + "                 AND Clientes.CodFil = p.CodFil\n"
                        + "WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                    AND p.CodPessoa = PessoaTrajeto.CodPessoa\n"
                        + "                    AND p.Ordem = Max(PessoaTrajeto.Ordem) ) Longitude, \n"
                        + " '' Titulo,\n"
                        + " CONCAT(min(PessoaTrajeto.HrCheg),';',"
                        + "(SELECT TOP 1 (SELECT MAX(h) FROM (VALUES(HrCheg), (HrSaida)) AS VALUE(h)) Hora\n"
                        + "    FROM PessoaTrajeto p WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                            AND CodPessoa = PessoaTrajeto.CodPessoa ORDER BY Hora desc),';') Detalhes, '0' Fotos, '' CodFil, '0' AS DistanciaKm, '' Cargo, '' Matr, '' Funcao, '' Escala, '' Turno, '' URL \n"
                        + " FROM PessoaTrajeto\n"
                        + " LEFT JOIN Pessoa on Pessoa.Codigo = PessoaTrajeto.CodPessoa\n"
                        + " WHERE DtCompet BETWEEN '" + data1 + "' AND '" + data2 + "' \n";
                if (!codPessoa.equals("")) {
                    sql += " AND CodPessoa = ? \n";
                }
                sql += " GROUP BY PessoaTrajeto.CodPessoa, DtCompet, Pessoa.Nome) \n"
                        + " UNION (Select '10' Tipo, CONCAT(tmktdet.sequencia,';',tmktdet.codfil,';') Chave, CONVERT(Varchar, tmktdet.CodCont) Secao, \n"
                        + "    contatos.Nome Posto, pessoa.nome Funcionario, CONVERT(Varchar,tmktdet.data,112) Data, tmktdet.hora Hora, \n"
                        + "    tmktdet.latitude Latitude, tmktdet.longitude Longitude, tmktdet.historico Titulo, convert(varchar(max),tmktdet.detalhes) \n"
                        + "    Detalhes, tmktdet.Fotos Fotos,\n"
                        + "    tmktdet.codfil, '0' AS DistanciaKm, '' Cargo, '' Matr, '' Funcao, '' Escala, '' Turno, '' URL \n"
                        + "    from tmktdet \n"
                        + "    left join pessoa on pessoa.codigo = tmktdet.codpessoa \n"
                        + "    left join contatos on contatos.codigo = tmktdet.CodCont\n"
                        + "                      and contatos.codfil = tmktdet.codfil\n"
                        + "    WHERE tmktdet.data BETWEEN '" + data1 + "' AND '" + data2 + "' \n";
                if (!codPessoa.equals("")) {
                    sql += " AND tmktdet.CodPessoa = ? \n";
                }
                sql += ")\n";
            }
            sql += ") a\n";
            if (!tipoRelatorio.equals("")) {
                sql += " WHERE Tipo <> ? \n";
            }
            sql += ") AS RowConstrainedResult \n"
                    + " WHERE RowNum >= ? AND RowNum < ? \n";
            sql += " ORDER BY RowNum ";

            consulta = new Consulta(sql, persistencia);

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            // }
            //consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                consulta.setString(matricula);
            }
            /*if (!secao.equals("")) {
                consulta.setString(secao);
            }*/

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            //consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }
            /*if (!secao.equals("")) {
                consulta.setString(secao);
            }*/

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            // }
            //consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }
            /*if (!secao.equals("")) {
                consulta.setString(secao);
            }*/

            consulta.setString(tipoRonda);
            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            // }
            //consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                consulta.setString(matricula);
            }
            /*if (!secao.equals("")) {
                consulta.setString(secao);
            }*/

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            //consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                consulta.setString(matricula);
            }
            /*if (!secao.equals("")) {
                consulta.setString(secao);
            }*/

            if (filtroWeb.equals("0") || filtroWeb.equals("1")) {
                //consulta.setString(data);
                if (!codFil.equals("")) {
                    consulta.setString(codFil);
                }
                if (!matricula.equals("") && !matricula.contains("IN")) {
                    consulta.setString(matricula);
                }
                /*if (!secao.equals("")) {
                    consulta.setString(secao);
                }*/

                //consulta.setString(data);
                if (!codPessoa.equals("")) {
                    consulta.setString(codPessoa);
                }

                //consulta.setString(data);
                if (!codPessoa.equals("")) {
                    consulta.setString(codPessoa);
                }
            }

            if (!tipoRelatorio.equals("")) {
                consulta.setString(tipoRelatorio);
            }
            if (paginacaoDesabilitada) {
                linhas = 99999;
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);

            consulta.select();
            boolean filtrarSaida = resumoCategoria.contains(FILTRO_SAIDA);
            boolean filtrarEntrada = resumoCategoria.contains(FILTRO_ENTRADA);
            boolean batidaSaida = false;
            
            LogsSatMobEW logsSatMobEW;
            while (consulta.Proximo()) {
                if (resumoCategoria.contains(consulta.getString("Tipo"))) {                    
                    // Quando o registro é tipo 1, pode representar entrada ou 
                    // saida, precisa verificar a quantidade de batidas
                    // Adicionar outros tipos também na lista
                    if (TIPO_ENTRADA_OU_SAIDA.equals(consulta.getString("Tipo"))) {
                        String batida = consulta.getString("Chave").split(";")[2];
                        batidaSaida = batida.equals("2") || batida.equals("4");
                    }                 
                    boolean adicionarItem = (filtrarEntrada && !batidaSaida) ||
                            (filtrarSaida && batidaSaida) ||
                            (!filtrarEntrada && !filtrarSaida);                            
                    if (adicionarItem) {                        
                        logsSatMobEW = new LogsSatMobEW();
                        logsSatMobEW.setCodfil(consulta.getString("CodFil"));
                        logsSatMobEW.setTipo(consulta.getString("Tipo"));
                        logsSatMobEW.setChave(consulta.getString("Chave"));
                        logsSatMobEW.setPosto(consulta.getString("Posto"));
                        logsSatMobEW.setSecao(consulta.getString("Secao"));
                        logsSatMobEW.setFuncionario(consulta.getString("Funcionario"));
                        logsSatMobEW.setData(consulta.getString("Data"));
                        logsSatMobEW.setHora(consulta.getString("Hora"));
                        logsSatMobEW.setLatitude(consulta.getString("Latitude"));
                        logsSatMobEW.setLongitude(consulta.getString("Longitude"));
                        logsSatMobEW.setTitulo(consulta.getString("Titulo"));
                        logsSatMobEW.setDetalhes(consulta.getString("Detalhes"));
                        logsSatMobEW.setFotos(consulta.getString("Fotos"));
                        logsSatMobEW.setCargo(consulta.getString("Cargo"));

                        logsSatMobEW.setMatricula(consulta.getString("Matr"));
                        logsSatMobEW.setFuncao(consulta.getString("Funcao"));
                        logsSatMobEW.setEscala(consulta.getString("Escala"));
                        logsSatMobEW.setTurno(consulta.getString("Turno"));
                        logsSatMobEW.setURL(consulta.getString("URL"));

                        if (!consulta.getString("Tipo").equals("7")) {
                            logsSatMobEW.setDistancia_km(consulta.getString("DistanciaKm"));
                            logsSatMobEW.setQtdeFotos("0");
                        } else {
                            logsSatMobEW.setDistancia_km("0");
                            logsSatMobEW.setQtdeFotos(consulta.getString("DistanciaKm"));
                        }
                        retorno.add(logsSatMobEW);
                    }
                }
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.obterLogs - " + e.getMessage());
        }
    }
    private static final String TIPO_ENTRADA_OU_SAIDA = "1";
    private static final String FILTRO_ENTRADA = "11";
    private static final String FILTRO_SAIDA = "12";

    /**
     * Lista todos os logs por data Tipos de relatório: 1 - Batida de Ponto 2 -
     * Relatório 3 - Relatório Supervisor 4 - Resumo Ronda 5 - Check Ins
     * Supervisores 6 - Rondas 7 - Rondas completas 8 - Check Ins Prestadores 9
     * - Resumo Rota Prestador 10 - Relatórios Prestador
     *
     * @param primeiro
     * @param linhas
     * @param data
     * @param codFil
     * @param matricula
     * @param codPessoa
     * @param secao
     * @param tipoRonda
     * @param filtroWeb
     * @param codPessoaAut
     * @param tipoRelatorio
     * @param hora
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<LogsSatMobEW> obterLogsHistorico(int primeiro, int linhas, String data, String codFil, String matricula,
            String codPessoa, String secao, String tipoRonda, String filtroWeb, BigDecimal codPessoaAut, String tipoRelatorio, String hora,
            Persistencia persistencia) throws Exception {
        try {
            String sql = "";
            Consulta consulta;

            try {
                sql = "UPDATE RHPontoGeo\n"
                        + "SET Latitude  = (SELECT TOP 1 Y.Latitude  FROM RHPontoGeo AS Y WHERE Y.Matr = RHPontoGeo.Matr AND Y.Batida IN(1,2,3,4) AND Y.Latitude  <> '[object Geoposition]' AND Y.DtCompet = (SELECT MAX(X.DtCompet) FROM RHPontoGeo AS X WHERE X.Matr = RHPontoGeo.Matr AND X.DtCompet <= RHPontoGeo.DtCompet AND X.Batida IN(1,2,3,4) AND X.Latitude  <> '[object Geoposition]')),\n"
                        + "    Longitude = (SELECT TOP 1 Y.Longitude FROM RHPontoGeo AS Y WHERE Y.Matr = RHPontoGeo.Matr AND Y.Batida IN(1,2,3,4) AND Y.Longitude <> ''                     AND Y.DtCompet = (SELECT MAX(X.DtCompet) FROM RHPontoGeo AS X WHERE X.Matr = RHPontoGeo.Matr AND X.DtCompet <= RHPontoGeo.DtCompet AND X.Batida IN(1,2,3,4) AND X.Longitude <> ''))\n"
                        + "FROM RHPontoGeo\n"
                        + "WHERE Latitude = '[object Geoposition]';";

                consulta = new Consulta(sql, persistencia);
                consulta.update();
            } catch (Exception ex) {
            }

            List<LogsSatMobEW> retorno = new ArrayList<>();
            sql = " SELECT RowConstrainedResult.* FROM ( SELECT *, ROW_NUMBER() OVER ( ORDER BY Data DESC, Hora DESC ) RowNum \n"
                    + " From (\n"
                    + "(SELECT '1' Tipo, CONCAT(RHPonto.DtCompet,';',convert(Bigint,RHPonto.Matr),';',RHPonto.Batida,';',Funcion.codfil,';') Chave,\n"
                    + " RHPontoDet.secao Secao, \n"
                    + " pstserv.local Posto, Funcion.nome Funcionario, RHPonto.DtCompet Data, RHPonto.Hora Hora, RHPontoGEO.latitude Latitude, \n"
                    + " RHPontoGEO.longitude Longitude, '' Titulo, '' Detalhes, CONVERT (varchar, RHPonto.Batida) Fotos,\n"
                    + " Funcion.codfil, CAST(CAST(dbo.fun_calcdistancia(clientes.Latitude, clientes.Longitude, RHPontoGEO.Latitude, RHPontoGEO.Longitude) AS DECIMAL(18,3)) AS VARCHAR(30)) DistanciaKm, Cargos.descricao Cargo \n"
                    //+ " Funcion.codfil, 0 as DistanciaKm, Cargos.descricao Cargo \n"
                    + " FROM RHPonto \n"
                    + " Left Join Funcion on Funcion.Matr = RHPonto.Matr \n"
                    + " Left Join Cargos ON Funcion.CodCargo = Cargos.Codigo \n"
                    + " Left Join RHPontoGEO on RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "                      and RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "                      and RHPontoGEO.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                      AND RHPontoDet.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN pstserv ON pstserv.secao = RHPontoDet.secao \n"
                    + "                   AND pstserv.codfil = Funcion.codfil \n"
                    + " LEFT JOIN clientes ON clientes.codigo = pstserv.CodCli AND clientes.codfil = pstserv.codfil";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }

            sql += " WHERE RHPonto.DtCompet = ? And RHPonto.Batida < 9000 \n";

            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and RHPonto.matr = ? \n";
            } else if (matricula.contains("IN")) {
                sql += " and RHPonto.matr " + matricula + "\n";
            }

            if (!hora.equals("")) {
                sql += " and RHPonto.hora < ? \n";
            }
            if (!secao.equals("")) {
                sql += " and RHPontoDet.Secao = ? \n";
            }
            sql += ") Union ( \n"
                    + " Select '2' Tipo, CONCAT(tmktdetpst.sequencia,';',tmktdetpst.codfil,';') Chave, pstserv.secao Secao, pstserv.local Posto, \n"
                    + " pessoa.nome Funcionario, tmktdetpst.data Data, tmktdetpst.hora Hora,  tmktdetpst.latitude Latitude, tmktdetpst.longitude Longitude, \n"
                    + " tmktdetpst.historico Titulo, convert(varchar(max),tmktdetpst.detalhes) Detalhes, tmktdetpst.Fotos Fotos,\n"
                    + " tmktdetpst.codfil, CAST(CAST(dbo.fun_calcdistancia(clientes.Latitude, clientes.Longitude, tmktdetpst.Latitude, tmktdetpst.Longitude) AS DECIMAL(18,3)) AS VARCHAR(30)) DistanciaKm, Cargos.descricao Cargo  \n"
                    //+ " tmktdetpst.codfil, 0 as DistanciaKm, Cargos.descricao Cargo  \n"
                    + " from tmktdetpst \n"
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia \n"
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao \n"
                    + "                    and pstserv.codfil = tmktdetpst.codfil \n"
                    + " left join pessoa\n"
                    + "  on tmktdetpst.codPessoa = pessoa.codigo\n"
                    + " left join funcion\n"
                    + "  on pessoa.matr = funcion.matr\n"
                    + " Left Join Cargos ON Funcion.CodCargo = Cargos.Codigo \n"
                    + " left join pstserv AS pstServBase\n"
                    + "  on funcion.secao = pstServBase.secao\n"
                    + " left join clientes\n"
                    + "  on pstServBase.codCli = clientes.codigo\n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE tmktdetpst.data = ? \n";
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and pessoa.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and pessoa.matr " + matricula + "\n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and pessoa.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and pessoa.matr " + matricula + "\n";
            }
            if (filtroWeb.equals("1")) {
                sql += " and tmktdetpst.filtroweb = 1 \n";
            }
            if (!codFil.equals("")) {
                sql += " and tmktdetpst.codfil = ? \n";
            }
            if (!codPessoa.equals("")) {
                sql += " and tmktdetpst.codpessoa = ? \n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.secao = ? \n";
            }
            sql += " and tmktdetpst.tipocont = 'R') UNION  \n"
                    + " (Select '3' Tipo, CONCAT(tmktdetpst.sequencia,';',tmktdetpst.codfil,';') Chave, pstserv.secao Secao, pstserv.local Posto, \n"
                    + " pessoa.nome Funcionario, tmktdetpst.data Data, tmktdetpst.hora Hora,  tmktdetpst.latitude Latitude, tmktdetpst.longitude Longitude, \n"
                    + " tmktdetpst.historico Titulo, convert(varchar(max),tmktdetpst.detalhes) Detalhes, tmktdetpst.Fotos Fotos,\n"
                    + " tmktdetpst.codfil, '0' AS DistanciaKm, '' Cargo \n"
                    + " from tmktdetpst \n"
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia \n"
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao \n"
                    + "                    and pstserv.codfil = tmktdetpst.codfil \n"
                    + " left join pessoa on pessoa.codigo = tmktdetpst.codpessoa \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE tmktdetpst.data = ? \n";

            if (filtroWeb.equals("1")) {
                sql += " and tmktdetpst.filtroweb = 1 \n";
            }
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!codPessoa.equals("")) {
                sql += " and tmktdetpst.codpessoa = ? \n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.secao = ? \n";
            }
            sql += " and tmktdetpst.tipocont = 'RS') UNION (\n"
                    + " select '4' Tipo, CONCAT(rondas.secao,';',CONVERT(varchar,rondas.data,112),';',rondas.hora,';',rondas.codfil,';') Chave, \n"
                    + " rondas.secao Secao, \n"
                    + " pstserv.local Posto, funcion.nome Funcionario, rondas.data Data, max(rondas.hr_alter) Hora, '' Latitude, '' Longitude,'' Titulo, \n"
                    + " CONCAT(min(rondas.hr_alter),';',max(rondas.hr_alter),';',count(rondas.hora),';',\n"
                    + "     (select count(*) from pstdepenronda \n"
                    + "         where pstdepenronda.hora = rondas.hora\n"
                    + "               and pstdepenronda.secao = rondas.secao \n"
                    + "               and pstdepenronda.codfil = rondas.codfil\n"
                    + "               and pstdepenronda.tipo = ?),';') Detalhes, '0' Fotos, \n"
                    + " rondas.codfil, '0' AS DistanciaKm, '' Cargo \n"
                    + " from rondas \n"
                    + " left join funcion on funcion.matr = rondas.matr \n"
                    + "                   and funcion.codfil = rondas.codfil \n"
                    + " Left Join Cargos ON Funcion.CodCargo = Cargos.Codigo \n"
                    + " left join pstserv on pstserv.secao = rondas.secao \n"
                    + "                   and pstserv.codfil = rondas.codfil \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE rondas.Data = ? \n";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("")) {
                sql += " and rondas.matr = ? \n";
            }
            if (!secao.equals("")) {
                sql += " and rondas.secao = ? \n";
            }
            sql += " group by rondas.hora, rondas.matr, rondas.secao, rondas.codfil, rondas.data, pstserv.local, funcion.nome, Cargos.descricao )\n"
                    + " UNION (SELECT '5' Tipo, CONCAT(RHPonto.DtCompet,';',convert(Bigint,RHPonto.Matr),';',RHPonto.Batida,';',Funcion.codfil,';') Chave,\n"
                    + " RHPontoDet.secao Secao, \n"
                    + " pstserv.local Posto, Funcion.nome Funcionario, RHPonto.DtBatida Data, RHPonto.Hora Hora, RHPontoGEO.latitude Latitude, \n"
                    + " RHPontoGEO.longitude Longitude, '' Titulo, '' Detalhes, CONVERT (varchar, RHPonto.Batida) Fotos,\n"
                    + " Funcion.codfil, '0' AS DistanciaKm, Cargos.descricao Cargo \n"
                    + " FROM RHPonto \n"
                    + " Left Join Funcion on Funcion.Matr = RHPonto.Matr \n"
                    + " Left Join Cargos ON Funcion.CodCargo = Cargos.Codigo \n"
                    + " Left Join RHPontoGEO on RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "                      and RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "                      and RHPontoGEO.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                      AND RHPontoDet.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN pstserv ON pstserv.secao = RHPontoDet.secao \n"
                    + "                   AND pstserv.codfil = Funcion.codfil \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE RHPonto.DtCompet = ? And RHPonto.Batida > 9000 \n";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("")) {
                sql += " and RHPonto.matr = ? \n";
            }
            if (!secao.equals("")) {
                sql += " and RHPontoDet.Secao = ? \n";
            }
            sql += ") \n";
            if (filtroWeb.equals("0") || filtroWeb.equals("1")) {
                sql += " UNION (\n"
                        + " select '7' Tipo, Convert(Varchar, PstInspecao.Codigo) Chave, \n"
                        + " PstInspecao.Secao Secao, \n"
                        + " pstserv.local Posto, funcion.nome Funcionario, Convert(VarChar,PstInspecao.Data, 112) Data, max(PstInspecao.hr_alter) Hora, \n"
                        + " PstInspecao.Latitude Latitude, PstInspecao.Longitude Longitude,\n"
                        + " Inspecoes.Descricao Titulo, Pessoa.Nome Detalhes, (SELECT TOP 1 X.CaminhoImagem FROM pstinspecao AS X WHERE X.Matr = PstInspecao.Matr AND X.Secao = PstInspecao.Secao AND X.CodFil = PstInspecao.CodFil AND X.Data =PstInspecao.Data AND X.Codigo =PstInspecao.Codigo AND X.CaminhoImagem IS NOT NULL AND X.CaminhoImagem <> '') Fotos, \n"
                        + " PstInspecao.codfil, '0' AS DistanciaKm, Cargos.descricao Cargo \n"
                        + " FROM PstInspecao \n"
                        + " LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao \n"
                        + "                 AND PstServ.Codfil = PstInspecao.CodFil \n"
                        + " LEFT JOIN Funcion ON Funcion.Matr = PstInspecao.Matr \n"
                        + "                AND Funcion.Codfil = PstInspecao.Codfil \n"
                        + " Left Join Cargos ON Funcion.CodCargo = Cargos.Codigo \n"
                        + " Left join Pessoa on Pessoa.Codigo = PstInspecao.CodOperador \n"
                        + " LEFT JOIN Inspecoes ON Inspecoes.Codigo = PstInspecao.CodInspecao \n";
                if (codPessoaAut != null) {
                    sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                            + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                            + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                            + "                        and pessoacliaut.flag_excl <> '*' \n";
                }

                sql += " WHERE PstInspecao.Data = ? \n";
                if (!codFil.equals("")) {
                    sql += " AND PstInspecao.CodFil = ? \n";
                }
                if (!matricula.equals("")) {
                    sql += " AND PstInspecao.Matr = ? \n";
                }
                if (!secao.equals("")) {
                    sql += " AND PstInspecao.Secao = ? \n";
                }
                if (codPessoaAut != null) {
                    sql += " and pessoacliaut.codigo is not null";
                }
                sql += " GROUP BY PstInspecao.Codigo, Pessoa.Nome, Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, PstInspecao.Data, PstInspecao.Matr, \n"
                        + " PstInspecao.CodInspecao, PstServ.Local, Funcion.Nome, PstInspecao.Latitude, PstInspecao.Longitude, Cargos.descricao ) \n"
                        + " UNION \n"
                        + "( SELECT '9' Tipo, CONCAT(PessoaTrajeto.CodPessoa,';',CONVERT(varchar,PessoaTrajeto.DtCompet,112),';') Chave, '' Secao,\n"
                        + " '' Posto, Pessoa.Nome Funcionario, CONVERT(Varchar,PessoaTrajeto.DtCompet,112) Data, \n"
                        + " (SELECT TOP 1 (SELECT MAX(h) FROM (VALUES(HrCheg), (HrSaida)) AS VALUE(h)) Hora\n"
                        + "    FROM PessoaTrajeto p WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                            AND CodPessoa = PessoaTrajeto.CodPessoa ORDER BY Hora desc) Hora, '' Latitude, '' Longitude,\n"
                        + " '' Titulo,\n"
                        + " CONCAT(min(PessoaTrajeto.HrCheg),';',"
                        + "(SELECT TOP 1 (SELECT MAX(h) FROM (VALUES(HrCheg), (HrSaida)) AS VALUE(h)) Hora\n"
                        + "    FROM PessoaTrajeto p WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                            AND CodPessoa = PessoaTrajeto.CodPessoa ORDER BY Hora desc),';') Detalhes, '0' Fotos, '' CodFil, '0' AS DistanciaKm, '' Cargo \n"
                        + " FROM PessoaTrajeto\n"
                        + " LEFT JOIN Pessoa on Pessoa.Codigo = PessoaTrajeto.CodPessoa\n"
                        + " WHERE DtCompet = ? \n";
                if (!codPessoa.equals("")) {
                    sql += " AND CodPessoa = ? \n";
                }
                sql += " GROUP BY CodPessoa, DtCompet, Nome) \n"
                        + " UNION (Select '10' Tipo, CONCAT(tmktdet.sequencia,';',tmktdet.codfil,';') Chave, CONVERT(Varchar, tmktdet.CodCont) Secao, \n"
                        + "    contatos.Nome Posto, pessoa.nome Funcionario, CONVERT(Varchar,tmktdet.data,112) Data, tmktdet.hora Hora, \n"
                        + "    tmktdet.latitude Latitude, tmktdet.longitude Longitude, tmktdet.historico Titulo, convert(varchar(max),tmktdet.detalhes) \n"
                        + "    Detalhes, tmktdet.Fotos Fotos,\n"
                        + "    tmktdet.codfil, '0' AS DistanciaKm, '' Cargo \n"
                        + "    from tmktdet \n"
                        + "    left join pessoa on pessoa.codigo = tmktdet.codpessoa \n"
                        + "    left join contatos on contatos.codigo = tmktdet.CodCont\n"
                        + "                      and contatos.codfil = tmktdet.codfil\n"
                        + "    WHERE tmktdet.data = ?\n";
                if (!codPessoa.equals("")) {
                    sql += " AND tmktdet.CodPessoa = ? \n";
                }
                sql += ")\n";
            }
            sql += ") a\n";
            if (!tipoRelatorio.equals("")) {
                sql += " WHERE Tipo = ? \n";
            }
            sql += ") AS RowConstrainedResult \n"
                    + " WHERE RowNum >= ? AND RowNum < ? \n";
            sql += " ORDER BY RowNum ";
            consulta = new Consulta(sql, persistencia);

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            // }
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                consulta.setString(matricula);
            }

            if (!hora.equals("")) {
                consulta.setString(hora);
            }
            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            //if (codPessoaAut != null) {
            //     consulta.setBigDecimal(codPessoaAut);
            // }
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }
            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }
            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            consulta.setString(tipoRonda);
            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            // }
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!matricula.equals("")) {
                consulta.setString(matricula);
            }
            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!matricula.equals("")) {
                consulta.setString(matricula);
            }
            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            if (filtroWeb.equals("0") || filtroWeb.equals("1")) {
                consulta.setString(data);
                if (!codFil.equals("")) {
                    consulta.setString(codFil);
                }
                if (!matricula.equals("")) {
                    consulta.setString(matricula);
                }
                if (!secao.equals("")) {
                    consulta.setString(secao);
                }

                consulta.setString(data);
                if (!codPessoa.equals("")) {
                    consulta.setString(codPessoa);
                }

                consulta.setString(data);
                if (!codPessoa.equals("")) {
                    consulta.setString(codPessoa);
                }
            }

            if (!tipoRelatorio.equals("")) {
                consulta.setString(tipoRelatorio);
            }

            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);

            consulta.select();
            LogsSatMobEW logsSatMobEW;
            while (consulta.Proximo()) {
                logsSatMobEW = new LogsSatMobEW();
                logsSatMobEW.setCodfil(consulta.getString("CodFil"));
                logsSatMobEW.setTipo(consulta.getString("Tipo"));
                logsSatMobEW.setChave(consulta.getString("Chave"));
                logsSatMobEW.setPosto(consulta.getString("Posto"));
                logsSatMobEW.setSecao(consulta.getString("Secao"));
                logsSatMobEW.setFuncionario(consulta.getString("Funcionario"));
                logsSatMobEW.setData(consulta.getString("Data"));
                logsSatMobEW.setHora(consulta.getString("Hora"));
                logsSatMobEW.setLatitude(consulta.getString("Latitude"));
                logsSatMobEW.setLongitude(consulta.getString("Longitude"));
                logsSatMobEW.setTitulo(consulta.getString("Titulo"));
                logsSatMobEW.setDetalhes(consulta.getString("Detalhes"));
                logsSatMobEW.setFotos(consulta.getString("Fotos"));
                logsSatMobEW.setDistancia_km(consulta.getString("DistanciaKm"));
                logsSatMobEW.setCargo(consulta.getString("Cargo"));
                retorno.add(logsSatMobEW);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.obterLogs - " + e.getMessage());
        }
    }

    /**
     * Lista todos os logs por data Tipos de relatório: 1 - Batida de Ponto 2 -
     * Relatório 3 - Relatório Supervisor 4 - Resumo Ronda 5 - Check Ins
     * Supervisores 6 - Rondas 7 - Rondas completas 8 - Check Ins Prestadores 9
     * - Resumo Rota Prestador 10 - Relatórios Prestador
     *
     * @param primeiro
     * @param linhas
     * @param data
     * @param codFil
     * @param matricula
     * @param codPessoa
     * @param secao
     * @param tipoRonda
     * @param filtroWeb
     * @param codPessoaAut
     * @param tipoRelatorio
     * @param hora
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<LogsSatMobEW> obterLogsHistoricoMensal(int primeiro, int linhas, String data, String codFil, String matricula,
            String codPessoa, String secao, String tipoRonda, String filtroWeb, BigDecimal codPessoaAut, String tipoRelatorio, String hora,
            Persistencia persistencia) throws Exception {
        try {
            List<LogsSatMobEW> retorno = new ArrayList<>();

            String sql = "DECLARE @DATE DATETIME;\n"
                    + " DECLARE @PrimeiroDia DATE;\n"
                    + " DECLARE @UltimoDia DATE;\n"
                    + " SET @DATE=?;\n"
                    + " SET @PrimeiroDia = (SELECT @DATE-DAY(@DATE)+1);\n"
                    + " SET @UltimoDia = (SELECT EOMONTH(@DATE));";

            sql += " SELECT DISTINCT '1' Tipo, CONCAT(RHPonto.DtCompet,';',convert(Bigint,RHPonto.Matr),';',RHPonto.Batida,';',Funcion.codfil,';') Chave,\n"
                    + " RHPontoDet.secao Secao, \n"
                    + " pstserv.local Posto, Funcion.nome Funcionario, RHPonto.DtCompet Data, RHPonto.Hora Hora, RHPontoGEO.latitude Latitude, \n"
                    + " RHPontoGEO.longitude Longitude, '' Titulo, '' Detalhes, CONVERT (varchar, RHPonto.Batida) Fotos,\n"
                    + " Funcion.codfil, CAST(CAST(dbo.fun_calcdistancia(clientes.Latitude, clientes.Longitude, RHPontoGEO.Latitude, RHPontoGEO.Longitude) AS DECIMAL(18,3)) AS VARCHAR(30)) DistanciaKm, Cargos.descricao Cargo, RHPontoImagem.URL \n"
                    //+ " Funcion.codfil, 0 as DistanciaKm, Cargos.descricao Cargo \n"
                    + " FROM RHPonto \n"
                    + " Left Join Funcion on Funcion.Matr = RHPonto.Matr \n"
                    + " Left Join Cargos ON Funcion.CodCargo = Cargos.Codigo \n"
                    + " Left Join RHPontoGEO on RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "                      and RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "                      and RHPontoGEO.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                      AND RHPontoDet.Batida = RHPonto.Batida \n"
                    + " LEFT join RHPontoImagem ON RHPontoImagem.matr = RHPonto.matr "
                    + "                         AND RHPontoImagem.dtcompet = RHPonto.dtcompet "
                    + "                         AND RHPontoImagem.batida = RHPonto.batida "
                    + " LEFT JOIN pstserv ON pstserv.secao = RHPontoDet.secao \n"
                    + "                   AND pstserv.codfil = Funcion.codfil \n"
                    + " LEFT JOIN clientes ON clientes.codigo = pstserv.CodCli AND clientes.codfil = pstserv.codfil";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }

            sql += " WHERE RHPonto.DtCompet BETWEEN @PrimeiroDia AND @UltimoDia And RHPonto.Batida < 9000 \n";

            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("")) {
                sql += " and RHPonto.matr = ? \n";
            }
            if (!hora.equals("")) {
                sql += " and RHPonto.hora < ? \n";
            }
            if (!secao.equals("")) {
                sql += " and RHPontoDet.Secao = ? \n";
            }
            sql += " ORDER BY Data DESC, Hora DESC";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(data);

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!matricula.equals("")) {
                consulta.setString(matricula);
            }

            if (!hora.equals("")) {
                consulta.setString(hora);
            }
            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            consulta.select();
            LogsSatMobEW logsSatMobEW;
            while (consulta.Proximo()) {
                logsSatMobEW = new LogsSatMobEW();
                logsSatMobEW.setCodfil(consulta.getString("CodFil"));
                logsSatMobEW.setTipo(consulta.getString("Tipo"));
                logsSatMobEW.setChave(consulta.getString("Chave"));
                logsSatMobEW.setPosto(consulta.getString("Posto"));
                logsSatMobEW.setSecao(consulta.getString("Secao"));
                logsSatMobEW.setFuncionario(consulta.getString("Funcionario"));
                logsSatMobEW.setData(consulta.getString("Data"));
                logsSatMobEW.setHora(consulta.getString("Hora"));
                logsSatMobEW.setLatitude(consulta.getString("Latitude"));
                logsSatMobEW.setLongitude(consulta.getString("Longitude"));
                logsSatMobEW.setTitulo(consulta.getString("Titulo"));
                logsSatMobEW.setDetalhes(consulta.getString("Detalhes"));
                logsSatMobEW.setFotos(consulta.getString("Fotos"));
                logsSatMobEW.setDistancia_km(consulta.getString("DistanciaKm"));
                logsSatMobEW.setCargo(consulta.getString("Cargo"));
                logsSatMobEW.setURL(consulta.getString("URL"));
                retorno.add(logsSatMobEW);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.obterLogs - " + e.getMessage());
        }
    }

    /**
     * Lista todos os logs por data Tipos de relatório: 1 - Batida de Ponto 2 -
     * Relatório 3 - Relatório Supervisor 4 - Resumo Ronda 5 - Check Ins
     * Supervisores 6 - Rondas 7 - Rondas completas 8 - Check Ins Prestadores 9
     * - Resumo Rota Prestador 10 - Relatórios Prestador
     *
     * @param primeiro
     * @param linhas
     * @param data
     * @param codFil
     * @param matricula
     * @param codPessoa
     * @param secao
     * @param tipoRonda
     * @param filtroWeb
     * @param codPessoaAut
     * @param tipoRelatorio
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<LogsSatMobEW> obterLogsResumoQde(int primeiro, int linhas, String data, String codFil, String matricula,
            String codPessoa, String secao, String tipoRonda, String filtroWeb, BigDecimal codPessoaAut, String tipoRelatorio,
            Persistencia persistencia) throws Exception {
        try {
            List<LogsSatMobEW> retorno = new ArrayList<>();
            String sql = " SELECT RowConstrainedResult.* FROM ( SELECT *, ROW_NUMBER() OVER ( ORDER BY Data DESC, Hora DESC ) RowNum \n"
                    + " From (\n"
                    + "(SELECT '1' Tipo, CONCAT(RHPonto.DtCompet,';',convert(Bigint,RHPonto.Matr),';',RHPonto.Batida,';',Funcion.codfil,';') Chave,\n"
                    + " RHPontoDet.secao Secao, \n"
                    + " pstserv.local Posto, Funcion.nome Funcionario, RHPonto.DtBatida Data, RHPonto.Hora Hora, RHPontoGEO.latitude Latitude, \n"
                    + " RHPontoGEO.longitude Longitude, '' Titulo, '' Detalhes, CONVERT (varchar, RHPonto.Batida) Fotos,\n"
                    + " Funcion.codfil, CAST(CAST(dbo.fun_calcdistancia(clientes.Latitude, clientes.Longitude, RHPontoGEO.Latitude, RHPontoGEO.Longitude) AS DECIMAL(18,3)) AS VARCHAR(30)) DistanciaKm \n"
                    //+ " Funcion.codfil, 0 as DistanciaKm \n"
                    + " FROM RHPonto \n"
                    + " JOIN(SELECT\n"
                    + "      MAX(UltimoReg.Hora) Hora,\n"
                    + "      UltimoReg.Matr,\n"
                    + "      UltimoReg.DtCompet\n"
                    + "      FROM RHponto AS UltimoReg\n"
                    + "      WHERE UltimoReg.DtCompet = ?\n"
                    + "      GROUP BY UltimoReg.Matr, UltimoReg.DtCompet) AS RHPontoUltReg"
                    + "   ON RHPonto.DtCompet = RHPontoUltReg.DtCompet"
                    + "  AND RHPonto.Matr = RHPontoUltReg.Matr"
                    + "  AND RHPonto.Hora = RHPontoUltReg.Hora"
                    + " Left Join Funcion on Funcion.Matr = RHPonto.Matr \n"
                    + " Left Join RHPontoGEO on RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "                      and RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "                      and RHPontoGEO.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                      AND RHPontoDet.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN pstserv ON pstserv.secao = RHPontoDet.secao \n"
                    + "                   AND pstserv.codfil = Funcion.codfil \n"
                    + " LEFT JOIN clientes ON clientes.codigo = pstserv.CodCli AND clientes.codfil = pstserv.codfil";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE RHPonto.DtCompet = ? And RHPonto.Batida < 9000 \n";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and RHPonto.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and RHPonto.matr " + matricula + "\n";
            }

            if (!secao.equals("")) {
                sql += " and RHPontoDet.Secao = ? \n";
            }
            sql += ") Union ( \n"
                    + " Select '2' Tipo, CONCAT(tmktdetpst.sequencia,';',tmktdetpst.codfil,';') Chave, pstserv.secao Secao, pstserv.local Posto, \n"
                    + " pessoa.nome Funcionario, tmktdetpst.data Data, tmktdetpst.hora Hora,  tmktdetpst.latitude Latitude, tmktdetpst.longitude Longitude, \n"
                    + " tmktdetpst.historico Titulo, convert(varchar(max),tmktdetpst.detalhes) Detalhes, tmktdetpst.Fotos Fotos,\n"
                    + " tmktdetpst.codfil, '0' AS DistanciaKm  \n"
                    + " from tmktdetpst \n"
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia \n"
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao \n"
                    + "                    and pstserv.codfil = tmktdetpst.codfil \n"
                    + " left join pessoa on pessoa.codigo = tmktdetpst.codpessoa \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE tmktdetpst.data = ? \n";
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and pessoa.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and pessoa.matr " + matricula + "\n";
            }
            if (filtroWeb.equals("1")) {
                sql += " and tmktdetpst.filtroweb = 1 \n";
            }
            if (!codFil.equals("")) {
                sql += " and tmktdetpst.codfil = ? \n";
            }
            if (!codPessoa.equals("")) {
                sql += " and tmktdetpst.codpessoa = ? \n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.secao = ? \n";
            }
            sql += " and tmktdetpst.tipocont = 'R') UNION  \n"
                    + " (Select '3' Tipo, CONCAT(tmktdetpst.sequencia,';',tmktdetpst.codfil,';') Chave, pstserv.secao Secao, pstserv.local Posto, \n"
                    + " pessoa.nome Funcionario, tmktdetpst.data Data, tmktdetpst.hora Hora,  tmktdetpst.latitude Latitude, tmktdetpst.longitude Longitude, \n"
                    + " tmktdetpst.historico Titulo, convert(varchar(max),tmktdetpst.detalhes) Detalhes, tmktdetpst.Fotos Fotos,\n"
                    + " tmktdetpst.codfil, '0' AS DistanciaKm \n"
                    + " from tmktdetpst \n"
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia \n"
                    + " inner join pstserv on pstserv.secao = tmktdetpst.secao \n"
                    + "                    and pstserv.codfil = tmktdetpst.codfil \n"
                    + " left join pessoa on pessoa.codigo = tmktdetpst.codpessoa \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE tmktdetpst.data = ? \n";
            if (filtroWeb.equals("1")) {
                sql += " and tmktdetpst.filtroweb = 1 \n";
            }
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!codPessoa.equals("")) {
                sql += " and tmktdetpst.codpessoa = ? \n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.secao = ? \n";
            }
            sql += " and tmktdetpst.tipocont = 'RS') UNION (\n"
                    + " select '4' Tipo, CONCAT(rondas.secao,';',CONVERT(varchar,rondas.data,112),';',rondas.hora,';',rondas.codfil,';') Chave, \n"
                    + " rondas.secao Secao, \n"
                    + " pstserv.local Posto, funcion.nome Funcionario, rondas.data Data, max(rondas.hr_alter) Hora, '' Latitude, '' Longitude,'' Titulo, \n"
                    + " CONCAT(min(rondas.hr_alter),';',max(rondas.hr_alter),';',count(rondas.hora),';',\n"
                    + "     (select count(*) from pstdepenronda \n"
                    + "         where pstdepenronda.hora = rondas.hora\n"
                    + "               and pstdepenronda.secao = rondas.secao \n"
                    + "               and pstdepenronda.codfil = rondas.codfil\n"
                    + "               and pstdepenronda.tipo = ?),';') Detalhes, '0' Fotos, \n"
                    + " rondas.codfil, '0' AS DistanciaKm \n"
                    + " from rondas \n"
                    + " left join funcion on funcion.matr = rondas.matr \n"
                    + "                   and funcion.codfil = rondas.codfil \n"
                    + " left join pstserv on pstserv.secao = rondas.secao \n"
                    + "                   and pstserv.codfil = rondas.codfil \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE rondas.Data = ? \n";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and rondas.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and rondas.matr " + matricula + "\n";
            }
            if (!secao.equals("")) {
                sql += " and rondas.secao = ? \n";
            }
            sql += " group by rondas.hora, rondas.matr, rondas.secao, rondas.codfil, rondas.data, pstserv.local, funcion.nome )\n"
                    + " UNION (SELECT '5' Tipo, CONCAT(RHPonto.DtCompet,';',convert(Bigint,RHPonto.Matr),';',RHPonto.Batida,';',Funcion.codfil,';') Chave,\n"
                    + " RHPontoDet.secao Secao, \n"
                    + " pstserv.local Posto, Funcion.nome Funcionario, RHPonto.DtBatida Data, RHPonto.Hora Hora, RHPontoGEO.latitude Latitude, \n"
                    + " RHPontoGEO.longitude Longitude, '' Titulo, '' Detalhes, CONVERT (varchar, RHPonto.Batida) Fotos,\n"
                    + " Funcion.codfil, '0' AS DistanciaKm \n"
                    + " FROM RHPonto \n"
                    + " Left Join Funcion on Funcion.Matr = RHPonto.Matr \n"
                    + " Left Join RHPontoGEO on RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "                      and RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "                      and RHPontoGEO.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                      AND RHPontoDet.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN pstserv ON pstserv.secao = RHPontoDet.secao \n"
                    + "                   AND pstserv.codfil = Funcion.codfil \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE RHPonto.DtCompet = ? And RHPonto.Batida > 9000 \n";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and RHPonto.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and RHPonto.matr " + matricula + "\n";
            }
            if (!secao.equals("")) {
                sql += " and RHPontoDet.Secao = ? \n";
            }
            sql += ") \n";
            if (filtroWeb.equals("0") || filtroWeb.equals("1")) {
                sql += " UNION (\n"
                        + " select '7' Tipo, CONCAT(Convert(Varchar, PstInspecao.Codigo), ';',convert(Bigint,PstInspecao.Matr)) Chave, \n"
                        + " PstInspecao.Secao Secao, \n"
                        + " pstserv.local Posto, funcion.nome Funcionario, Convert(VarChar,PstInspecao.Data, 112) Data, max(PstInspecao.hr_alter) Hora, \n"
                        + " PstInspecao.Latitude Latitude, PstInspecao.Longitude Longitude,\n"
                        + " Inspecoes.Descricao Titulo, Pessoa.Nome Detalhes, (SELECT TOP 1 X.CaminhoImagem FROM pstinspecao AS X WHERE X.Matr = PstInspecao.Matr AND X.Secao = PstInspecao.Secao AND X.CodFil = PstInspecao.CodFil AND X.Data =PstInspecao.Data AND X.Codigo =PstInspecao.Codigo AND X.CaminhoImagem IS NOT NULL AND X.CaminhoImagem <> '') Fotos, \n"
                        + " PstInspecao.codfil, '0' AS DistanciaKm \n"
                        + " FROM PstInspecao \n"
                        + " LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao \n"
                        + "                 AND PstServ.Codfil = PstInspecao.CodFil \n"
                        + " LEFT JOIN Funcion ON Funcion.Matr = PstInspecao.Matr \n"
                        + "                AND Funcion.Codfil = PstInspecao.Codfil \n"
                        + " Left join Pessoa on Pessoa.Codigo = PstInspecao.CodOperador \n"
                        + " LEFT JOIN Inspecoes ON Inspecoes.Codigo = PstInspecao.CodInspecao \n";
                if (codPessoaAut != null) {
                    sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                            + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                            + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                            + "                        and pessoacliaut.flag_excl <> '*' \n";
                }

                sql += " WHERE PstInspecao.Data = ? \n";
                if (!codFil.equals("")) {
                    sql += " AND PstInspecao.CodFil = ? \n";
                }
                if (!matricula.equals("") && !matricula.contains("IN")) {
                    sql += " and PstInspecao.matr = " + matricula + " \n";
                } else if (matricula.contains("IN")) {
                    sql += " and PstInspecao.matr " + matricula + "\n";
                }

                if (!secao.equals("")) {
                    sql += " AND PstInspecao.Secao = ? \n";
                }
                if (codPessoaAut != null) {
                    sql += " and pessoacliaut,codigo is not null ";
                }
                sql += " GROUP BY PstInspecao.Codigo, Pessoa.Nome, Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, PstInspecao.Data, PstInspecao.Matr, \n"
                        + " PstInspecao.CodInspecao, PstServ.Local, Funcion.Nome, PstInspecao.Latitude, PstInspecao.Longitude ) \n"
                        + " UNION ( SELECT '9' Tipo, CONCAT(PessoaTrajeto.CodPessoa,';',CONVERT(varchar,PessoaTrajeto.DtCompet,112),';') Chave, '' Secao,\n"
                        + " '' Posto, Pessoa.Nome Funcionario, CONVERT(Varchar,PessoaTrajeto.DtCompet,112) Data, \n"
                        + " (SELECT TOP 1 (SELECT MAX(h) FROM (VALUES(HrCheg), (HrSaida)) AS VALUE(h)) Hora\n"
                        + "    FROM PessoaTrajeto p WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                            AND CodPessoa = PessoaTrajeto.CodPessoa ORDER BY Hora desc) Hora, '' Latitude, '' Longitude,\n"
                        + " '' Titulo,\n"
                        + " CONCAT(min(PessoaTrajeto.HrCheg),';',"
                        + "(SELECT TOP 1 (SELECT MAX(h) FROM (VALUES(HrCheg), (HrSaida)) AS VALUE(h)) Hora\n"
                        + "    FROM PessoaTrajeto p WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                            AND CodPessoa = PessoaTrajeto.CodPessoa ORDER BY Hora desc),';') Detalhes, '0' Fotos, '' CodFil, '0' AS DistanciaKm \n"
                        + " FROM PessoaTrajeto\n"
                        + " LEFT JOIN Pessoa on Pessoa.Codigo = PessoaTrajeto.CodPessoa\n"
                        + " WHERE DtCompet = ? \n";
                if (!codPessoa.equals("")) {
                    sql += " AND CodPessoa = ? \n";
                }
                sql += " GROUP BY CodPessoa, DtCompet, Nome) \n"
                        + " UNION (Select '10' Tipo, CONCAT(tmktdet.sequencia,';',tmktdet.codfil,';') Chave, CONVERT(Varchar, tmktdet.CodCont) Secao, \n"
                        + "    contatos.Nome Posto, pessoa.nome Funcionario, CONVERT(Varchar,tmktdet.data,112) Data, tmktdet.hora Hora, \n"
                        + "    tmktdet.latitude Latitude, tmktdet.longitude Longitude, tmktdet.historico Titulo, convert(varchar(max),tmktdet.detalhes) \n"
                        + "    Detalhes, tmktdet.Fotos Fotos,\n"
                        + "    tmktdet.codfil, '0' AS DistanciaKm \n"
                        + "    from tmktdet \n"
                        + "    left join pessoa on pessoa.codigo = tmktdet.codpessoa \n"
                        + "    left join contatos on contatos.codigo = tmktdet.CodCont\n"
                        + "                      and contatos.codfil = tmktdet.codfil\n"
                        + "    WHERE tmktdet.data = ?\n";
                if (!codPessoa.equals("")) {
                    sql += " AND tmktdet.CodPessoa = ? \n";
                }
                sql += ")\n";
            }
            sql += ") a\n";
            if (!tipoRelatorio.equals("")) {
                sql += " WHERE Tipo = ? \n";
            }
            sql += ") AS RowConstrainedResult \n"
                    + " WHERE RowNum >= 0 \n";
            sql += " ORDER BY RowNum ";
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(data);

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            // }
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }
            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            //if (codPessoaAut != null) {
            //     consulta.setBigDecimal(codPessoaAut);
            // }
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }
            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            consulta.setString(tipoRonda);
            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            // }
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            if (filtroWeb.equals("0") || filtroWeb.equals("1")) {
                consulta.setString(data);
                if (!codFil.equals("")) {
                    consulta.setString(codFil);
                }

                if (!secao.equals("")) {
                    consulta.setString(secao);
                }

                consulta.setString(data);
                if (!codPessoa.equals("")) {
                    consulta.setString(codPessoa);
                }

                consulta.setString(data);
                if (!codPessoa.equals("")) {
                    consulta.setString(codPessoa);
                }
            }

            if (!tipoRelatorio.equals("")) {
                consulta.setString(tipoRelatorio);
            }

            consulta.select();
            LogsSatMobEW logsSatMobEW;
            while (consulta.Proximo()) {
                logsSatMobEW = new LogsSatMobEW();
                logsSatMobEW.setCodfil(consulta.getString("CodFil"));
                logsSatMobEW.setTipo(consulta.getString("Tipo"));
                logsSatMobEW.setChave(consulta.getString("Chave"));
                logsSatMobEW.setPosto(consulta.getString("Posto"));
                logsSatMobEW.setSecao(consulta.getString("Secao"));
                logsSatMobEW.setFuncionario(consulta.getString("Funcionario"));
                logsSatMobEW.setData(consulta.getString("Data"));
                logsSatMobEW.setHora(consulta.getString("Hora"));
                logsSatMobEW.setLatitude(consulta.getString("Latitude"));
                logsSatMobEW.setLongitude(consulta.getString("Longitude"));
                logsSatMobEW.setTitulo(consulta.getString("Titulo"));
                logsSatMobEW.setDetalhes(consulta.getString("Detalhes"));
                logsSatMobEW.setFotos(consulta.getString("Fotos"));
                logsSatMobEW.setDistancia_km(consulta.getString("DistanciaKm"));
                retorno.add(logsSatMobEW);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.obterLogs - " + e.getMessage());
        }
    }

    public List<LogsSatMobEW> obterLogsResumoQde(int primeiro, int linhas, String data1, String data2, String codFil, String matricula,
            String codPessoa, String secao, String tipoRonda, String filtroWeb, BigDecimal codPessoaAut, String tipoRelatorio,
            Set<String> resumoCategoria,
            Persistencia persistencia) throws Exception {
        try {
            List<LogsSatMobEW> retorno = new ArrayList<>();
            String sql = " SELECT RowConstrainedResult.* FROM ( SELECT *, ROW_NUMBER() OVER ( ORDER BY Data DESC, Hora DESC ) RowNum \n"
                    + " From (\n"
                    + "(SELECT '1' Tipo, CONCAT(RHPonto.DtCompet,';',convert(Bigint,RHPonto.Matr),';',RHPonto.Batida,';',Funcion.codfil,';') Chave,\n"
                    + " RHPontoDet.secao Secao, \n"
                    + " pstserv.local Posto, Funcion.nome Funcionario, RHPonto.DtBatida Data, RHPonto.Hora Hora, RHPontoGEO.latitude Latitude, \n"
                    + " RHPontoGEO.longitude Longitude, '' Titulo, '' Detalhes, CONVERT (varchar, RHPonto.Batida) Fotos,\n"
                    + " Funcion.codfil, CAST(CAST(dbo.fun_calcdistancia(clientes.Latitude, clientes.Longitude, RHPontoGEO.Latitude, RHPontoGEO.Longitude) AS DECIMAL(18,3)) AS VARCHAR(30)) DistanciaKm \n"
                    //+ " Funcion.codfil, 0 as DistanciaKm \n"
                    + " FROM RHPonto \n"
                    + " JOIN(SELECT\n"
                    + "      MAX(UltimoReg.Hora) Hora,\n"
                    + "      UltimoReg.Matr,\n"
                    + "      UltimoReg.DtCompet\n"
                    + "      FROM RHponto AS UltimoReg\n"
                    + "      WHERE UltimoReg.DtCompet  BETWEEN '" + data1 + "' AND '" + data2 + "'\n"
                    + "      GROUP BY UltimoReg.Matr, UltimoReg.DtCompet) AS RHPontoUltReg"
                    + "   ON RHPonto.DtCompet = RHPontoUltReg.DtCompet"
                    + "  AND RHPonto.Matr = RHPontoUltReg.Matr"
                    + "  AND RHPonto.Hora = RHPontoUltReg.Hora"
                    + " Left Join Funcion on Funcion.Matr = RHPonto.Matr \n"
                    + " Left Join RHPontoGEO on RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "                      and RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "                      and RHPontoGEO.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                      AND RHPontoDet.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN pstserv ON pstserv.secao = RHPontoDet.secao \n"
                    + "                   AND pstserv.codfil = Funcion.codfil \n"
                    + " LEFT JOIN clientes ON clientes.codigo = pstserv.CodCli AND clientes.codfil = pstserv.codfil";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE RHPonto.DtCompet BETWEEN '" + data1 + "' AND '" + data2 + "' And RHPonto.Batida < 9000 \n";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and RHPonto.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and RHPonto.matr " + matricula + "\n";
            }

            if (!secao.equals("")) {
                sql += " and RHPontoDet.Secao = ? \n";
            }
            sql += ") Union ( \n"
                    + " Select '2' Tipo, CONCAT(tmktdetpst.sequencia,';',tmktdetpst.codfil,';') Chave, pstserv.secao Secao, pstserv.local Posto, \n"
                    + " pessoa.nome Funcionario, tmktdetpst.data Data, tmktdetpst.hora Hora,  tmktdetpst.latitude Latitude, tmktdetpst.longitude Longitude, \n"
                    + " tmktdetpst.historico Titulo, convert(varchar(max),tmktdetpst.detalhes) Detalhes, tmktdetpst.Fotos Fotos,\n"
                    + " tmktdetpst.codfil, '0' AS DistanciaKm  \n"
                    + " from tmktdetpst \n"
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia \n"
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao \n"
                    + "                    and pstserv.codfil = tmktdetpst.codfil \n"
                    + " left join pessoa on pessoa.codigo = tmktdetpst.codpessoa \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE tmktdetpst.data BETWEEN '" + data1 + "' AND '" + data2 + "' \n";
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and pessoa.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and pessoa.matr " + matricula + "\n";
            }
            if (filtroWeb.equals("1")) {
                sql += " and tmktdetpst.filtroweb = 1 \n";
            }
            if (!codFil.equals("")) {
                sql += " and tmktdetpst.codfil = ? \n";
            }
            if (!codPessoa.equals("")) {
                sql += " and tmktdetpst.codpessoa = ? \n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.secao = ? \n";
            }
            sql += " and tmktdetpst.tipocont = 'R') UNION  \n"
                    + " (Select '3' Tipo, CONCAT(tmktdetpst.sequencia,';',tmktdetpst.codfil,';') Chave, pstserv.secao Secao, pstserv.local Posto, \n"
                    + " pessoa.nome Funcionario, tmktdetpst.data Data, tmktdetpst.hora Hora,  tmktdetpst.latitude Latitude, tmktdetpst.longitude Longitude, \n"
                    + " tmktdetpst.historico Titulo, convert(varchar(max),tmktdetpst.detalhes) Detalhes, tmktdetpst.Fotos Fotos,\n"
                    + " tmktdetpst.codfil, '0' AS DistanciaKm \n"
                    + " from tmktdetpst \n"
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia \n"
                    + " inner join pstserv on pstserv.secao = tmktdetpst.secao \n"
                    + "                    and pstserv.codfil = tmktdetpst.codfil \n"
                    + " left join pessoa on pessoa.codigo = tmktdetpst.codpessoa \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE tmktdetpst.data BETWEEN '" + data1 + "' AND '" + data2 + "' \n";
            if (filtroWeb.equals("1")) {
                sql += " and tmktdetpst.filtroweb = 1 \n";
            }
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!codPessoa.equals("")) {
                sql += " and tmktdetpst.codpessoa = ? \n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.secao = ? \n";
            }
            sql += " and tmktdetpst.tipocont = 'RS') UNION (\n"
                    + " select '4' Tipo, CONCAT(rondas.secao,';',CONVERT(varchar,rondas.data,112),';',rondas.hora,';',rondas.codfil,';') Chave, \n"
                    + " rondas.secao Secao, \n"
                    + " pstserv.local Posto, funcion.nome Funcionario, rondas.data Data, max(rondas.hr_alter) Hora, '' Latitude, '' Longitude,'' Titulo, \n"
                    + " CONCAT(min(rondas.hr_alter),';',max(rondas.hr_alter),';',count(rondas.hora),';',\n"
                    + "     (select count(*) from pstdepenronda \n"
                    + "         where pstdepenronda.hora = rondas.hora\n"
                    + "               and pstdepenronda.secao = rondas.secao \n"
                    + "               and pstdepenronda.codfil = rondas.codfil\n"
                    + "               and pstdepenronda.tipo = ?),';') Detalhes, '0' Fotos, \n"
                    + " rondas.codfil, '0' AS DistanciaKm \n"
                    + " from rondas \n"
                    + " left join funcion on funcion.matr = rondas.matr \n"
                    + "                   and funcion.codfil = rondas.codfil \n"
                    + " left join pstserv on pstserv.secao = rondas.secao \n"
                    + "                   and pstserv.codfil = rondas.codfil \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE rondas.Data BETWEEN '" + data1 + "' AND '" + data2 + "' \n";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and rondas.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and rondas.matr " + matricula + "\n";
            }
            if (!secao.equals("")) {
                sql += " and rondas.secao = ? \n";
            }
            sql += " group by rondas.hora, rondas.matr, rondas.secao, rondas.codfil, rondas.data, pstserv.local, funcion.nome )\n"
                    + " UNION (SELECT '5' Tipo, CONCAT(RHPonto.DtCompet,';',convert(Bigint,RHPonto.Matr),';',RHPonto.Batida,';',Funcion.codfil,';') Chave,\n"
                    + " RHPontoDet.secao Secao, \n"
                    + " pstserv.local Posto, Funcion.nome Funcionario, RHPonto.DtBatida Data, RHPonto.Hora Hora, RHPontoGEO.latitude Latitude, \n"
                    + " RHPontoGEO.longitude Longitude, '' Titulo, '' Detalhes, CONVERT (varchar, RHPonto.Batida) Fotos,\n"
                    + " Funcion.codfil, '0' AS DistanciaKm \n"
                    + " FROM RHPonto \n"
                    + " Left Join Funcion on Funcion.Matr = RHPonto.Matr \n"
                    + " Left Join RHPontoGEO on RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "                      and RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "                      and RHPontoGEO.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                      AND RHPontoDet.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN pstserv ON pstserv.secao = RHPontoDet.secao \n"
                    + "                   AND pstserv.codfil = Funcion.codfil \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE RHPonto.DtCompet  BETWEEN '" + data1 + "' AND '" + data2 + "' And RHPonto.Batida > 9000 \n";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and RHPonto.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and RHPonto.matr " + matricula + "\n";
            }

            if (!secao.equals("")) {
                sql += " and RHPontoDet.Secao = ? \n";
            }
            sql += ") \n";
            if (filtroWeb.equals("0") || filtroWeb.equals("1")) {
                sql += " UNION (\n"
                        + " select '7' Tipo, CONCAT(Convert(Varchar, PstInspecao.Codigo), ';',convert(Bigint,PstInspecao.Matr)) Chave, \n"
                        + " PstInspecao.Secao Secao, \n"
                        + " pstserv.local Posto, funcion.nome Funcionario, Convert(VarChar,PstInspecao.Data, 112) Data, max(PstInspecao.hr_alter) Hora, \n"
                        + " PstInspecao.Latitude Latitude, PstInspecao.Longitude Longitude,\n"
                        + " Inspecoes.Descricao Titulo, Pessoa.Nome Detalhes, (SELECT TOP 1 X.CaminhoImagem FROM pstinspecao AS X WHERE X.Matr = PstInspecao.Matr AND X.Secao = PstInspecao.Secao AND X.CodFil = PstInspecao.CodFil AND X.Data =PstInspecao.Data AND X.Codigo =PstInspecao.Codigo AND X.CaminhoImagem IS NOT NULL AND X.CaminhoImagem <> '') Fotos, \n"
                        + " PstInspecao.codfil, '0' AS DistanciaKm \n"
                        + " FROM PstInspecao \n"
                        + " LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao \n"
                        + "                 AND PstServ.Codfil = PstInspecao.CodFil \n"
                        + " LEFT JOIN Funcion ON Funcion.Matr = PstInspecao.Matr \n"
                        + "                AND Funcion.Codfil = PstInspecao.Codfil \n"
                        + " Left join Pessoa on Pessoa.Codigo = PstInspecao.CodOperador \n"
                        + " LEFT JOIN Inspecoes ON Inspecoes.Codigo = PstInspecao.CodInspecao \n";
                if (codPessoaAut != null) {
                    sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                            + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                            + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                            + "                        and pessoacliaut.flag_excl <> '*' \n";
                }

                sql +=  " WHERE PstInspecao.Data BETWEEN '" + data1 + "' AND '" + data2 + "' \n";
                if (!codFil.equals("")) {
                    sql += " AND PstInspecao.CodFil = ? \n";
                }
                if (!matricula.equals("") && !matricula.contains("IN")) {
                    sql += " and PstInspecao.matr = " + matricula + " \n";
                } else if (matricula.contains("IN")) {
                    sql += " and PstInspecao.matr " + matricula + "\n";
                }
                if (!secao.equals("")) {
                    sql += " AND PstInspecao.Secao = ? \n";
                }
                if (codPessoaAut != null) {
                    sql += " and pessoacliaut.codigo is not null ";
                }
                sql += " GROUP BY PstInspecao.Codigo, Pessoa.Nome, Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, PstInspecao.Data, PstInspecao.Matr, \n"
                        + " PstInspecao.CodInspecao, PstServ.Local, Funcion.Nome, PstInspecao.Latitude, PstInspecao.Longitude ) \n"
                        + " UNION ( SELECT '9' Tipo, CONCAT(PessoaTrajeto.CodPessoa,';',CONVERT(varchar,PessoaTrajeto.DtCompet,112),';') Chave, '' Secao,\n"
                        + " '' Posto, Pessoa.Nome Funcionario, CONVERT(Varchar,PessoaTrajeto.DtCompet,112) Data, \n"
                        + " (SELECT TOP 1 (SELECT MAX(h) FROM (VALUES(HrCheg), (HrSaida)) AS VALUE(h)) Hora\n"
                        + "    FROM PessoaTrajeto p WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                            AND CodPessoa = PessoaTrajeto.CodPessoa ORDER BY Hora desc) Hora, '' Latitude, '' Longitude,\n"
                        + " '' Titulo,\n"
                        + " CONCAT(min(PessoaTrajeto.HrCheg),';',"
                        + "(SELECT TOP 1 (SELECT MAX(h) FROM (VALUES(HrCheg), (HrSaida)) AS VALUE(h)) Hora\n"
                        + "    FROM PessoaTrajeto p WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                            AND CodPessoa = PessoaTrajeto.CodPessoa ORDER BY Hora desc),';') Detalhes, '0' Fotos, '' CodFil, '0' AS DistanciaKm \n"
                        + " FROM PessoaTrajeto\n"
                        + " LEFT JOIN Pessoa on Pessoa.Codigo = PessoaTrajeto.CodPessoa\n"
                        + " WHERE DtCompet BETWEEN '" + data1 + "' AND '" + data2 + "' \n";
                if (!codPessoa.equals("")) {
                    sql += " AND CodPessoa = ? \n";
                }
                sql += " GROUP BY CodPessoa, DtCompet, Nome) \n"
                        + " UNION (Select '10' Tipo, CONCAT(tmktdet.sequencia,';',tmktdet.codfil,';') Chave, CONVERT(Varchar, tmktdet.CodCont) Secao, \n"
                        + "    contatos.Nome Posto, pessoa.nome Funcionario, CONVERT(Varchar,tmktdet.data,112) Data, tmktdet.hora Hora, \n"
                        + "    tmktdet.latitude Latitude, tmktdet.longitude Longitude, tmktdet.historico Titulo, convert(varchar(max),tmktdet.detalhes) \n"
                        + "    Detalhes, tmktdet.Fotos Fotos,\n"
                        + "    tmktdet.codfil, '0' AS DistanciaKm \n"
                        + "    from tmktdet \n"
                        + "    left join pessoa on pessoa.codigo = tmktdet.codpessoa \n"
                        + "    left join contatos on contatos.codigo = tmktdet.CodCont\n"
                        + "                      and contatos.codfil = tmktdet.codfil\n"
                        + "    WHERE tmktdet.data BETWEEN '" + data1 + "' AND '" + data2 + "'\n";
                if (!codPessoa.equals("")) {
                    sql += " AND tmktdet.CodPessoa = ? \n";
                }
                sql += ")\n";
            }
            sql += ") a\n";
            if (!tipoRelatorio.equals("")) {
                sql += " WHERE Tipo = ? \n";
            }
            sql += ") AS RowConstrainedResult \n"
                    + " WHERE RowNum >= 0 \n";
            sql += " ORDER BY RowNum ";
            Consulta consulta = new Consulta(sql, persistencia);

            //consulta.setString(data);
            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            // }
            //consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            // }
            //consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }
            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            //consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }
            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            consulta.setString(tipoRonda);
            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            //consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            //consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            if (filtroWeb.equals("0") || filtroWeb.equals("1")) {
                //consulta.setString(data);
                if (!codFil.equals("")) {
                    consulta.setString(codFil);
                }

                if (!secao.equals("")) {
                    consulta.setString(secao);
                }

                //consulta.setString(data);
                if (!codPessoa.equals("")) {
                    consulta.setString(codPessoa);
                }

                //consulta.setString(data);
                if (!codPessoa.equals("")) {
                    consulta.setString(codPessoa);
                }
            }

            if (!tipoRelatorio.equals("")) {
                consulta.setString(tipoRelatorio);
            }

            consulta.select();
            LogsSatMobEW logsSatMobEW;
            while (consulta.Proximo()) {
                if (resumoCategoria.contains(consulta.getString("Tipo"))) {
                    logsSatMobEW = new LogsSatMobEW();
                    logsSatMobEW.setCodfil(consulta.getString("CodFil"));
                    logsSatMobEW.setTipo(consulta.getString("Tipo"));
                    logsSatMobEW.setChave(consulta.getString("Chave"));
                    logsSatMobEW.setPosto(consulta.getString("Posto"));
                    logsSatMobEW.setSecao(consulta.getString("Secao"));
                    logsSatMobEW.setFuncionario(consulta.getString("Funcionario"));
                    logsSatMobEW.setData(consulta.getString("Data"));
                    logsSatMobEW.setHora(consulta.getString("Hora"));
                    logsSatMobEW.setLatitude(consulta.getString("Latitude"));
                    logsSatMobEW.setLongitude(consulta.getString("Longitude"));
                    logsSatMobEW.setTitulo(consulta.getString("Titulo"));
                    logsSatMobEW.setDetalhes(consulta.getString("Detalhes"));
                    logsSatMobEW.setFotos(consulta.getString("Fotos"));
                    logsSatMobEW.setDistancia_km(consulta.getString("DistanciaKm"));
                    retorno.add(logsSatMobEW);                    
                }
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.obterLogs - " + e.getMessage());
        }
    }

    /**
     * Calcula a quantidade total de relatórios dado os filtros
     *
     * @param data
     * @param codFil
     * @param matricula
     * @param codPessoa
     * @param secao
     * @param tipoRonda
     * @param filtroWeb
     * @param codPessoaAut
     * @param tipoRelatorio
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Integer totalLogs(String data, String codFil, String matricula,
            String codPessoa, String secao, String tipoRonda, String filtroWeb, BigDecimal codPessoaAut, String tipoRelatorio,
            Persistencia persistencia) throws Exception {
        try {
            int retorno = 0;
            String sql = " SELECT COUNT(*) Total FROM \n"
                    + " (SELECT RowConstrainedResult.* FROM ( SELECT *, ROW_NUMBER() OVER ( ORDER BY Data DESC, Hora DESC ) RowNum \n"
                    + " From (\n"
                    + "(SELECT '1' Tipo, CONCAT(RHPonto.DtCompet,';',convert(Bigint,RHPonto.Matr),';',RHPonto.Batida,';',Funcion.codfil,';') Chave,\n"
                    + " RHPontoDet.secao Secao, \n"
                    + " pstserv.local Posto, Funcion.nome Funcionario, RHPonto.DtCompet Data, RHPonto.Hora Hora, RHPontoGEO.latitude Latitude, \n"
                    + " RHPontoGEO.longitude Longitude, '' Titulo, '' Detalhes, CONVERT (varchar, RHPonto.Batida) Fotos,\n"
                    + " Funcion.codfil, CAST(CAST(dbo.fun_calcdistancia(clientes.Latitude, clientes.Longitude, RHPontoGEO.Latitude, RHPontoGEO.Longitude) AS DECIMAL(18,3)) AS VARCHAR(30)) DistanciaKm \n"
                    //+ " Funcion.codfil, 0 as DistanciaKm \n"
                    + " FROM RHPonto \n"
                    + " Left Join Funcion on Funcion.Matr = RHPonto.Matr \n"
                    + " Left Join RHPontoGEO on RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "                      and RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "                      and RHPontoGEO.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                      AND RHPontoDet.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN pstserv ON pstserv.secao = RHPontoDet.secao \n"
                    + "                   AND pstserv.codfil = Funcion.codfil \n"
                    + " LEFT JOIN clientes ON clientes.codigo = pstserv.CodCli AND clientes.codfil = pstserv.codfil";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }

            sql += " WHERE RHPonto.DtCompet = ? And RHPonto.Batida < 9000 \n";

            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and RHPonto.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and RHPonto.matr " + matricula + "\n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.Local LIKE '%" + secao + "%' \n";
            }
            sql += ") Union ( \n"
                    + " Select '2' Tipo, CONCAT(tmktdetpst.sequencia,';',tmktdetpst.codfil,';') Chave, pstserv.secao Secao, pstserv.local Posto, \n"
                    + " pessoa.nome Funcionario, tmktdetpst.data Data, tmktdetpst.hora Hora,  tmktdetpst.latitude Latitude, tmktdetpst.longitude Longitude, \n"
                    + " tmktdetpst.historico Titulo, convert(varchar(max),tmktdetpst.detalhes) Detalhes, tmktdetpst.Fotos Fotos,\n"
                    + " tmktdetpst.codfil, CAST(CAST(dbo.fun_calcdistancia(clientes.Latitude, clientes.Longitude, tmktdetpst.Latitude, tmktdetpst.Longitude) AS DECIMAL(18,3)) AS VARCHAR(30)) DistanciaKm  \n"
                    //+ " tmktdetpst.codfil,  0 as DistanciaKm  \n"
                    + " from tmktdetpst \n"
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia \n"
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao \n"
                    + "                    and pstserv.codfil = tmktdetpst.codfil \n"
                    + " left join pessoa\n"
                    + "  on tmktdetpst.codPessoa = pessoa.codigo\n"
                    + " left join funcion\n"
                    + "  on pessoa.matr = funcion.matr\n"
                    + " left join pstserv AS pstServBase\n"
                    + "  on funcion.secao = pstServBase.secao\n"
                    + " left join clientes\n"
                    + "  on pstServBase.codCli = clientes.codigo\n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE tmktdetpst.data = ? \n";
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and pessoa.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and pessoa.matr " + matricula + "\n";
            }
            if (filtroWeb.equals("1")) {
                sql += " and tmktdetpst.filtroweb = 1 \n";
            }
            if (!codFil.equals("")) {
                sql += " and tmktdetpst.codfil = ? \n";
            }
            if (!codPessoa.equals("")) {
                sql += " and tmktdetpst.codpessoa = ? \n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.Local LIKE '%" + secao + "%' \n";
            }
            sql += " and tmktdetpst.tipocont = 'R') UNION  \n"
                    + " (Select '3' Tipo, CONCAT(tmktdetpst.sequencia,';',tmktdetpst.codfil,';') Chave, pstserv.secao Secao, pstserv.local Posto, \n"
                    + " pessoa.nome Funcionario, tmktdetpst.data Data, tmktdetpst.hora Hora,  tmktdetpst.latitude Latitude, tmktdetpst.longitude Longitude, \n"
                    + " tmktdetpst.historico Titulo, convert(varchar(max),tmktdetpst.detalhes) Detalhes, tmktdetpst.Fotos Fotos,\n"
                    + " tmktdetpst.codfil, '0' AS DistanciaKm \n"
                    + " from tmktdetpst \n"
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia \n"
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao \n"
                    + "                    and pstserv.codfil = tmktdetpst.codfil \n"
                    + " left join pessoa on pessoa.codigo = tmktdetpst.codpessoa \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE tmktdetpst.data = ? \n";

            if (filtroWeb.equals("1")) {
                sql += " and tmktdetpst.filtroweb = 1 \n";
            }
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!codPessoa.equals("")) {
                sql += " and tmktdetpst.codpessoa = ? \n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.Local LIKE '%" + secao + "%' \n";
            }
            sql += " and tmktdetpst.tipocont = 'RS') UNION (\n"
                    + " select '4' Tipo, CONCAT(rondas.secao,';',CONVERT(varchar,rondas.data,112),';',rondas.hora,';',rondas.codfil,';') Chave, \n"
                    + " rondas.secao Secao, \n"
                    + " pstserv.local Posto, funcion.nome Funcionario, rondas.data Data, max(rondas.hr_alter) Hora, '' Latitude, '' Longitude,'' Titulo, \n"
                    + " CONCAT(min(rondas.hr_alter),';',max(rondas.hr_alter),';',count(rondas.hora),';',\n"
                    + "     (select count(*) from pstdepenronda \n"
                    + "         where pstdepenronda.hora = rondas.hora\n"
                    + "               and pstdepenronda.secao = rondas.secao \n"
                    + "               and pstdepenronda.codfil = rondas.codfil\n"
                    + "               and pstdepenronda.tipo = ?),';') Detalhes, '0' Fotos, \n"
                    + " rondas.codfil, '0' AS DistanciaKm \n"
                    + " from rondas \n"
                    + " left join funcion on funcion.matr = rondas.matr \n"
                    + "                   and funcion.codfil = rondas.codfil \n"
                    + " left join pstserv on pstserv.secao = rondas.secao \n"
                    + "                   and pstserv.codfil = rondas.codfil \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE rondas.Data = ? \n";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and rondas.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and rondas.matr " + matricula + "\n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.Local LIKE '%" + secao + "%' \n";
            }
            sql += " group by rondas.hora, rondas.matr, rondas.secao, rondas.codfil, rondas.data, pstserv.local, funcion.nome )\n"
                    + " UNION (SELECT '5' Tipo, CONCAT(RHPonto.DtCompet,';',convert(Bigint,RHPonto.Matr),';',RHPonto.Batida,';',Funcion.codfil,';') Chave,\n"
                    + " RHPontoDet.secao Secao, \n"
                    + " pstserv.local Posto, Funcion.nome Funcionario, RHPonto.DtBatida Data, RHPonto.Hora Hora, RHPontoGEO.latitude Latitude, \n"
                    + " RHPontoGEO.longitude Longitude, '' Titulo, '' Detalhes, CONVERT (varchar, RHPonto.Batida) Fotos,\n"
                    + " Funcion.codfil, '0' AS DistanciaKm \n"
                    + " FROM RHPonto \n"
                    + " Left Join Funcion on Funcion.Matr = RHPonto.Matr \n"
                    + " Left Join RHPontoGEO on RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "                      and RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "                      and RHPontoGEO.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                      AND RHPontoDet.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN pstserv ON pstserv.secao = RHPontoDet.secao \n"
                    + "                   AND pstserv.codfil = Funcion.codfil \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE RHPonto.DtCompet = ? And RHPonto.Batida > 9000 \n";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and RHPonto.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and RHPonto.matr " + matricula + "\n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.Local LIKE '%" + secao + "%' \n";
            }
            sql += ") \n";
            if (filtroWeb.equals("0") || filtroWeb.equals("1")) {
                sql += " UNION (\n"
                        + " select '7' Tipo, Convert(Varchar, PstInspecao.Codigo) Chave, \n"
                        + " PstInspecao.Secao Secao, \n"
                        + " pstserv.local Posto, funcion.nome Funcionario, Convert(VarChar,PstInspecao.Data, 112) Data, max(PstInspecao.hr_alter) Hora, \n"
                        + " PstInspecao.Latitude Latitude, PstInspecao.Longitude Longitude,\n"
                        + " Inspecoes.Descricao Titulo, Pessoa.Nome Detalhes, (SELECT TOP 1 X.CaminhoImagem FROM pstinspecao AS X WHERE X.Matr = PstInspecao.Matr AND X.Secao = PstInspecao.Secao AND X.CodFil = PstInspecao.CodFil AND X.Data =PstInspecao.Data AND X.Codigo =PstInspecao.Codigo AND X.CaminhoImagem IS NOT NULL AND X.CaminhoImagem <> '') Fotos, \n"
                        + " PstInspecao.codfil, '0' AS DistanciaKm \n"
                        + " FROM PstInspecao \n"
                        + " LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao \n"
                        + "                 AND PstServ.Codfil = PstInspecao.CodFil \n"
                        + " LEFT JOIN Funcion ON Funcion.Matr = PstInspecao.Matr \n"
                        + "                AND Funcion.Codfil = PstInspecao.Codfil \n"
                        + " Left join Pessoa on Pessoa.Codigo = PstInspecao.CodOperador \n"
                        + " LEFT JOIN Inspecoes ON Inspecoes.Codigo = PstInspecao.CodInspecao \n";
                if (codPessoaAut != null) {
                    sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                            + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                            + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                            + "                        and pessoacliaut.flag_excl <> '*' \n";
                }
                sql += " WHERE PstInspecao.Data = ? \n";
                if (!codFil.equals("")) {
                    sql += " AND PstInspecao.CodFil = ? \n";
                }
                if (!matricula.equals("") && !matricula.contains("IN")) {
                    sql += " and PstInspecao.matr = " + matricula + " \n";
                } else if (matricula.contains("IN")) {
                    sql += " and PstInspecao.matr " + matricula + "\n";
                }
                if (!secao.equals("")) {
                    sql += " and pstserv.Local LIKE '%" + secao + "%' \n";
                }
                if (codPessoaAut != null) {
                    sql += " and pessoacliaut.codigo is not null ";
                }
                sql += " GROUP BY PstInspecao.Codigo, Pessoa.Nome, Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, PstInspecao.Data, PstInspecao.Matr, \n"
                        + " PstInspecao.CodInspecao, PstServ.Local, Funcion.Nome, PstInspecao.Latitude, PstInspecao.Longitude ) \n"
                        + " UNION ( SELECT '9' Tipo, CONCAT(PessoaTrajeto.CodPessoa,';',CONVERT(varchar,PessoaTrajeto.DtCompet,112),';') Chave, '' Secao,\n"
                        + " '' Posto, Pessoa.Nome Funcionario, CONVERT(Varchar,PessoaTrajeto.DtCompet,112) Data, \n"
                        + " (SELECT TOP 1 (SELECT MAX(h) FROM (VALUES(HrCheg), (HrSaida)) AS VALUE(h)) Hora\n"
                        + "    FROM PessoaTrajeto p WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                            AND CodPessoa = PessoaTrajeto.CodPessoa ORDER BY Hora desc) Hora, '' Latitude, '' Longitude,\n"
                        + " '' Titulo,\n"
                        + " CONCAT(min(PessoaTrajeto.HrCheg),';',"
                        + "(SELECT TOP 1 (SELECT MAX(h) FROM (VALUES(HrCheg), (HrSaida)) AS VALUE(h)) Hora\n"
                        + "    FROM PessoaTrajeto p WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                            AND CodPessoa = PessoaTrajeto.CodPessoa ORDER BY Hora desc),';') Detalhes, '0' Fotos, '' CodFil, '0' AS DistanciaKm \n"
                        + " FROM PessoaTrajeto\n"
                        + " LEFT JOIN Pessoa on Pessoa.Codigo = PessoaTrajeto.CodPessoa\n"
                        + " WHERE DtCompet = ? \n";
                if (!codPessoa.equals("")) {
                    sql += " AND CodPessoa = ? \n";
                }
                sql += " GROUP BY CodPessoa, DtCompet, Nome) \n"
                        + " UNION (Select '10' Tipo, CONCAT(tmktdet.sequencia,';',tmktdet.codfil,';') Chave, CONVERT(Varchar, tmktdet.CodCont) Secao, \n"
                        + "    contatos.Nome Posto, pessoa.nome Funcionario, CONVERT(Varchar,tmktdet.data,112) Data, tmktdet.hora Hora, \n"
                        + "    tmktdet.latitude Latitude, tmktdet.longitude Longitude, tmktdet.historico Titulo, convert(varchar(max),tmktdet.detalhes) \n"
                        + "    Detalhes, tmktdet.Fotos Fotos,\n"
                        + "    tmktdet.codfil, '0' AS DistanciaKm \n"
                        + "    from tmktdet \n"
                        + "    left join pessoa on pessoa.codigo = tmktdet.codpessoa \n"
                        + "    left join contatos on contatos.codigo = tmktdet.CodCont\n"
                        + "                      and contatos.codfil = tmktdet.codfil\n"
                        + "    WHERE tmktdet.data = ?\n";
                if (!codPessoa.equals("")) {
                    sql += " AND tmktdet.CodPessoa = ? \n";
                }
                sql += ")\n";
            }
            sql += ") a\n";
            if (!tipoRelatorio.equals("")) {
                sql += " WHERE Tipo = ? \n";
            }
            sql += ") AS RowConstrainedResult ) b ";
            Consulta consulta = new Consulta(sql, persistencia);

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            /*          if (!secao.equals("")) {
                consulta.setString(secao);
            }
             */
            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }
            /*if (!secao.equals("")) {
                consulta.setString(secao);
            }*/

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }
            /*if (!secao.equals("")) {
                consulta.setString(secao);
            }*/

            consulta.setString(tipoRonda);
            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            /*if (!secao.equals("")) {
                consulta.setString(secao);
            }*/
            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            /*if (!secao.equals("")) {
                consulta.setString(secao);
            }*/
            if (filtroWeb.equals("0") || filtroWeb.equals("1")) {
                consulta.setString(data);
                if (!codFil.equals("")) {
                    consulta.setString(codFil);
                }

                /*if (!secao.equals("")) {
                    consulta.setString(secao);
                }*/
                consulta.setString(data);
                if (!codPessoa.equals("")) {
                    consulta.setString(codPessoa);
                }

                consulta.setString(data);
                if (!codPessoa.equals("")) {
                    consulta.setString(codPessoa);
                }
            }

            if (!tipoRelatorio.equals("")) {
                consulta.setString(tipoRelatorio);
            }

            consulta.select();
            if (consulta.Proximo()) {
                retorno = consulta.getInt("Total");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.obterLogs - " + e.getMessage());
        }
    }

    public Integer totalLogs(String data1, String data2, String codFil, String matricula, String nomeColaborador,
            String codPessoa, String secao, String tipoRonda, String filtroWeb, BigDecimal codPessoaAut, String tipoRelatorio,
            Set<String> resumoCategoria, 
            Persistencia persistencia) throws Exception {
        try {
            int retorno = 0;

            if (null == matricula) {
                matricula = "";
            }

            if (null == secao) {
                secao = "";
            }

            if (null == filtroWeb) {
                filtroWeb = "0";
            }

            if (null == tipoRelatorio) {
                tipoRelatorio = "";
            }

            if (null == codPessoa) {
                codPessoa = "";
            }

            if (null == nomeColaborador) {
                nomeColaborador = "";
            }

            if (null == matricula) {
                matricula = "";
            }

            String sql = " SELECT * FROM \n"
                    + " (SELECT RowConstrainedResult.* FROM ( SELECT *, ROW_NUMBER() OVER ( ORDER BY Data DESC, Hora DESC ) RowNum \n"
                    + " From (\n"
                    + "(SELECT '1' Tipo, CONCAT(RHPonto.DtCompet,';',convert(Bigint,RHPonto.Matr),';',RHPonto.Batida,';',Funcion.codfil,';') Chave,\n"
                    + " RHPontoDet.secao Secao, \n"
                    + " pstserv.local Posto, Funcion.nome Funcionario, RHPonto.DtCompet Data, RHPonto.Hora Hora, RHPontoGEO.latitude Latitude, \n"
                    + " RHPontoGEO.longitude Longitude, '' Titulo, '' Detalhes, CONVERT (varchar, RHPonto.Batida) Fotos,\n"
                    + " Funcion.codfil, CAST(CAST(dbo.fun_calcdistancia(clientes.Latitude, clientes.Longitude, RHPontoGEO.Latitude, RHPontoGEO.Longitude) AS DECIMAL(18,3)) AS VARCHAR(30)) DistanciaKm \n"
                    //+ " Funcion.codfil, 0 as DistanciaKm \n"
                    + " FROM RHPonto \n"
                    + " Left Join Funcion on Funcion.Matr = RHPonto.Matr \n"
                    + " Left Join RHPontoGEO on RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "                      and RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "                      and RHPontoGEO.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                      AND RHPontoDet.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN pstserv ON pstserv.secao = RHPontoDet.secao \n"
                    + "                   AND pstserv.codfil = Funcion.codfil \n"
                    + " LEFT JOIN clientes ON clientes.codigo = pstserv.CodCli AND clientes.codfil = pstserv.codfil";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }

            sql += " WHERE RHPonto.DtCompet BETWEEN '" + data1 + "' AND '" + data2 + "' And RHPonto.Batida < 9000 \n";

            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }

            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and RHPonto.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and pstserv.secao " + matricula + "\n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.Local LIKE '%" + secao + "%' \n";
            }
            if (!nomeColaborador.equals("")) {
                sql += " and Funcion.nome LIKE '%" + nomeColaborador + "%' \n";
            }

            sql += ") Union ( \n"
                    + " Select '2' Tipo, CONCAT(tmktdetpst.sequencia,';',tmktdetpst.codfil,';') Chave, pstserv.secao Secao, pstserv.local Posto, \n"
                    + " pessoa.nome Funcionario, tmktdetpst.data Data, tmktdetpst.hora Hora,  tmktdetpst.latitude Latitude, tmktdetpst.longitude Longitude, \n"
                    + " tmktdetpst.historico Titulo, convert(varchar(max),tmktdetpst.detalhes) Detalhes, tmktdetpst.Fotos Fotos,\n"
                    + " tmktdetpst.codfil, CAST(CAST(dbo.fun_calcdistancia(clientes.Latitude, clientes.Longitude, tmktdetpst.Latitude, tmktdetpst.Longitude) AS DECIMAL(18,3)) AS VARCHAR(30)) DistanciaKm  \n"
                    //+ " tmktdetpst.codfil, 0 as DistanciaKm  \n"
                    + " from tmktdetpst \n"
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia \n"
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao \n"
                    + "                    and pstserv.codfil = tmktdetpst.codfil \n"
                    + " left join pessoa\n"
                    + "  on tmktdetpst.codPessoa = pessoa.codigo\n"
                    + " left join funcion\n"
                    + "  on pessoa.matr = funcion.matr\n"
                    + " left join pstserv AS pstServBase\n"
                    + "  on funcion.secao = pstServBase.secao\n"
                    + " left join clientes\n"
                    + "  on pstServBase.codCli = clientes.codigo\n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE tmktdetpst.data BETWEEN '" + data1 + "' AND '" + data2 + "' \n";
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and pessoa.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and pstserv.secao " + matricula + "\n";
            }
            if (filtroWeb.equals("1")) {
                sql += " and tmktdetpst.filtroweb = 1 \n";
            }
            if (!codFil.equals("")) {
                sql += " and tmktdetpst.codfil = ? \n";
            }
            if (!codPessoa.equals("")) {
                sql += " and tmktdetpst.codpessoa = ? \n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.Local LIKE '%" + secao + "%' \n";
            }
            if (!nomeColaborador.equals("")) {
                sql += " and pessoa.nome LIKE '%" + nomeColaborador + "%' \n";
            }
            sql += " and tmktdetpst.tipocont = 'R') UNION  \n"
                    + " (Select '3' Tipo, CONCAT(tmktdetpst.sequencia,';',tmktdetpst.codfil,';') Chave, pstserv.secao Secao, pstserv.local Posto, \n"
                    + " pessoa.nome Funcionario, tmktdetpst.data Data, tmktdetpst.hora Hora,  tmktdetpst.latitude Latitude, tmktdetpst.longitude Longitude, \n"
                    + " tmktdetpst.historico Titulo, convert(varchar(max),tmktdetpst.detalhes) Detalhes, tmktdetpst.Fotos Fotos,\n"
                    + " tmktdetpst.codfil, '0' AS DistanciaKm \n"
                    + " from tmktdetpst \n"
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia \n"
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao \n"
                    + "                    and pstserv.codfil = tmktdetpst.codfil \n"
                    + " left join pessoa on pessoa.codigo = tmktdetpst.codpessoa \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE tmktdetpst.data BETWEEN '" + data1 + "' AND '" + data2 + "' \n";

            if (filtroWeb.equals("1")) {
                sql += " and tmktdetpst.filtroweb = 1 \n";
            }
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!codPessoa.equals("")) {
                sql += " and tmktdetpst.codpessoa = ? \n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.Local LIKE '%" + secao + "%' \n";
            }
            if (!nomeColaborador.equals("")) {
                sql += " and pessoa.nome LIKE '%" + nomeColaborador + "%' \n";
            }
            sql += " and tmktdetpst.tipocont = 'RS') UNION (\n"
                    + " select '4' Tipo, CONCAT(rondas.secao,';',CONVERT(varchar,rondas.data,112),';',rondas.hora,';',rondas.codfil,';') Chave, \n"
                    + " rondas.secao Secao, \n"
                    + " pstserv.local Posto, funcion.nome Funcionario, rondas.data Data, max(rondas.hr_alter) Hora, '' Latitude, '' Longitude,'' Titulo, \n"
                    + " CONCAT(min(rondas.hr_alter),';',max(rondas.hr_alter),';',count(rondas.hora),';',\n"
                    + "     (select count(*) from pstdepenronda \n"
                    + "         where pstdepenronda.hora = rondas.hora\n"
                    + "               and pstdepenronda.secao = rondas.secao \n"
                    + "               and pstdepenronda.codfil = rondas.codfil\n"
                    + "               and pstdepenronda.tipo = ?),';') Detalhes, '0' Fotos, \n"
                    + " rondas.codfil, '0' AS DistanciaKm \n"
                    + " from rondas \n"
                    + " left join funcion on funcion.matr = rondas.matr \n"
                    + "                   and funcion.codfil = rondas.codfil \n"
                    + " left join pstserv on pstserv.secao = rondas.secao \n"
                    + "                   and pstserv.codfil = rondas.codfil \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE rondas.Data BETWEEN '" + data1 + "' AND '" + data2 + "' \n";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and rondas.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and pstserv.secao " + matricula + "\n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.Local LIKE '%" + secao + "%' \n";
            }
            if (!nomeColaborador.equals("")) {
                sql += " and Funcion.nome LIKE '%" + nomeColaborador + "%' \n";
            }
            sql += " group by rondas.hora, rondas.matr, rondas.secao, rondas.codfil, rondas.data, pstserv.local, funcion.nome )\n"
                    + " UNION (SELECT '5' Tipo, CONCAT(RHPonto.DtCompet,';',convert(Bigint,RHPonto.Matr),';',RHPonto.Batida,';',Funcion.codfil,';') Chave,\n"
                    + " RHPontoDet.secao Secao, \n"
                    + " pstserv.local Posto, Funcion.nome Funcionario, RHPonto.DtBatida Data, RHPonto.Hora Hora, RHPontoGEO.latitude Latitude, \n"
                    + " RHPontoGEO.longitude Longitude, '' Titulo, '' Detalhes, CONVERT (varchar, RHPonto.Batida) Fotos,\n"
                    + " Funcion.codfil, '0' AS DistanciaKm \n"
                    + " FROM RHPonto \n"
                    + " Left Join Funcion on Funcion.Matr = RHPonto.Matr \n"
                    + " Left Join RHPontoGEO on RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "                      and RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "                      and RHPontoGEO.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                      AND RHPontoDet.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN pstserv ON pstserv.secao = RHPontoDet.secao \n"
                    + "                   AND pstserv.codfil = Funcion.codfil \n";
            if (codPessoaAut != null) {
                sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                        + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                        + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                        + "                        and pessoacliaut.flag_excl <> '*' \n";
            }
            sql += " WHERE RHPonto.DtCompet BETWEEN '" + data1 + "' AND '" + data2 + "' And RHPonto.Batida > 9000 \n";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and RHPonto.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and pstserv.secao " + matricula + "\n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.Local LIKE '%" + secao + "%' \n";
            }
            if (!nomeColaborador.equals("")) {
                sql += " and Funcion.nome LIKE '%" + nomeColaborador + "%' \n";
            }
            sql += ") \n";
            if (filtroWeb.equals("0") || filtroWeb.equals("1")) {
                sql += " UNION (\n"
                        + " select '7' Tipo, Convert(Varchar, PstInspecao.Codigo) Chave, \n"
                        + " PstInspecao.Secao Secao, \n"
                        + " pstserv.local Posto, funcion.nome Funcionario, Convert(VarChar,PstInspecao.Data, 112) Data, max(PstInspecao.hr_alter) Hora, \n"
                        + " PstInspecao.Latitude Latitude, PstInspecao.Longitude Longitude,\n"
                        + " Inspecoes.Descricao Titulo, Pessoa.Nome Detalhes, (SELECT TOP 1 X.CaminhoImagem FROM pstinspecao AS X WHERE X.Matr = PstInspecao.Matr AND X.Secao = PstInspecao.Secao AND X.CodFil = PstInspecao.CodFil AND X.Data =PstInspecao.Data AND X.Codigo =PstInspecao.Codigo AND X.CaminhoImagem IS NOT NULL AND X.CaminhoImagem <> '') Fotos, \n"
                        + " PstInspecao.codfil, '0' AS DistanciaKm \n"
                        + " FROM PstInspecao \n"
                        + " LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao \n"
                        + "                 AND PstServ.Codfil = PstInspecao.CodFil \n"
                        + " LEFT JOIN Funcion ON Funcion.Matr = PstInspecao.Matr \n"
                        + "                AND Funcion.Codfil = PstInspecao.Codfil \n"
                        + " Left join Pessoa on Pessoa.Codigo = PstInspecao.CodOperador \n"
                        + " LEFT JOIN Inspecoes ON Inspecoes.Codigo = PstInspecao.CodInspecao \n";
                if (codPessoaAut != null) {
                    sql += " inner join pessoacliaut on pessoacliaut.codcli = pstserv.codcli \n"
                            + "                        and pessoacliaut.codfil = pstserv.codfil \n"
                            + "                        and pessoacliaut.codigo = " + codPessoaAut + " \n"
                            + "                        and pessoacliaut.flag_excl <> '*' \n";
                }
                sql += " WHERE PstInspecao.Data BETWEEN '" + data1 + "' AND '" + data2 + "' \n";
                if (!codFil.equals("")) {
                    sql += " AND PstInspecao.CodFil = ? \n";
                }
                if (!matricula.equals("") && !matricula.contains("IN")) {
                    sql += " and PstInspecao.matr = " + matricula + " \n";
                } else if (matricula.contains("IN")) {
                    sql += " and pstserv.secao " + matricula + "\n";
                }
                if (!secao.equals("")) {
                    sql += " and pstserv.Local LIKE '%" + secao + "%' \n";
                }
                if (!nomeColaborador.equals("")) {
                    sql += " and Funcion.nome LIKE '%" + nomeColaborador + "%' \n";
                }
                if (codPessoaAut != null) {
                    sql += " and pessoacliaut.codigo is not null ";
                }
                sql += " GROUP BY PstInspecao.Codigo, Pessoa.Nome, Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, PstInspecao.Data, PstInspecao.Matr, \n"
                        + " PstInspecao.CodInspecao, PstServ.Local, Funcion.Nome, PstInspecao.Latitude, PstInspecao.Longitude ) \n"
                        + " UNION ( SELECT '9' Tipo, CONCAT(PessoaTrajeto.CodPessoa,';',CONVERT(varchar,PessoaTrajeto.DtCompet,112),';') Chave, '' Secao,\n"
                        + " '' Posto, Pessoa.Nome Funcionario, CONVERT(Varchar,PessoaTrajeto.DtCompet,112) Data, \n"
                        + " (SELECT TOP 1 (SELECT MAX(h) FROM (VALUES(HrCheg), (HrSaida)) AS VALUE(h)) Hora\n"
                        + "    FROM PessoaTrajeto p WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                            AND CodPessoa = PessoaTrajeto.CodPessoa ORDER BY Hora desc) Hora, '' Latitude, '' Longitude,\n"
                        + " '' Titulo,\n"
                        + " CONCAT(min(PessoaTrajeto.HrCheg),';',"
                        + "(SELECT TOP 1 (SELECT MAX(h) FROM (VALUES(HrCheg), (HrSaida)) AS VALUE(h)) Hora\n"
                        + "    FROM PessoaTrajeto p WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                        + "                            AND CodPessoa = PessoaTrajeto.CodPessoa ORDER BY Hora desc),';') Detalhes, '0' Fotos, '' CodFil, '0' AS DistanciaKm \n"
                        + " FROM PessoaTrajeto\n"
                        + " LEFT JOIN Pessoa on Pessoa.Codigo = PessoaTrajeto.CodPessoa\n"
                        + " WHERE DtCompet BETWEEN '" + data1 + "' AND '" + data2 + "' \n";
                if (!codPessoa.equals("")) {
                    sql += " AND CodPessoa = ? \n";
                }
                sql += " GROUP BY CodPessoa, DtCompet, Nome) \n"
                        + " UNION (Select '10' Tipo, CONCAT(tmktdet.sequencia,';',tmktdet.codfil,';') Chave, CONVERT(Varchar, tmktdet.CodCont) Secao, \n"
                        + "    contatos.Nome Posto, pessoa.nome Funcionario, CONVERT(Varchar,tmktdet.data,112) Data, tmktdet.hora Hora, \n"
                        + "    tmktdet.latitude Latitude, tmktdet.longitude Longitude, tmktdet.historico Titulo, convert(varchar(max),tmktdet.detalhes) \n"
                        + "    Detalhes, tmktdet.Fotos Fotos,\n"
                        + "    tmktdet.codfil, '0' AS DistanciaKm \n"
                        + "    from tmktdet \n"
                        + "    left join pessoa on pessoa.codigo = tmktdet.codpessoa \n"
                        + "    left join contatos on contatos.codigo = tmktdet.CodCont\n"
                        + "                      and contatos.codfil = tmktdet.codfil\n"
                        + "    WHERE tmktdet.Data BETWEEN '" + data1 + "' AND '" + data2 + "' \n";
                if (!codPessoa.equals("")) {
                    sql += " AND tmktdet.CodPessoa = ? \n";
                }
                sql += ")\n";
            }
            sql += ") a\n";
            if (!tipoRelatorio.equals("")) {
                sql += " WHERE Tipo = ? \n";
            }
            sql += ") AS RowConstrainedResult ) b ";
            Consulta consulta = new Consulta(sql, persistencia);

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            //consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            /*          if (!secao.equals("")) {
                consulta.setString(secao);
            }
             */
            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            //consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }
            /*if (!secao.equals("")) {
                consulta.setString(secao);
            }*/

            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            //consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }
            /*if (!secao.equals("")) {
                consulta.setString(secao);
            }*/

            consulta.setString(tipoRonda);
            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            //consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            /*if (!secao.equals("")) {
                consulta.setString(secao);
            }*/
            //if (codPessoaAut != null) {
            //    consulta.setBigDecimal(codPessoaAut);
            //}
            //consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            /*if (!secao.equals("")) {
                consulta.setString(secao);
            }*/
            if (filtroWeb.equals("0") || filtroWeb.equals("0")) {
                //consulta.setString(data);
                if (!codFil.equals("")) {
                    consulta.setString(codFil);
                }

                /*if (!secao.equals("")) {
                    consulta.setString(secao);
                }*/
                //consulta.setString(data);
                if (!codPessoa.equals("")) {
                    consulta.setString(codPessoa);
                }

                //consulta.setString(data);
                if (!codPessoa.equals("")) {
                    consulta.setString(codPessoa);
                }
            }

            if (!tipoRelatorio.equals("")) {
                consulta.setString(tipoRelatorio);
            }

            consulta.select();
            boolean filtrarSaida = resumoCategoria.contains(FILTRO_SAIDA);
            boolean filtrarEntrada = resumoCategoria.contains(FILTRO_ENTRADA);
            boolean batidaSaida = false;
            retorno = 0;
            while (consulta.Proximo()) {
                if (resumoCategoria.contains(consulta.getString("Tipo"))) {                    
                    // Quando o registro é tipo 1, pode representar entrada ou 
                    // saida, precisa verificar a quantidade de batidas
                    // Adicionar outros tipos também na lista
                    if (TIPO_ENTRADA_OU_SAIDA.equals(consulta.getString("Tipo"))) {
                        String batida = consulta.getString("Chave").split(";")[2];
                        batidaSaida = batida.equals("2") || batida.equals("4");
                    }                 
                    boolean adicionarItem = (filtrarEntrada && !batidaSaida) ||
                            (filtrarSaida && batidaSaida) ||
                            (!filtrarEntrada && !filtrarSaida);                            
                    if (adicionarItem) {                      
                        ++retorno;                        
                    }                    
                }
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.obterLogs - " + e.getMessage());
        }
    }

    /**
     * Tipos de relatório: 1 - Batida de Ponto 2 - Relatório 3 - Relatório
     * Supervisor 4 - Resumo Ronda 5 - Check Ins Supervisores 6 - Rondas 7 -
     * Rondas completas 8 - Check Ins Prestadores 9 - Resumo Rota Prestador 10 -
     * Paradas Rota Prestador
     *
     * @param data
     * @param codFil
     * @param matricula
     * @param codPessoa
     * @param secao
     * @param tipoRonda
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<LogsSatMobEW> obterLogs(String data, String codFil, String matricula,
            String codPessoa, String secao, String tipoRonda, Persistencia persistencia) throws Exception {
        try {
            if (persistencia.getEmpresa().contains("DELTA")) {
                secao = "";
            }
            List<LogsSatMobEW> retorno = new ArrayList<>();
            String sql = "(SELECT '1' Tipo, CONCAT(CONVERT(Varchar,RHPonto.DtCompet,112),';',convert(Bigint,RHPonto.Matr),';',RHPonto.Batida,';',Funcion.codfil,';') Chave,\n"
                    + " RHPontoDet.secao Secao, \n"
                    + " pstserv.local Posto, Funcion.nome Funcionario, CONVERT(Varchar,RHPonto.DtBatida,112) Data, RHPonto.Hora Hora, RHPontoGEO.latitude Latitude, \n"
                    + " RHPontoGEO.longitude Longitude, '' Titulo, '' Detalhes, CONVERT (varchar, RHPonto.Batida) Fotos,\n"
                    + " Funcion.codfil, RHPontoImagem.URL \n"
                    + " FROM RHPonto \n"
                    + " Left Join Funcion on Funcion.Matr = RHPonto.Matr \n"
                    + " Left Join RHPontoGEO on RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "                      and RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "                      and RHPontoGEO.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                      AND RHPontoDet.Batida = RHPonto.Batida \n"
                    + " LEFT join RHPontoImagem ON RHPontoImagem.matr = RHPonto.matr "
                    + "                         AND RHPontoImagem.dtcompet = RHPonto.dtcompet "
                    + "                         AND RHPontoImagem.batida = RHPonto.batida "
                    + " LEFT JOIN pstserv ON pstserv.secao = RHPontoDet.secao \n"
                    + "                   AND pstserv.codfil = Funcion.codfil "
                    + " WHERE RHPonto.DtCompet = ? And RHPonto.Batida < 9000 \n";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (persistencia.getEmpresa().contains("CONFE") || persistencia.getEmpresa().contains("DELTA")) {
                sql += " and Funcion.matr = " + matricula;
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and RHPonto.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and RHPonto.matr " + matricula + "\n";
            }
            if (!secao.equals("")) {
                sql += " and RHPontoDet.Secao = ? \n";
            }
            sql += ") Union ( \n"
                    + " Select '2' Tipo, CONCAT(tmktdetpst.sequencia,';',tmktdetpst.codfil,';') Chave, pstserv.secao Secao, pstserv.local Posto, \n"
                    + " pessoa.nome Funcionario, CONVERT(Varchar,tmktdetpst.data,112) Data, tmktdetpst.hora Hora,  tmktdetpst.latitude Latitude, tmktdetpst.longitude Longitude, \n"
                    + " tmktdetpst.historico Titulo, convert(varchar(max),tmktdetpst.detalhes) Detalhes, tmktdetpst.Fotos Fotos,\n"
                    + " tmktdetpst.codfil, '' URL \n"
                    + " from tmktdetpst \n"
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia \n"
                    + " inner join pstserv on pstserv.secao = tmktdetpst.secao \n"
                    + "                    and pstserv.codfil = tmktdetpst.codfil \n"
                    + " left join pessoa on pessoa.codigo = tmktdetpst.codpessoa \n"
                    + " WHERE tmktdetpst.data = ? \n";
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and pessoa.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and pessoa.matr " + matricula + "\n";
            }
            if (persistencia.getEmpresa().contains("CONFE")) {
                sql += " and Pessoa.matr = " + matricula;
            }
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (!codPessoa.equals("")) {
                sql += " and tmktdetpst.codpessoa = ? \n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.secao = ? \n";
            }
            sql += " and tmktdetpst.tipocont = 'R') UNION  \n"
                    + " (Select '3' Tipo, CONCAT(tmktdetpst.sequencia,';',tmktdetpst.codfil,';') Chave, pstserv.secao Secao, pstserv.local Posto, \n"
                    + " pessoa.nome Funcionario,  CONVERT(Varchar,tmktdetpst.data,112) Data, tmktdetpst.hora Hora,  tmktdetpst.latitude Latitude, tmktdetpst.longitude Longitude, \n"
                    + " tmktdetpst.historico Titulo, convert(varchar(max),tmktdetpst.detalhes) Detalhes, tmktdetpst.Fotos Fotos,\n"
                    + " tmktdetpst.codfil, '' URL \n"
                    + " from tmktdetpst \n"
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia \n"
                    + " inner join pstserv on pstserv.secao = tmktdetpst.secao \n"
                    + "                    and pstserv.codfil = tmktdetpst.codfil \n"
                    + " left join pessoa on pessoa.codigo = tmktdetpst.codpessoa \n"
                    + " WHERE tmktdetpst.data = ? \n";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (persistencia.getEmpresa().contains("CONFE")) {
                sql += " and Pessoa.matr = " + matricula;
            }
            if (!codPessoa.equals("")) {
                sql += " and tmktdetpst.codpessoa = ? \n";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.secao = ? \n";
            }
            sql += " and tmktdetpst.tipocont = 'RS') UNION (\n"
                    + " select '4' Tipo, CONCAT(rondas.secao,';',CONVERT(varchar,rondas.data,112),';',rondas.hora,';',rondas.codfil,';') Chave, \n"
                    + " rondas.secao Secao, \n"
                    + " pstserv.local Posto, funcion.nome Funcionario, CONVERT(Varchar,rondas.data,112) Data, max(rondas.hr_alter) Hora, '' Latitude, '' Longitude,'' Titulo, \n"
                    + " CONCAT(min(rondas.hr_alter),';',max(rondas.hr_alter),';',count(rondas.hora),';',\n"
                    + "     (select count(*) from pstdepenronda \n"
                    + "         where pstdepenronda.hora = rondas.hora\n"
                    + "               and pstdepenronda.secao = rondas.secao \n"
                    + "               and pstdepenronda.codfil = rondas.codfil\n"
                    + "               and pstdepenronda.tipo = ?),';') Detalhes, '0' Fotos, \n"
                    + " rondas.codfil, '' URL \n"
                    + " from rondas \n"
                    + " left join funcion on funcion.matr = rondas.matr \n"
                    + "                   and funcion.codfil = rondas.codfil \n"
                    + " left join pstserv on pstserv.secao = rondas.secao \n"
                    + "                   and pstserv.codfil = rondas.codfil \n"
                    + "WHERE rondas.Data = ? \n";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (persistencia.getEmpresa().contains("CONFE")) {
                sql += " and Funcion.matr = " + matricula;
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and rondas.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and rondas.matr " + matricula + "\n";
            }
            if (!secao.equals("")) {
                sql += " and rondas.secao = ? \n";
            }
            sql += " group by rondas.hora, rondas.matr, rondas.secao, rondas.codfil, rondas.data, pstserv.local, funcion.nome )\n"
                    + " UNION (SELECT '5' Tipo, CONCAT(CONVERT(Varchar,RHPonto.DtCompet,112),';',convert(Bigint,RHPonto.Matr),';',RHPonto.Batida,';',Funcion.codfil,';') Chave,\n"
                    + " RHPontoDet.secao Secao, \n"
                    + " pstserv.local Posto, Funcion.nome Funcionario, CONVERT(Varchar,RHPonto.DtBatida,112) Data, RHPonto.Hora Hora, RHPontoGEO.latitude Latitude, \n"
                    + " RHPontoGEO.longitude Longitude, '' Titulo, '' Detalhes, CONVERT (varchar, RHPonto.Batida) Fotos,\n"
                    + " Funcion.codfil, '' URL \n"
                    + " FROM RHPonto \n"
                    + " Left Join Funcion on Funcion.Matr = RHPonto.Matr \n"
                    + " Left Join RHPontoGEO on RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "                      and RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "                      and RHPontoGEO.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                      AND RHPontoDet.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN pstserv ON pstserv.secao = RHPontoDet.secao \n"
                    + "                   AND pstserv.codfil = Funcion.codfil "
                    + " WHERE RHPonto.DtCompet = ? And RHPonto.Batida > 9000 \n";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? \n";
            }
            if (persistencia.getEmpresa().contains("CONFE")) {
                sql += " and Funcion.matr = " + matricula;
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and RHPonto.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and RHPonto.matr " + matricula + "\n";
            }
            if (!secao.equals("")) {
                sql += " and RHPontoDet.Secao = ? \n";
            }
            sql += ") \n"
                    + " UNION (\n"
                    + " select '7' Tipo, Convert(Varchar, PstInspecao.Codigo) Chave, \n"
                    + " PstInspecao.Secao Secao, \n"
                    + " pstserv.local Posto, funcion.nome Funcionario, Convert(VarChar, PstInspecao.Data, 112) Data, max(PstInspecao.hr_alter) Hora, \n"
                    + " PstInspecao.Latitude Latitude, PstInspecao.Longitude Longitude,\n"
                    + " Inspecoes.Descricao + (select TOP 1 CASE WHEN Inspecoes.Descricao = 'Entrada e Saída de Viaturas' THEN ' - ' + X.Resposta ELSE '' END from PstInspecao AS X where X.data = pstinspecao.data order by sequencia) Titulo, Pessoa.Nome Detalhes, (SELECT TOP 1 X.CaminhoImagem FROM pstinspecao AS X WHERE X.Matr = PstInspecao.Matr AND X.Secao = PstInspecao.Secao AND X.CodFil = PstInspecao.CodFil AND X.Data =PstInspecao.Data AND X.Codigo =PstInspecao.Codigo AND X.CaminhoImagem IS NOT NULL AND X.CaminhoImagem <> '') Fotos, \n"
                    + " PstInspecao.codfil, '' URL \n"
                    + " FROM PstInspecao \n"
                    + " LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao \n"
                    + "                 AND PstServ.Codfil = PstInspecao.CodFil \n"
                    + " LEFT JOIN Funcion ON Funcion.Matr = PstInspecao.Matr \n"
                    + "                AND Funcion.Codfil = PstInspecao.Codfil \n"
                    + " LEFT JOIN Pessoa ON Pessoa.Codigo = PstInspecao.CodOperador \n"
                    + " LEFT JOIN Inspecoes ON Inspecoes.Codigo = PstInspecao.CodInspecao \n"
                    + " WHERE PstInspecao.Data = ? \n";
            if (!codFil.equals("")) {
                sql += " AND PstInspecao.CodFil = ? \n";
            }
            if (persistencia.getEmpresa().contains("CONFE")) {
                sql += " and Funcion.matr = " + matricula;
            }
            if (!matricula.equals("") && !matricula.contains("IN")) {
                sql += " and PstInspecao.matr = " + matricula + " \n";
            } else if (matricula.contains("IN")) {
                sql += " and PstInspecao.matr " + matricula + "\n";
            }
            if (!secao.equals("")) {
                sql += " AND PstInspecao.Secao = ? \n";
            }
            sql += " GROUP BY PstInspecao.Codigo, Pessoa.Nome, Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, PstInspecao.Data, PstInspecao.Matr, \n"
                    + " PstInspecao.CodInspecao, PstServ.Local, Funcion.Nome, PstInspecao.Latitude, PstInspecao.Longitude ) \n"
                    + " UNION ( SELECT '9' Tipo, CONCAT(PessoaTrajeto.CodPessoa,';',CONVERT(varchar,PessoaTrajeto.DtCompet,112),';') Chave, '' Secao,\n"
                    + " '' Posto, Pessoa.Nome Funcionario, CONVERT(Varchar,PessoaTrajeto.DtCompet,112) Data, '24:00' Hora, '' Latitude, '' Longitude, "
                    + " '' Titulo,\n"
                    + " CONCAT(min(PessoaTrajeto.HrCheg),';',"
                    + "(SELECT TOP 1 (SELECT MAX(h) FROM (VALUES(HrCheg), (HrSaida)) AS VALUE(h)) Hora\n"
                    + "    FROM PessoaTrajeto p WHERE DtCompet = PessoaTrajeto.DtCompet \n"
                    + "                            AND CodPessoa = PessoaTrajeto.CodPessoa ORDER BY Hora desc),';') Detalhes, '0' Fotos, '' CodFil, '' URL\n"
                    + " FROM PessoaTrajeto\n"
                    + " LEFT JOIN Pessoa on Pessoa.Codigo = PessoaTrajeto.CodPessoa\n"
                    + " WHERE DtCompet = ? \n";
            if (!codPessoa.equals("")) {
                sql += " AND CodPessoa = ? \n";
            }
            if (persistencia.getEmpresa().contains("CONFE")) {
                sql += " and Pessoa.matr = " + matricula;
            }
            sql += " GROUP BY CodPessoa, DtCompet, Nome) \n"
                    + " UNION (Select '2' Tipo, CONCAT(tmktdet.sequencia,';',tmktdet.codfil,';') Chave, CONVERT(Varchar, tmktdet.CodCont) Secao, \n"
                    + "    contatos.Nome Posto, pessoa.nome Funcionario, CONVERT(Varchar,tmktdet.data,112) Data, tmktdet.hora Hora, \n"
                    + "    tmktdet.latitude Latitude, tmktdet.longitude Longitude, tmktdet.historico Titulo, convert(varchar(max),tmktdet.detalhes) \n"
                    + "    Detalhes, tmktdet.Fotos Fotos,\n"
                    + "    tmktdet.codfil, '' URL \n"
                    + "    from tmktdet \n"
                    + "    left join pessoa on pessoa.codigo = tmktdet.codpessoa \n"
                    + "    left join contatos on contatos.codigo = tmktdet.CodCont\n"
                    + "                      and contatos.codfil = tmktdet.codfil\n"
                    + "    WHERE tmktdet.data = ?\n";
            if (!codPessoa.equals("")) {
                sql += " AND tmktdet.CodPessoa = ? \n";
            }

            sql += ")\n";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }
            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }
            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            consulta.setString(tipoRonda);
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            /*if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }*/
            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            if (!secao.equals("")) {
                consulta.setString(secao);
            }

            consulta.setString(data);
            if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }

            consulta.setString(data);
            if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }

            consulta.select();
            LogsSatMobEW logsSatMobEW;
            while (consulta.Proximo()) {
                logsSatMobEW = new LogsSatMobEW();
                logsSatMobEW.setCodfil(consulta.getString("CodFil"));
                logsSatMobEW.setTipo(consulta.getString("Tipo"));
                logsSatMobEW.setChave(consulta.getString("Chave"));
                logsSatMobEW.setPosto(consulta.getString("Posto"));
                logsSatMobEW.setSecao(consulta.getString("Secao"));
                logsSatMobEW.setFuncionario(consulta.getString("Funcionario"));
                logsSatMobEW.setData(consulta.getString("Data"));
                logsSatMobEW.setHora(consulta.getString("Hora"));
                logsSatMobEW.setLatitude(consulta.getString("Latitude"));
                logsSatMobEW.setLongitude(consulta.getString("Longitude"));
                logsSatMobEW.setTitulo(consulta.getString("Titulo"));
                logsSatMobEW.setDetalhes(consulta.getString("Detalhes"));
                logsSatMobEW.setFotos(consulta.getString("Fotos"));
                logsSatMobEW.setURL(consulta.getString("URL"));
                retorno.add(logsSatMobEW);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.obterLogs - " + e.getMessage());
        }
    }

    public List<LogsSatMobEW> obterRondaSecao(String data, String codFil,
            String secao, String hora, Persistencia persistencia) throws Exception {
        try {
            List<LogsSatMobEW> retorno = new ArrayList<>();
            String sql = "SELECT case when Tipo = '0' Then RowNum else Sequencia end Chave, RowConstrainedResult.* FROM ( SELECT *, ROW_NUMBER() OVER ( ORDER BY Hora) RowNum \n"
                    + " FROM(\n"
                    + "(SELECT Rondas.CodFil CodFil, '6' Tipo, Rondas.Sequencia Sequencia, Rondas.Secao Secao, Pstserv.Local Posto,\n"
                    + "    Funcion.Nome Funcionario, CONVERT(VarChar, Rondas.Data, 112) Data, Rondas.Hr_Alter Hora,\n"
                    + "    Rondas.Latitude Latitude, Rondas.Longitude Longitude, '' Titulo, PstDepen.Descricao Detalhes, '' Fotos\n"
                    + "FROM Rondas \n"
                    + "LEFT JOIN funcion ON funcion.Matr = Rondas.Matr \n"
                    + "LEFT JOIN PstDepen ON PstDepen.Secao = Rondas.Secao \n"
                    + "                   AND PstDepen.CodFil = Rondas.CodFil \n"
                    + "                   AND PstDepen.codigo = Rondas.CodDepen \n"
                    + "LEFT JOIN PstServ ON PstServ.Secao = Rondas.Secao \n"
                    + "                   AND PstServ.CodFil = Rondas.CodFil \n"
                    + "WHERE Rondas.Data = ? AND Rondas.CodFil = ? \n"
                    + "    AND Rondas.Secao = ? AND Rondas.Hora = ?)\n"
                    + "UNION\n"
                    + "(SELECT '' CodFil, 'pos' Tipo, '' Sequencia, '' Secao, '' Posto, '' Funcionario, CONVERT(Varchar, rastrearEW.Data, 112) Data, rastrearEW.Hora Hora, \n"
                    + "    rastrearEW.Latitude Latitude, rastrearEW.Longitude Longitude, '' Titulo, '' Detalhes, '' Fotos\n"
                    + "FROM rastrearEW\n"
                    + "LEFT JOIN Pessoa ON Pessoa.Codigo = rastrearEW.CodPessoa\n"
                    + "LEFT JOIN Funcion on Funcion.Matr = Pessoa.Matr\n"
                    + "WHERE rastrearEW.Data = ? \n"
                    + "    AND Funcion.Matr IN (SELECT Rondas.Matr FROM Rondas\n"
                    + "                            WHERE Rondas.Data = ? AND Rondas.CodFil = ? \n"
                    + "                                AND Rondas.Secao = ? AND Rondas.Hora = ?))\n"
                    + "\n"
                    + ")a ) AS RowConstrainedResult \n"
                    + "WHERE Hora between ? AND ?\n"
                    + "ORDER BY RowNum";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codFil);
            consulta.setString(secao);
            consulta.setString(hora);
            consulta.setString(data);
            consulta.setString(data);
            consulta.setString(codFil);
            consulta.setString(secao);
            consulta.setString(hora);

            LocalTime h = LocalTime.parse(hora, DateTimeFormatter.ofPattern("H"));
            consulta.setString(h.format(DateTimeFormatter.ofPattern("HH:mm")));
            consulta.setString(h.plusHours(1).format(DateTimeFormatter.ofPattern("HH:mm")));
            consulta.select();
            LogsSatMobEW logsSatMobEW;
            while (consulta.Proximo()) {
                logsSatMobEW = new LogsSatMobEW();
                logsSatMobEW.setCodfil(consulta.getString("CodFil"));
                logsSatMobEW.setTipo(consulta.getString("Tipo"));
                logsSatMobEW.setChave(consulta.getString("Chave"));
                logsSatMobEW.setPosto(consulta.getString("Posto"));
                logsSatMobEW.setSecao(consulta.getString("Secao"));
                logsSatMobEW.setFuncionario(consulta.getString("Funcionario"));
                logsSatMobEW.setData(consulta.getString("Data"));
                logsSatMobEW.setHora(consulta.getString("Hora"));
                logsSatMobEW.setLatitude(consulta.getString("Latitude"));
                logsSatMobEW.setLongitude(consulta.getString("Longitude"));
                logsSatMobEW.setTitulo(consulta.getString("Titulo"));
                logsSatMobEW.setDetalhes(consulta.getString("Detalhes"));
                logsSatMobEW.setFotos(consulta.getString("Fotos"));
                retorno.add(logsSatMobEW);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.obterRondaSecao - " + e.getMessage() + "\r\n"
                    + "SELECT case when Tipo = '0' Then RowNum else Sequencia end Chave, RowConstrainedResult.* FROM ( SELECT *, ROW_NUMBER() OVER ( ORDER BY Hora) RowNum \n"
                    + " FROM(\n"
                    + "(SELECT '6' Tipo, Rondas.Sequencia Sequencia, Rondas.Secao Secao, Pstserv.Local Posto,\n"
                    + "    Funcion.Nome Funcionario, CONVERT(VarChar, Rondas.Data, 112) Data, Rondas.Hr_Alter Hora,\n"
                    + "    Rondas.Latitude Latitude, Rondas.Longitude Longitude, '' Titulo, PstDepen.Descricao Descricao,\n"
                    + "    '' Fotos\n"
                    + "FROM Rondas \n"
                    + "LEFT JOIN funcion ON funcion.Matr = Rondas.Matr \n"
                    + "LEFT JOIN PstDepen ON PstDepen.Secao = Rondas.Secao \n"
                    + "                   AND PstDepen.CodFil = Rondas.CodFil \n"
                    + "                   AND PstDepen.codigo = Rondas.CodDepen \n"
                    + "LEFT JOIN PstServ ON PstServ.Secao = Rondas.Secao \n"
                    + "                   AND PstServ.CodFil = Rondas.CodFil \n"
                    + "WHERE Rondas.Data = ? AND Rondas.CodFil = ? \n"
                    + "    AND Rondas.Secao = ? AND Rondas.Hora = ?)\n"
                    + "UNION\n"
                    + "(SELECT '0' Tipo, '' Sequencia, '' Secao, '' Posto, '' Funcionario, CONVERT(Varchar, rastrearEW.Data, 112) Data, rastrearEW.Hora Hora, \n"
                    + "    rastrearEW.Latitude Latitude, rastrearEW.Longitude Longitude, '' Titulo, '' Descricao, '' Fotos\n"
                    + "FROM rastrearEW\n"
                    + "LEFT JOIN Pessoa ON Pessoa.Codigo = rastrearEW.CodPessoa\n"
                    + "LEFT JOIN Funcion on Funcion.Matr = Pessoa.Matr\n"
                    + "WHERE rastrearEW.Data = ? \n"
                    + "    AND Funcion.Matr IN (SELECT Rondas.Matr FROM Rondas\n"
                    + "                            WHERE Rondas.Data = ? AND Rondas.CodFil = ? \n"
                    + "                                AND Rondas.Secao = ? AND Rondas.Hora = ?))\n"
                    + "\n"
                    + ")a ) AS RowConstrainedResult \n"
                    + "WHERE Hora between ? AND ?\n"
                    + "ORDER BY RowNum");
        }
    }

    /**
     * Lista as posições do prestador no dia
     *
     * @param codPessoa
     * @param data
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<LogsSatMobEW> obterRotaPrestador(String codPessoa, String data, Persistencia persistencia) throws Exception {
        try {
            List<LogsSatMobEW> retorno = new ArrayList<>();
            String sql = "SELECT RowConstrainedResult.* FROM ( SELECT *, ROW_NUMBER() OVER ( ORDER BY HrCheg) RowNum \n"
                    + " FROM ((SELECT CONVERT(Varchar, Data, 112) Data, hora HrCheg, '' HrSaida, '0' Tipo, Latitude, Longitude, Acuracia, '' Nome\n"
                    + "FROM rastrearEW\n"
                    + "WHERE Data = ? AND CodPessoa = ? AND Distancia > 0.025)\n"
                    + "\n"
                    + "UNION\n"
                    + "\n"
                    + "(SELECT CONVERT(Varchar, DtCompet, 112) Data, HrCheg HrCheg, HrSaida HrSaida, Tipo Tipo,\n"
                    + "CASE WHEN Contatos.Latitude IS NOT NULL THEN Contatos.Latitude\n"
                    + "    WHEN Clientes.Latitude IS NOT NULL THEN Clientes.Latitude END Latitude,\n"
                    + "CASE WHEN Contatos.Longitude IS NOT NULL THEN Contatos.Longitude\n"
                    + "    WHEN Clientes.Longitude IS NOT NULL THEN Clientes.Longitude END Longitude,\n"
                    + "'0' Acuracia, \n"
                    + "CASE WHEN Contatos.Fantasia IS NOT NULL THEN Contatos.Fantasia\n"
                    + "    WHEN PstServ.Local IS NOT NULL THEN PstServ.Local\n"
                    + "    WHEN Clientes.NRed IS NOT NULL THEN Clientes.NRed END Nome \n"
                    + "FROM PessoaTrajeto \n"
                    + "LEFT JOIN Contatos ON Contatos.Codigo = PessoaTrajeto.CodContato\n"
                    + "                  AND Contatos.CodFil = PessoaTrajeto.CodFil\n"
                    + "LEFT JOIN Clientes ON Clientes.Codigo = PessoaTrajeto.CodCli\n"
                    + "                  AND Clientes.CodFil = PessoaTrajeto.CodFil\n"
                    + "LEFT JOIN PstServ ON PstServ.Secao = PessoaTrajeto.Secao\n"
                    + "                 AND PstServ.CodFil = PessoaTrajeto.CodFil\n"
                    + "WHERE DtCompet = ? AND PessoaTrajeto.CodPessoa = ?))a ) AS RowConstrainedResult \n"
                    + " ORDER BY RowNum ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codPessoa);
            consulta.setString(data);
            consulta.setString(codPessoa);
            consulta.select();
            LogsSatMobEW logsSatMobEW;
            boolean pausado = true;
            while (consulta.Proximo()) {
                logsSatMobEW = new LogsSatMobEW();
                logsSatMobEW.setChave(consulta.getString("RowNum"));
                logsSatMobEW.setData(consulta.getString("Data"));
                logsSatMobEW.setDetalhes(consulta.getString("HrCheg") + ";" + consulta.getString("HrSaida"));
                logsSatMobEW.setTipo(consulta.getString("Tipo"));
                logsSatMobEW.setLatitude(consulta.getString("Latitude"));
                logsSatMobEW.setLongitude(consulta.getString("Longitude"));
                logsSatMobEW.setPosto(consulta.getString("Nome"));

                if (logsSatMobEW.getTipo().equals("3")) {
                    pausado = true;
                    retorno.add(logsSatMobEW);
                } else {
                    if (pausado && (logsSatMobEW.getTipo().equals("1") || logsSatMobEW.getTipo().equals("2"))) {
                        pausado = false;
                    }

                    if (!pausado) {
                        retorno.add(logsSatMobEW);
                    }
                }
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.obterRotaPrestador - " + e.getMessage() + "\r\n"
                    + "(SELECT Data Data, hora HrCheg, '' HrSaida, '0' Tipo, Latitude, Longitude, Acuracia, '' Nome\n"
                    + "FROM rastrearEW\n"
                    + "WHERE Data = " + data + " AND CodPessoa = " + codPessoa + ")\n"
                    + "\n"
                    + "UNION\n"
                    + "\n"
                    + "(SELECT DtCompet Data, HrCheg HrCheg, HrSaida HrSaida, Tipo Tipo,\n"
                    + "CASE WHEN Contatos.Latitude IS NOT NULL THEN Contatos.Latitude\n"
                    + "    WHEN Clientes.Latitude IS NOT NULL THEN Clientes.Latitude END Latitude,\n"
                    + "CASE WHEN Contatos.Longitude IS NOT NULL THEN Contatos.Longitude\n"
                    + "    WHEN Clientes.Longitude IS NOT NULL THEN Clientes.Longitude END Longitude,\n"
                    + "'0' Acuracia, \n"
                    + "CASE WHEN Contatos.Fantasia IS NOT NULL THEN Contatos.Fantasia\n"
                    + "    WHEN PstServ.Local IS NOT NULL THEN PstServ.Local\n"
                    + "    WHEN Clientes.NRed IS NOT NULL THEN Clientes.NRed END Nome \n"
                    + "FROM PessoaTrajeto \n"
                    + "LEFT JOIN Contatos ON Contatos.Codigo = PessoaTrajeto.CodContato\n"
                    + "                  AND Contatos.CodFil = PessoaTrajeto.CodFil\n"
                    + "LEFT JOIN Clientes ON Clientes.Codigo = PessoaTrajeto.CodCli\n"
                    + "                  AND Clientes.CodFil = PessoaTrajeto.CodFil\n"
                    + "LEFT JOIN PstServ ON PstServ.Secao = PessoaTrajeto.Secao\n"
                    + "                 AND PstServ.CodFil = PessoaTrajeto.CodFil\n"
                    + "WHERE DtCompet = " + data + " AND PessoaTrajeto.CodPessoa = " + codPessoa + ")\n"
                    + "\n"
                    + "ORDER BY HrCheg");
        }
    }

    public List<Funcion> consultaHistoricoEfetivo(String Secao, String CodFil, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            List<Funcion> Retorno = new ArrayList<>();
            sql = "SELECT\n"
                    + " Funcion.Matr,\n"
                    + " Funcion.Nome,\n"
                    + " Funcion.Cargo,\n"
                    + " RHHorario.Descricao Horario,\n"
                    + " Funcion.Escala,\n"
                    + " Funcion.Dt_Admis,\n"
                    + " Funcion.Dt_Demis\n"
                    + " FROM Funcion\n"
                    + " LEFT JOIN RHHorario\n"
                    + "   ON Funcion.Horario = RHHorario.Codigo\n"
                    + "  AND Funcion.CodFil = RHHorario.CodFil \n"
                    + " WHERE Funcion.CodFil = ?\n";
            if (null != Secao && !Secao.equals("")) {
                sql += "AND   Funcion.Secao = ?";
            }
            sql += " ORDER BY Funcion.Secao, Funcion.Nome";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(CodFil);

            if (null != Secao && !Secao.equals("")) {
                consulta.setString(Secao);
            }

            consulta.select();
            Funcion funcion;
            while (consulta.Proximo()) {
                funcion = new Funcion();

                funcion.setMatr(consulta.getString("Matr"));
                funcion.setNome(consulta.getString("Nome"));
                funcion.setCargo(consulta.getString("Cargo"));
                funcion.setFuncao(consulta.getString("Horario"));

                funcion.setEscala(consulta.getString("Escala"));
                funcion.setDt_Admis(consulta.getString("Dt_Admis"));
                funcion.setDt_Demis(consulta.getString("Dt_Demis"));

                Retorno.add(funcion);
            }

            consulta.Close();
            return Retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.consultaHistoricoEfetivo - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<RHPontoDet> consultaPontosEfetivo(String data1, String data2, String secao, String codFil, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            List<RHPontoDet> Retorno = new ArrayList<>();
            sql = "DECLARE @dtCompet1 VARCHAR(12);\n"
                    + " DECLARE @dtCompet2 VARCHAR(12);\n"
                    + " SET @dtCompet1 = '" + data1 + "';\n"
                    + " SET @dtCompet2 = '" + data2 + "';\n"
                    + "\n"
                    + " SELECT\n"
                    + " CONVERT(BIGINT, Funcion.Matr) Matr,\n"
                    + " Funcion.Nome,\n"
                    + " PstServ.Local,\n"
                    + " RHPonto1.DtCompet,\n"
                    + " RHPonto1.Hora Hora1,\n"
                    + " RHPonto2.Hora Hora2,\n"
                    + " RHPonto3.Hora Hora3,\n"
                    + " RHPonto4.Hora Hora4\n"
                    + " FROM Funcion\n"
                    + " JOIN RHPonto\n"
                    + "   ON Funcion.Matr = RHPonto.Matr\n"
                    + "  AND RHPonto.Batida = 1 \n"
                    + " LEFT JOIN PstServ\n"
                    + "   ON Funcion.Secao = PstServ.Secao\n"
                    + " LEFT JOIN RHPonto RHPonto1\n"
                    + "   ON Funcion.Matr = RHPonto1.Matr\n"
                    + "  AND RHPonto1.Batida = 1\n"
                    + "  AND RHPonto1.DtCompet = RHPonto.DtCompet\n"
                    + " LEFT JOIN RHPonto RHPonto2\n"
                    + "    ON Funcion.Matr = RHPonto2.Matr\n"
                    + "   AND RHPonto2.Batida = 2\n"
                    + "   AND RHPonto2.DtCompet = RHPonto.DtCompet\n"
                    + " LEFT JOIN RHPonto RHPonto3\n"
                    + "    ON Funcion.Matr = RHPonto3.Matr\n"
                    + "   AND RHPonto3.Batida = 3\n"
                    + "   AND RHPonto3.DtCompet = RHPonto.DtCompet\n"
                    + " LEFT JOIN RHPonto RHPonto4\n"
                    + "   ON Funcion.Matr = RHPonto4.Matr\n"
                    + "  AND RHPonto4.Batida = 4\n"
                    + "  AND RHPonto4.DtCompet = RHPonto.DtCompet\n"
                    + " WHERE (RHPonto1.Hora IS NOT NULL OR\n"
                    + "        RHPonto2.Hora IS NOT NULL OR\n"
                    + "        RHPonto3.Hora IS NOT NULL)\n"
                    + " AND  Funcion.codFil = ?\n"
                    + " AND  RHPonto.DtCompet BETWEEN @dtCompet1 AND @dtCompet2\n";

            if (null != secao && !secao.equals("")) {
                sql += " AND  Funcion.Secao = ?\n";
            }

            sql += "ORDER BY RHPonto.DtCompet, PstServ.Local, Funcion.Nome\n";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);

            if (null != secao && !secao.equals("")) {
                consulta.setString(secao);
            }

            consulta.select();
            RHPontoDet rhPontoDet;
            while (consulta.Proximo()) {
                rhPontoDet = new RHPontoDet();

                rhPontoDet.setMatr(consulta.getString("Matr"));
                rhPontoDet.setNome(consulta.getString("Nome"));
                rhPontoDet.setLocal(consulta.getString("Local"));
                rhPontoDet.setDtCompet(consulta.getString("DtCompet"));

                rhPontoDet.setHora1(consulta.getString("Hora1"));
                rhPontoDet.setHora2(consulta.getString("Hora2"));
                rhPontoDet.setHora3(consulta.getString("Hora3"));
                rhPontoDet.setHora4(consulta.getString("Hora4"));

                Retorno.add(rhPontoDet);
            }

            consulta.Close();
            return Retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.consultaPontosEfetivo - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public void alterarResposta(PstInspecao item, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            sql = "UPDATE PstInspecao SET resposta = ?, operador = ?, dt_alter = ?, hr_alter = ? WHERE codigo = ? AND sequencia = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(item.getResposta());
            consulta.setString(item.getOperador());
            consulta.setString(item.getDt_Alter());
            consulta.setString(item.getHr_Alter());
            consulta.setBigDecimal(item.getCodigo());
            consulta.setBigDecimal(item.getSequencia());

            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.alterarResposta - " + e.getMessage() + "\r\n" + sql);
        }
    }
}
