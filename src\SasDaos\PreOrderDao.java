/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CxFGuiasVol;
import SasBeans.PreOrder;
import SasBeans.Rt_Guias;
import SasBeansCompostas.PreOrderManifesto;
import SasBeansCompostas.PreOrderRPV;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class PreOrderDao {

    public String obterLote(String dtColeta, String codCli, Persistencia persistencia) throws Exception {
        try {
            String retorno = "1";
            String sql = " SELECT ISNULL(MAX(Lote),0) + 1 Lote FROM PreOrder WHERE DtColeta = ? AND CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtColeta);
            consulta.setString(codCli);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = consulta.getString("Lote");
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PreOrderDao.obterLote - " + e.getMessage() + "\r\n"
                    + " SELECT ISNULL(MAX(Lote),0) + 1 Lote FROM PreOrder WHERE DtColeta = " + dtColeta + " AND CodFil = " + codCli);
        }
    }

    /**
     * Realiza a exclusão dos registros da tabela PreOrder para uma data e
     * cliente específico
     *
     * @param dtColeta
     * @param codCli
     * @param lotes
     * @param persistencia
     * @throws Exception
     */
    public void excluirPreOrder(String dtColeta, String codCli, List<Integer> lotes, Persistencia persistencia) throws Exception {
        try {
            String sql = " DELETE FROM PreOrder WHERE DtColeta = ? AND CodCli1 = ? AND Lote in (";

            for (Integer lote : lotes) {
                sql += " ?, ";
            }
            sql = sql.substring(0, sql.length() - 2) + " ) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtColeta);
            consulta.setString(codCli);
            for (Integer lote : lotes) {
                consulta.setInt(lote);
            }
            consulta.delete();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("PreOrderDao.excluirPreOrder - " + e.getMessage());
        }
    }

    /**
     * Verifica a existência de pedidos para a data e cliente em questão
     *
     * @param dtColeta
     * @param codCli
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean verificarExistenciaPreOrder(String dtColeta, String codCli, Persistencia persistencia) throws Exception {
        try {
            boolean retorno = false;
            String sql = " SELECT * FROM PreOrder WHERE DtColeta = ? AND CodCli1 = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtColeta);
            consulta.setString(codCli);
            consulta.select();
            while (consulta.Proximo()) {
                retorno = true;
                break;
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PreOrderDao.verificarExistenciaPreOrder - " + e.getMessage());
        }
    }

    /**
     * Verifica a existência de pedidos para a data, cliente e pedidos em
     * questão e retorna os lotes das importações
     *
     * @param dtColeta
     * @param codCli
     * @param pedidosCliente
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Integer> verificarExistenciaPreOrder(String dtColeta, String codCli, List<String> pedidosCliente,
            Persistencia persistencia) throws Exception {
        try {
            List<Integer> retorno = new ArrayList<>();
            String sql = " SELECT Lote FROM PreOrder WHERE DtColeta = ? AND CodCli1 = ? AND PedidoCliente in (";
            for (String pedidoCliente : pedidosCliente) {
                sql += " ?, ";
            }
            sql = sql.substring(0, sql.length() - 2) + " ) GROUP BY Lote ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtColeta);
            consulta.setString(codCli);
            for (String pedidoCliente : pedidosCliente) {
                consulta.setString(pedidoCliente);
            }
            consulta.select();
            Integer lote;
            while (consulta.Proximo()) {
                lote = consulta.getInt("Lote");
                retorno.add(lote);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PreOrderDao.verificarExistenciaPreOrder - " + e.getMessage());
        }
    }

    public List<PreOrderManifesto> gerarManifesto(String sequencia, String parada, Persistencia persistencia) throws Exception {
        try {
            List<PreOrderManifesto> retorno = new ArrayList<>();
            String sql = " Select CONVERT(BIGINT,RPV.Guia) Guia, RPV.Serie, CliDstX.NRed NRedDst, PreOrderVOL.Lacre, PreOrderVol.Valor, \n"
                    + "Filiais.RazaoSocial, Filiais.Endereco ,Filiais.Cidade, Filiais.UF, Filiais.CNPJ, Filiais.Fone,  \n"
                    + "Convert(bigint, RPV.CodPessoaAut) CodPessoaAut, Pessoa.Nome, PreOrderVol.Obs, Rotas.Rota \n"
                    + "from Rt_Perc \n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "left join PreOrder on PreOrder.DtColeta = Rotas.Data\n"
                    + "                   and PreOrder.CodFil  = Rt_Perc.CodFil\n"
                    + "                   and PreOrder.CodCli1 = Rt_perc.CodCli1\n"
                    + "left join RPV on RPV.RPV = PreOrder.RPV\n"
                    + "          and RPV.parada = Rt_Perc.parada\n"
                    + "left join PreOrderVol on PreOrderVol.Sequencia = PreOrder.Sequencia\n"
                    + "left join Clientes CliDstX on  CliDstX.Codigo = PreOrder.CodCli2\n"
                    + "                           and CliDstX.CodFil = PreOrder.CodFil\n"
                    + "left join filiais  on Filiais.Codfil = Rotas.Codfil "
                    + "left Join Pessoa on Pessoa.codigo = RPV.CodPessoaAut "
                    + "where Rt_Perc.sequencia = ?\n"
                    + "  and Rt_Perc.Parada    = ?\n"
                    + "  and Rt_Perc.Flag_Excl <> '*'\n"
                    + "  and RPV.Flag_Excl <> '*'\n"
                    + "  and RPV.Guia is not null ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();
            PreOrderManifesto preOrderManifesto;
            while (consulta.Proximo()) {
                preOrderManifesto = new PreOrderManifesto();
                preOrderManifesto.setDestino(consulta.getString("NRedDst"));
                preOrderManifesto.setGuia(consulta.getString("Guia"));
                preOrderManifesto.setSerie(consulta.getString("Serie"));
                preOrderManifesto.setLacre(consulta.getString("Lacre"));
                preOrderManifesto.setValor(consulta.getString("Valor"));
                preOrderManifesto.setObs(consulta.getString("Obs"));

                preOrderManifesto.setRazaoSocial(consulta.getString("RazaoSocial"));
                preOrderManifesto.setEndereco(consulta.getString("Endereco"));
                preOrderManifesto.setCidade(consulta.getString("Cidade"));
                preOrderManifesto.setUF(consulta.getString("UF"));
                preOrderManifesto.setCNPJ(consulta.getString("CNPJ"));
                preOrderManifesto.setFone(consulta.getString("Fone"));

                preOrderManifesto.setNome(consulta.getString("Nome"));
                preOrderManifesto.setCodPessoaAut(consulta.getString("CodPessoaAut"));
                preOrderManifesto.setRota(consulta.getString("Rota"));

                retorno.add(preOrderManifesto);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PreOrderDao.gerarManifesto - " + e.getMessage());
        }
    }

    public List<PreOrderManifesto> gerarManifesto(String codfil, String dtColeta, String lote, Persistencia persistencia) throws Exception {
        try {
            List<PreOrderManifesto> retorno = new ArrayList<>();
            String sql = " Select CONVERT(BIGINT,RPV.Guia) Guia, RPV.Serie, CliDstX.NRed NRedDst, \n"
                    + "CliDstX.Agencia AgenciaDestino, CliDstX.SubAgencia SubAgenciaDestino, PreOrderVOL.Lacre, PreOrderVol.Valor, \n"
                    + "Filiais.RazaoSocial, Filiais.Endereco ,Filiais.Cidade, Filiais.UF, Filiais.CNPJ, Filiais.Fone,  \n"
                    + "Convert(bigint, RPV.CodPessoaAut) CodPessoaAut, Pessoa.Nome, Rotas.Rota \n"
                    + "from Rt_Perc \n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "left join PreOrder on PreOrder.DtColeta = Rotas.Data\n"
                    + "                   and PreOrder.CodFil  = Rotas.CodFil\n"
                    + "                   and PreOrder.CodCli1 = Rt_perc.CodCli1\n"
                    + "left join RPV on RPV.RPV = PreOrder.RPV\n"
                    + "          and RPV.parada = Rt_Perc.parada\n"
                    + "left join PreOrderVol on PreOrderVol.Sequencia = PreOrder.Sequencia\n"
                    + "left join Clientes CliDstX on  CliDstX.Codigo = PreOrder.CodCli2\n"
                    + "                           and CliDstX.CodFil = PreOrder.CodFil\n"
                    + "left join filiais  on Filiais.Codfil = Rotas.Codfil "
                    + "left Join Pessoa on Pessoa.codigo = RPV.CodPessoaAut "
                    + "where PreOrder.CodFil      = ?\n"
                    + "  and PreOrder.DtColeta    = ?\n"
                    + "  and PreOrder.Lote        = ?\n"
                    + "  and Rt_Perc.Flag_Excl <> '*'\n"
                    + "  and RPV.Flag_Excl <> '*'\n"
                    + "  and RPV.Guia is not null \n"
                    + "  and Rotas.rota <> '090'";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codfil);
            consulta.setString(dtColeta);
            consulta.setString(lote);
            consulta.select();
            PreOrderManifesto preOrderManifesto;
            while (consulta.Proximo()) {
                preOrderManifesto = new PreOrderManifesto();
//                preOrderManifesto.setAgenciaDestino(consulta.getString("AgenciaDestino"));
//                preOrderManifesto.setSubAgenciaDestino(consulta.getString("SubAgenciaDestino"));
                preOrderManifesto.setDestino(consulta.getString("NRedDst"));
                preOrderManifesto.setGuia(consulta.getString("Guia"));
                preOrderManifesto.setSerie(consulta.getString("Serie"));
                preOrderManifesto.setLacre(consulta.getString("Lacre"));
                preOrderManifesto.setValor(consulta.getString("Valor"));

                preOrderManifesto.setRazaoSocial(consulta.getString("RazaoSocial"));
                preOrderManifesto.setEndereco(consulta.getString("Endereco"));
                preOrderManifesto.setCidade(consulta.getString("Cidade"));
                preOrderManifesto.setUF(consulta.getString("UF"));
                preOrderManifesto.setCNPJ(consulta.getString("CNPJ"));
                preOrderManifesto.setFone(consulta.getString("Fone"));

                preOrderManifesto.setNome(consulta.getString("Nome"));
                preOrderManifesto.setCodPessoaAut(consulta.getString("CodPessoaAut"));
                preOrderManifesto.setRota(consulta.getString("Rota"));

                retorno.add(preOrderManifesto);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PreOrderDao.gerarManifesto - " + e.getMessage());
        }
    }

    /**
     * Retorna uma lista de PreOrders disponíveis
     *
     * @param dtInicio
     * @param dtFim
     * @param CodFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PreOrder> buscarPreOrder(String CodFil, String dtInicio, String dtFim, Persistencia persistencia) throws Exception {
        try {
            String sql = " Select CodFil, Banco, DtColeta, Lote, \n"
                    + " count(RPV) Qtd, SUM(Valor) Valor "
                    + "from PreOrder \n"
                    + "where dtColeta Between ? and ? \n";
            if (!CodFil.equals("")) {
                sql += " and CodFil = ? \n";
            }
            sql += " and RPV in (select RPV.RPV from RPV where RPV.guia is not null)\n"
                    + " Group by CodFil, Banco, DtColeta, Lote \n"
                    + " ORDER BY DtColeta DESC ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtInicio);
            consulta.setString(dtFim);
            if (!CodFil.equals("")) {
                consulta.setString(CodFil);
            }
            consulta.select();
            List<PreOrder> retorno = new ArrayList<>();
            while (consulta.Proximo()) {
                PreOrder preOrder = new PreOrder();
//                preOrder.setSequencia(consulta.getString("Sequencia"));
                preOrder.setCodFil(consulta.getString("CodFil"));
//                preOrder.setBanco(consulta.getString("Banco"));
//                preOrder.setAgencia(consulta.getString("Agencia"));
//                preOrder.setSubAgencia(consulta.getString("SubAgencia"));
                /**
                 * Salvando quantidade de RPVs em RPV
                 */
                preOrder.setRPV(consulta.getString("Qtd"));
                preOrder.setDtColeta(consulta.getString("DtColeta"));
//                preOrder.setDtEntrega(consulta.getString("DtEntrega"));
//                preOrder.setCodCli1(consulta.getString("CodCli1"));
//                preOrder.setHora1O(consulta.getString("Hora1O"));
//                preOrder.setHora2O(consulta.getString("Hora2O"));
//                preOrder.setCodCli2(consulta.getString("CodCli2"));
//                preOrder.setHora1D(consulta.getString("Hora1D"));
//                preOrder.setHora2D(consulta.getString("Hora2D"));
                preOrder.setLote(consulta.getString("Lote"));
//                preOrder.setSolicitante(consulta.getString("Solicitante"));
//                preOrder.setPedidoCliente(consulta.getString("PedidoCliente"));
                preOrder.setValor(consulta.getString("Valor"));
//                preOrder.setObs(consulta.getString("Obs"));
//                preOrder.setClassifSrv(consulta.getString("ClassifSrv"));
//                preOrder.setOperIncl(consulta.getString("OperIncl"));
//                preOrder.setDt_Incl(consulta.getString("Dt_Incl"));
//                preOrder.setHr_Incl(consulta.getString("Hr_Incl"));
//                preOrder.setOS(consulta.getString("OS"));
//                preOrder.setChequesQtde(consulta.getString("ChequesQtde"));
//                preOrder.setChequesValor(consulta.getString("ChequesValor"));
//                preOrder.setOperador(consulta.getString("Operador"));
//                preOrder.setDt_Alter(consulta.getString("Dt_Alter"));
//                preOrder.setHr_Alter(consulta.getString("Hr_Alter"));
//                preOrder.setOperExcl(consulta.getString("OperExcl"));
//                preOrder.setDt_Excl(consulta.getString("Dt_Excl"));
//                preOrder.setHr_Excl(consulta.getString("Hr_Excl"));
//                preOrder.setSituacao(consulta.getString("Situacao"));
//                preOrder.setFlag_Excl(consulta.getString("Flag_Excl"));

                retorno.add(preOrder);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PreOrderDao.buscarPreOrder - " + e.getMessage());
        }
    }

    /**
     * Inserer o número do RPV gerado vinculado ao Pedido
     *
     * @param sequencia
     * @param RPV
     * @param guia
     * @param serie
     * @param pedido
     * @param persistencia
     * @throws Exception
     */
    public void adicionaRPV(String sequencia, String RPV, String guia, String serie, String pedido, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE PreOrder SET RPV = ?, Guia = ?, Serie = ?, Pedido = ? WHERE Sequencia = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(RPV);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(pedido);
            consulta.setString(sequencia);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PreorderDao.adicionaRPV - " + e.getMessage());
        }
    }

    /**
     * Busca a lista de PreOrder com informações necessárias para geração de
     * RPV.
     *
     * @param sequencia
     * @param parada
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PreOrderRPV> obterListaPreOrderRPV(String sequencia, String parada, Persistencia persistencia) throws Exception {
        List<PreOrderRPV> retorno = new ArrayList<>();
        try {
            String sql = "Select Rt_Perc.Sequencia, Rt_Perc.Parada, PreOrder.Sequencia PreOrderSeq,\n"
                    + "CliFatX.NRed NRedFat, CliFatX.Ende EndeFat, CliFatX.Bairro BaiFat, CliFatX.Cidade CidFat, \n"
                    + "CliFatX.Estado UFFat, CliFatX.CGC CGCFat, \n"
                    + "CliOriX.NRed NRedOri, CliOriX.Ende EndeOri, CliOriX.Bairro BaiOri, CliOriX.Cidade CidOri, \n"
                    + "CliOriX.Estado UFOri, \n"
                    + "CliDstX.NRed NRedDst, CliDstX.Ende EndeDst, CliDstX.Bairro BaiDst, CliDstX.Cidade CidDst, \n"
                    + "CliDstX.Estado UFDst, \n"
                    + "PreOrder.Lote, PreOrder.Obs, PreOrder.OS, PreOrderVol.Qtde, PreOrderVOL.Lacre, PreOrderVol.Tipo, PreOrderVol.Valor, \n"
                    + "CONVERT(BIGINT,PreOrder.RPV) RPV \n"
                    + "from Rt_Perc \n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "left join PreOrder on PreOrder.DtColeta = Rotas.Data\n"
                    + "                   and PreOrder.CodFil  = Rt_Perc.CodFil\n"
                    + "                   and PreOrder.CodCli1 = Rt_perc.CodCli1\n"
                    + "left join PreOrderVol on PreOrderVol.Sequencia = PreOrder.Sequencia\n"
                    + "left join OS_Vig on  OS_Vig.OS     = PreOrder.OS\n"
                    + "                 and OS_Vig.CodFil = PreOrder.CodFil\n"
                    + "left join Clientes CliFatX on  CliFatX.Codigo = OS_Vig.CliFat\n"
                    + "                           and CliFatX.CodFil = OS_Vig.CodFil\n"
                    + "left join Clientes CliOriX on  CliOriX.Codigo = PreOrder.CodCli1\n"
                    + "                           and CliOriX.CodFil = PreOrder.CodFil\n"
                    + "left join Clientes CliDstX on  CliDstX.Codigo = PreOrder.CodCli2\n"
                    + "                           and CliDstX.CodFil = PreOrder.CodFil\n"
                    + "where Rt_Perc.sequencia = ?\n"
                    + "  and Rt_Perc.Parada    = ?\n"
                    + "  and Rt_Perc.Flag_Excl <> '*'";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();
            PreOrderRPV preOrderRPV;
            while (consulta.Proximo()) {
                preOrderRPV = new PreOrderRPV();
                preOrderRPV.setLote(consulta.getString("Lote"));
                preOrderRPV.setSequencia(consulta.getString("Sequencia"));
                preOrderRPV.setParada(consulta.getString("Parada"));
                preOrderRPV.setPreOrderSeq(consulta.getString("PreOrderSeq"));
                preOrderRPV.setNRedFat(consulta.getString("NRedFat"));
                preOrderRPV.setEndeFat(consulta.getString("EndeFat"));
                preOrderRPV.setCGCFat(consulta.getString("CGCFat"));
                preOrderRPV.setBaiFat(consulta.getString("BaiFat"));
                preOrderRPV.setCidFat(consulta.getString("CidFat"));
                preOrderRPV.setUFFat(consulta.getString("UFFat"));
                preOrderRPV.setNRedOri(consulta.getString("NRedOri"));
                preOrderRPV.setEndeOri(consulta.getString("EndeOri"));
                preOrderRPV.setBaiOri(consulta.getString("BaiOri"));
                preOrderRPV.setCidOri(consulta.getString("CidOri"));
                preOrderRPV.setUFOri(consulta.getString("UFOri"));
                preOrderRPV.setNRedDst(consulta.getString("NRedDst"));
                preOrderRPV.setEndeDst(consulta.getString("EndeDst"));
                preOrderRPV.setBaiDst(consulta.getString("BaiDst"));
                preOrderRPV.setCidDst(consulta.getString("CidDst"));
                preOrderRPV.setUFDst(consulta.getString("UFDst"));
                preOrderRPV.setObs(consulta.getString("Obs"));
                preOrderRPV.setOS(consulta.getString("OS"));
                preOrderRPV.setQtde(consulta.getString("Qtde"));
                preOrderRPV.setLacre(consulta.getString("Lacre"));
                preOrderRPV.setTipo(consulta.getString("Tipo"));
                preOrderRPV.setValor(consulta.getString("Valor"));
                preOrderRPV.setRPV(consulta.getString("RPV"));
                retorno.add(preOrderRPV);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PreOrderDao.obterListaPreOrderRPV - " + e.getMessage() + "\r\n"
                    + "Select Rt_Perc.Sequencia, Rt_Perc.Parada, PreOrder.Sequencia PreOrderSeq,\n"
                    + "CliFatX.NRed NRedFat, CliFatX.Ende EndeFat, CliFatX.Bairro BaiFat, CliFatX.Cidade CidFat, \n"
                    + "CliFatX.Estado UFFat, CliFatX.CGC CGCFat, \n"
                    + "CliOriX.NRed NRedOri, CliOriX.Ende EndeOri, CliOriX.Bairro BaiOri, CliOriX.Cidade CidOri, \n"
                    + "CliOriX.Estado UFOri, \n"
                    + "CliDstX.NRed NRedDst, CliDstX.Ende EndeDst, CliDstX.Bairro BaiDst, CliDstX.Cidade CidDst, \n"
                    + "CliDstX.Estado UFDst, \n"
                    + "PreOrder.Lote, PreOrder.Obs, PreOrder.OS, PreOrderVol.Qtde, PreOrderVOL.Lacre, PreOrderVol.Tipo, PreOrderVol.Valor, \n"
                    + "CONVERT(BIGINT,PreOrder.RPV) RPV \n"
                    + "from Rt_Perc \n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "left join PreOrder on PreOrder.DtColeta = Rotas.Data\n"
                    + "                   and PreOrder.CodFil  = Rt_Perc.CodFil\n"
                    + "                   and PreOrder.CodCli1 = Rt_perc.CodCli1\n"
                    + "left join PreOrderVol on PreOrderVol.Sequencia = PreOrder.Sequencia\n"
                    + "left join OS_Vig on  OS_Vig.OS     = PreOrder.OS\n"
                    + "                 and OS_Vig.CodFil = PreOrder.CodFil\n"
                    + "left join Clientes CliFatX on  CliFatX.Codigo = OS_Vig.CliFat\n"
                    + "                           and CliFatX.CodFil = OS_Vig.CodFil\n"
                    + "left join Clientes CliOriX on  CliOriX.Codigo = PreOrder.CodCli1\n"
                    + "                           and CliOriX.CodFil = PreOrder.CodFil\n"
                    + "left join Clientes CliDstX on  CliDstX.Codigo = PreOrder.CodCli2\n"
                    + "                           and CliDstX.CodFil = PreOrder.CodFil\n"
                    + "where Rt_Perc.sequencia = ?\n"
                    + "  and Rt_Perc.Parada    = ?\n"
                    + "  and Rt_Perc.Flag_Excl <> '*'");
        }
    }

    public List<Rt_Guias> obterListaEGTV(String sequencia, String parada, Persistencia persistencia) throws Exception {
        List<Rt_Guias> retorno = new ArrayList<>();
        try {
            String sql = "Select RPV.Guia, RPV.Serie, Rt_Perc.HrCheg, Rt_Perc.HrSaida, EscalaOri.Veiculo, EscalaOri.Rota,"
                    + " convert(DATE,Rt_Perc.Dt_Fech) coletaOri,\n"
                    + "Rt_Perc.Sequencia, Rt_Perc.Parada, PreOrder.CodFil, PreOrder.Sequencia PreOrderSeq,\n"
                    + "CliFatX.NRed NRedFat, CliFatX.Nome NomeFat, CliFatX.Ende EndeFat, CliFatX.Bairro BaiFat, CliFatX.Cidade CidFat, CliFatX.Estado UFFat, CliFatX.CGC CGCFat,\n"
                    + "CliOriX.NRed NRedOri, CliOriX.Ende EndeOri, CliOriX.Bairro BaiOri, CliOriX.Cidade CidOri, CliOriX.Estado UFOri,\n"
                    + "CliDstX.NRed NRedDst, CliDstX.Ende EndeDst, CliDstX.Bairro BaiDst, CliDstX.Cidade CidDst, CliDstX.Estado UFDst,\n"
                    + "PreOrder.Obs, PreOrder.OS, PreOrderVol.Qtde, PreOrderVOL.Lacre, PreOrderVol.Tipo, PreOrderVol.Valor, PreOrder.RPV, \n"
                    + " XMLGTVE.ChaveGTVE ChaveGTVE, \n"
                    + " XMLGTVE.Protocolo ProtocoloGTVE, \n"
                    + " XMLGTVE.Link LinkGTVE \n"
                    + "from Rt_Perc \n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "left join PreOrder on PreOrder.DtColeta = Rotas.Data\n"
                    + "                   and PreOrder.CodFil  = Rt_Perc.CodFil\n"
                    + "                   and PreOrder.CodCli1 = Rt_perc.CodCli1\n"
                    + "left join PreOrderVol on PreOrderVol.Sequencia = PreOrder.Sequencia\n"
                    + "LEFT JOIN Escala EscalaOri ON EscalaOri.SeqRota = Rt_Perc.Sequencia\n"
                    + "left join RPV on RPV.RPV = PreOrder.RPV\n"
                    + "          and RPV.parada = Rt_Perc.parada\n"
                    + "left join OS_Vig on  OS_Vig.OS     = PreOrder.OS\n"
                    + "                 and OS_Vig.CodFil = PreOrder.CodFil\n"
                    + "left join Clientes CliFatX on  CliFatX.Codigo = OS_Vig.CliFat\n"
                    + "                           and CliFatX.CodFil = OS_Vig.CodFil\n"
                    + "left join Clientes CliOriX on  CliOriX.Codigo = PreOrder.CodCli1\n"
                    + "                           and CliOriX.CodFil = PreOrder.CodFil\n"
                    + "left join Clientes CliDstX on  CliDstX.Codigo = PreOrder.CodCli2\n"
                    + "                           and CliDstX.CodFil = PreOrder.CodFil\n"
                    + " LEFT JOIN XMLGTVE on RPV.Guia = XMLGTVE.Guia AND RPV.Serie = XMLGTVE.Serie \n"
                    + "where Rt_Perc.sequencia = ?\n"
                    + "  and Rt_Perc.Parada    = ?\n"
                    + "  and Rt_Perc.Flag_Excl <> '*'\n"
                    + "  and RPV.Flag_Excl <> '*'\n"
                    + "  and RPV.Guia is not null";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();
            Rt_Guias preOrderRPV;
            while (consulta.Proximo()) {
                preOrderRPV = new Rt_Guias();
                preOrderRPV.setSequencia(consulta.getString("Sequencia"));
                preOrderRPV.setParada(consulta.getInt("Parada"));
//                rt_Guias.setCodCli1(consulta.getString("CodCli1"));
//                rt_Guias.setCodCli2(consulta.getString("CodCli2"));
                preOrderRPV.setGuia(consulta.getString("Guia"));
                preOrderRPV.setSerie(consulta.getString("Serie"));
//                rt_Guias.setSerieAnt(consulta.getString("serieant"));
                preOrderRPV.setCodFil(consulta.getString("CodFil"));
                preOrderRPV.setValor(consulta.getString("Valor"));
                preOrderRPV.setOS(consulta.getString("OS"));
//                rt_Guias.setKM(consulta.getString("km"));
//                rt_Guias.setKMTerra(consulta.getString("KMterra"));
//                rt_Guias.setOperador(consulta.getString("operador"));

                preOrderRPV.setnRedOri(consulta.getString("NRedOri"));
                preOrderRPV.setEndOri(consulta.getString("EndeOri"));
                preOrderRPV.setCidadeOri(consulta.getString("CidOri"));
                preOrderRPV.setBairroOri(consulta.getString("BaiOri"));
                preOrderRPV.setEstadoOri(consulta.getString("UFOri"));
//                rt_Guias.setEmailOri(consulta.getString("EmailOri"));
                preOrderRPV.setVeiculoOri(consulta.getString("Veiculo"));
                preOrderRPV.setRotaOri(consulta.getString("Rota"));
                preOrderRPV.setColetaOri(consulta.getString("coletaOri"));

//                rt_Guias.setCodCliDst(consulta.getString("CodCliDst"));
                preOrderRPV.setBairroDst(consulta.getString("BaiDst"));
                preOrderRPV.setEstadoDst(consulta.getString("UFDst"));
                preOrderRPV.setnRedDst(consulta.getString("NRedDst"));
                preOrderRPV.setEndDst(consulta.getString("EndeDst"));
                preOrderRPV.setCidadeDst(consulta.getString("CidDst"));
//                rt_Guias.setCidadeDst(consulta.getString("CidadeDst"));
//                rt_Guias.setEmailDst(consulta.getString("EmailDst"));
//                rt_Guias.setCepDst(consulta.getString("CepDst"));
//                rt_Guias.setVeiculoDst(consulta.getString("veiculoDst"));
//                rt_Guias.setRotaDst(consulta.getString("rotaDst"));
//                rt_Guias.setColetaDst(consulta.getString("coletaDst"));

                preOrderRPV.setnRedFat(consulta.getString("NRedFat"));
//                rt_Guias.setCodCliFat(consulta.getString("CodCliFat"));
                preOrderRPV.setNomeFat(consulta.getString("NomeFat"));
                preOrderRPV.setEndFat(consulta.getString("EndeFat"));
                preOrderRPV.setCidadeFat(consulta.getString("CidFat"));
                preOrderRPV.setBairroFat(consulta.getString("BaiFat"));
                preOrderRPV.setEstadoFat(consulta.getString("UFFat"));
                preOrderRPV.setCgcFat(consulta.getString("CGCFat"));
//                rt_Guias.setIeFat(consulta.getString("IEFat"));
//                rt_Guias.setEmailFat(consulta.getString("EmailFat"));

                preOrderRPV.setHoraChegada(consulta.getString("HrCheg"));
                preOrderRPV.setHoraSaida(consulta.getString("HrSaida"));
//                rt_Guias.setHora1(consulta.getString("hora1"));
//                rt_Guias.setHora2(consulta.getString("hora2"));

//                rt_Guias.setEr(consulta.getString("er"));
                preOrderRPV.setTipoSrv("");
                preOrderRPV.setObserv(consulta.getString("Obs"));
                
                preOrderRPV.setGTVeChave(consulta.getString("ChaveGTVE"));
                preOrderRPV.setGTVeLink(consulta.getString("LinkGTVE"));
                preOrderRPV.setGTVeProtocolo(consulta.getString("ProtocoloGTVE"));
                
                preOrderRPV.setLacres(new ArrayList<>());
                CxFGuiasVol lacre = new CxFGuiasVol();
                lacre.setLacre(consulta.getString("Lacre"));
                preOrderRPV.getLacres().add(lacre);
                retorno.add(preOrderRPV);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PreOrderDao.obterListaEGTV - " + e.getMessage());
        }
    }

    /**
     * Atualiza as informações de codcli2 e OS no dia.
     *
     * @param persistencia
     * @throws Exception
     */
    public void atualizarCodCli2OS(Persistencia persistencia) throws Exception {
        try {
            String sql = "update preorder set CodCli2 = (Select top 1 Codigo from Clientes "
                    + " where Clientes.Situacao = 'A' and clientes.Agencia = PreOrder.Agencia "
                    + "         and Clientes.SubAgencia = PreOrder.SubAgencia and Clientes.Codfil = PreOrder.CodFil)\n"
                    + "where PreOrder.DtColeta >= Convert(Date, GetDate())\n"
                    + "  and (PreOrder.CodCli2 = '' or PreOrder.CodCLi2 is null)\n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.update();

            sql = "update preorder set OS = (Select top 1 OS from OS_Vig where OS_Vig.Situacao = 'A' and OS_Vig.Cliente = PreOrder.CodCli2 "
                    + "and OS_Vig.CodFil = PreOrder.CodFil and DtFim >= GetDate())\n"
                    + "where PreOrder.DtColeta >= Convert(Date, GetDate())\n"
                    + "  and (PreOrder.OS = '' or PreOrder.OS is null)";
            consulta = new Consulta(sql, persistencia);
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("PreOrderDao.atualizarCodCli2OS - " + e.getMessage());
        }
    }

    /**
     * Atualiza as informações de codcli2 e OS no dia.
     *
     * @param persistencia
     * @throws Exception
     */
    public void atualizarOS(Persistencia persistencia) throws Exception {
        try {
            String sql = "Declare @data date;\n"
                    + "Declare @sequencia int;\n"
                    + "Declare @os int;\n"
                    + "Declare @codfil int;\n"
                    + "Declare @cxforte Varchar(07);\n"
                    + "Declare @cxforteNred Varchar(30);\n"
                    + "Declare @cxforteRegiao Varchar(03);\n"
                    + "Declare @codcli1 Varchar(07);\n"
                    + "Declare @codcli2 Varchar(07);\n"
                    + "-------------------------------------------------\n"
                    + "--Defina parametros------------------------------\n"
                    + "set @data = Convert(Date,Getdate());\n"
                    + "-------------------------------------------------\n"
                    + "set @cxforte = ''; \n"
                    + "Select * into #preOrder from PreOrder\n"
                    + " where PreOrder.DtColeta >= Convert(Date, GetDate())\n"
                    + "  and (PreOrder.OS = '' or PreOrder.OS is null)\n"
                    + "\n"
                    + "WHILE (SELECT count(*) FROM #preOrder) > 0\n"
                    + "BEGIN  \n"
                    + "	Select top 01 \n"
                    + "	@sequencia = Sequencia,  \n"
                    + "	@codcli1 = CodCli1,\n"
                    + "	@codcli2 = CodCli2,\n"
                    + "	@codfil = CodFil\n"
                    + "	From #PreOrder;\n"
                    + "\n"
                    //                    + "if(len(@cxforte) <= 1) begin \n"
                    + "     Select top 01 \n"
                    + "     @cxforte = Clientes.Codigo, \n"
                    + "     @cxforteNred = Clientes.Nred, \n"
                    + "     @cxforteRegiao = Regiao \n"
                    + "     from CxForte \n"
                    + "     Left join Clientes  on Clientes.Codigo = CxForte.CodCli \n"
                    + "                        and Clientes.CodFil = CxForte.CodFil \n"
                    + "     where CxForte.CodFil = @codfil Order By DtFecha Desc; \n"
                    //                    + "end \n"
                    + "\n"
                    + "	Select top 1 @os = OS\n"
                    + "	from OS_Vig \n"
                    + "	where CodFil = @codfil\n"
                    + "	  and DtFim >= @data\n"
                    + "	  and Situacao = 'A' \n"
                    + "	  and TipoOS in (1,2,6)\n"
                    + "	  and (((Cliente = @codcli1) and (CliDst = @codcli2)) or ((Cliente = @codcli2) and (CliDst = @codcli1)))\n"
                    + "	Order by OS Desc;\n"
                    + "\n"
                    + "	Update PreOrder set OS = @os where Sequencia = @sequencia;\n"
                    + "	\n"
                    + "	if(Substring(@codcli1,4,1) = 7)\n"
                    + "		Update PreOrder set Codcli1 = @cxforte where Sequencia = @sequencia;\n"
                    + "\n"
                    + "	if(Substring(@codcli2,4,1) = 7)\n"
                    + "		Update PreOrder set Codcli2 = @cxforte where Sequencia = @sequencia;\n"
                    + "\n"
                    + "	Delete from #preOrder where Sequencia = @sequencia;\n"
                    + "   IF (SELECT count(*) FROM #preOrder) = 0\n"
                    + "      BREAK  \n"
                    + "   ELSE  \n"
                    + "      CONTINUE  \n"
                    + "END\n"
                    + "Drop table #preOrder; \n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("PreOrderDao.atualizarOS - " + e.getMessage());
        }
    }

    /**
     * Retorna a lista paginada de pedidos
     *
     * @param primeiro
     * @param linhas
     * @param filtros
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PreOrder> buscarPreOrderPaginado(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            Map<String, String> filtro = filtros;
            String sql = "SELECT  * "
                    + " FROM    ( SELECT    ROW_NUMBER() OVER ( ORDER BY PreOrder.PedidoCliente Desc ) AS RowNum, \n"
                    + "   PreOrder.PedidoCliente, PreOrder.Codfil, PreOrder.DtColeta, PreOrder.Hora1O, PreOrder.Hora2O,\n"
                    + " PreOrder.Agencia, PreOrder.SubAgencia, PreOrder.DtEntrega, PreOrder.Hora1D, PreOrder.Hora2D,\n"
                    + " SUM(PreOrder.Valor) Valor, PreOrder.Operador, PreOrder.Dt_Alter, PreOrder.Hr_Alter, PreORder.Banco, \n"
                    + "CliFatX.IE IEFat, CliFatX.NRed NRedFat, CliFatX.Ende EndeFat, CliFatX.Bairro BaiFat, CliFatX.Cidade CidFat, \n"
                    + "CliFatX.Estado UFFat, CliFatX.CGC CGCFat, \n"
                    + "  CliOriX.NRed NRed1, CliDstX.NRed NRed2, CliOriX.Agencia AgenciaOri, CliOriX.SubAgencia SubAgenciaOri, \n"
                    + "CliOriX.Ende EndeOri, CliOriX.Bairro BaiOri, CliOriX.Cidade CidOri, CliOriX.Estado UFOri, \n"
                    + "CliDstX.Ende EndeDst, CliDstX.Bairro BaiDst, CliDstX.Cidade CidDst, CliDstX.Estado UFDst, \n"
                    + " PessoaRec.Nome Assinatura, PessoaEnt.Nome AssinaturaDestino,\n"
                    + "Rotas.Data ColetaOri, Rotas.Rota RotaOri, \n"
                    + " MIN(Rt_Perc.HrCheg) Hora1, MAX(Rt_Perc.HrSaida) Hora2, \n"
                    + "RotaDest.Data ColetaDst, RotaDest.Rota RotaDst,\n"
                    + "MIN(Rt_PercDest.HrCheg) HoraChegada, MAX(Rt_PercDest.HrSaida) HoraSaida, \n"
                    + " OS_Vig.OS, Escala.Veiculo VeiculoOri, EscalaDst.Veiculo VeiculoDst \n"
                    + " FROM PreOrder \n"
                    + " LEFT JOIN PreOrderVol on PreOrderVol.Sequencia = PreOrder.Sequencia \n"
                    + "                       and PreOrderVol.CodFil   = PreOrder.CodFil \n"
                    + "left join OS_Vig on  OS_Vig.OS     = PreOrder.OS\n"
                    + "                 and OS_Vig.CodFil = PreOrder.CodFil\n"
                    + "left join Clientes CliFatX on  CliFatX.Codigo = OS_Vig.CliFat\n"
                    + "                           and CliFatX.CodFil = OS_Vig.CodFil\n"
                    + " LEFT JOIN Clientes CliOriX on CliOriX.Codigo = PreOrder.CodCli1 \n"
                    + "                          and CliOriX.CodFil = PreOrder.CodFil \n"
                    + " LEFT JOIN Clientes CliDstX on CliDstX.Codigo = PreOrder.CodCli2 \n"
                    + "                          and CliDstX.CodFil = PreOrder.CodFil \n"
                    + "LEFT JOIN RPV ON RPV.RPV = PreOrder.RPV\n"
                    + "left join Rt_Perc on Rt_Perc.Sequencia = RPV.SeqRota\n"
                    + "                and Rt_Perc.Parada = RPV.Parada\n"
                    + "                    and Rt_Perc.Flag_Excl <> '*'\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "                and Rotas.CodFil = Rt_Perc.CodFil\n"
                    + "LEFT JOin Escala on Escala.CodFil = Rotas.CodFil\n"
                    + "                and Escala.SeqRota = Rotas.Sequencia\n"
                    + "Left join CxfGuias  on CxfGuias.Guia = RPV.Guia\n"
                    + "                   and CxfGuias.Serie = RPV.Serie\n"
                    + "Left join Rt_Perc Rt_PercDest  on  Rt_PercDest.Sequencia = CxfGuias.SeqRotaSai\n"
                    + "                              and Rt_PercDest.Hora1 = CxfGuias.Hora1D\n"
                    + "                              and Rt_PercDest.Flag_Excl <> '*'\n"
                    + "Left join Rotas RotaDest on RotaDest.Sequencia = CxfGuias.SeqRotaSai\n"
                    + "LEFT JOin Escala EscalaDst on EscalaDst.CodFil = RotaDest.CodFil\n"
                    + "                and EscalaDst.SeqRota = RotaDest.Sequencia\n"
                    + "left join Pessoa PessoaRec on PessoaRec.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota and Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = RPV.Guia and RPVX.Serie = RPV.Serie and Rt_Perc.ER = 'R')\n"
                    + "left join Pessoa PessoaEnt on PessoaEnt.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota and Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = RPV.Guia and RPVX.Serie = RPV.Serie and Rt_Perc.ER = 'E') "
                    + " WHERE PreOrder.Sequencia > 0 \n";
            if (filtro.get("CodCli").equals("")) {
                sql += " and"
                        + "(OS_Vig.Cliente in "
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? and PessoaCliAut.CodFil = Rotas.CodFil) "
                        + " OR OS_Vig.clidst in "
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? and PessoaCliAut.CodFil = Rotas.CodFil))";
            } else {
                sql += " and  (OS_Vig.Cliente = ? or OS_Vig.CliDst = ?) \n";
            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            sql = sql + "GROUP BY PreOrder.PedidoCliente, PreOrder.DtColeta, PreOrder.Hora1O, PreOrder.Hora2O, PreOrder.Codfil,\n"
                    + " PreOrder.Agencia, PreOrder.SubAgencia, PreOrder.DtEntrega, PreOrder.Hora1D, PreOrder.Hora2D,\n"
                    + " PreOrder.Operador, PreOrder.Dt_Alter, PreOrder.Hr_Alter, PreORder.Banco, \n"
                    + "CliFatX.NRed, CliFatX.Ende, CliFatX.Bairro, CliFatX.Cidade, \n"
                    + "CliFatX.Estado, CliFatX.CGC, \n"
                    + "CliOriX.NRed, CliDstX.NRed, CliOriX.Agencia, CliOriX.SubAgencia, \n"
                    + "CliOriX.Ende, CliOriX.Bairro, CliOriX.Cidade, CliOriX.Estado, \n"
                    + "CliDstX.Ende, CliDstX.Bairro, CliDstX.Cidade, CliDstX.Estado,"
                    + " PessoaRec.Nome, PessoaEnt.Nome,  Rotas.Data, Rotas.Rota, \n"
                    + "RotaDest.Data, RotaDest.Rota, CliFatX.IE, OS_Vig.OS, Escala.Veiculo, EscalaDst.Veiculo)\n"
                    + " AS RowConstrainedResult "
                    + "WHERE   RowNum >= ?"
                    + "    AND RowNum < ? "
                    + "ORDER BY RowNum";
            Consulta consulta = new Consulta(sql, persistencia);
            if (filtro.get("CodCli").equals("")) {
                consulta.setString(filtro.get("CodPessoa"));
                consulta.setString(filtro.get("CodPessoa"));
            } else {
                consulta.setString(filtro.get("CodCli"));
                consulta.setString(filtro.get("CodCli"));
            }

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();

            List<PreOrder> retorno = new ArrayList<>();
            while (consulta.Proximo()) {
                PreOrder preOrder = new PreOrder();
//                preOrder.setSequencia(consulta.getString("Sequencia"));
                preOrder.setCodFil(consulta.getString("CodFil"));
                preOrder.setBanco(consulta.getString("Banco"));
                preOrder.setAgencia(consulta.getString("Agencia"));
                preOrder.setSubAgencia(consulta.getString("SubAgencia"));
//                preOrder.setRPV(consulta.getString("RPVPreOrder"));
                preOrder.setDtColeta(consulta.getString("DtColeta"));
                preOrder.setDtEntrega(consulta.getString("DtEntrega"));
//                preOrder.setCodCli1(consulta.getString("CodCli1"));
                preOrder.setNRed1(consulta.getString("NRed1"));
                preOrder.setHora1O(consulta.getString("Hora1O"));
                preOrder.setHora2O(consulta.getString("Hora2O"));
//                preOrder.setCodCli2(consulta.getString("CodCli2"));
                preOrder.setAgenciaOri(consulta.getString("AgenciaOri"));
                preOrder.setSubAgenciaOri(consulta.getString("SubAgenciaOri"));
                preOrder.setNRed2(consulta.getString("NRed2"));
                preOrder.setHora1D(consulta.getString("Hora1D"));
                preOrder.setHora2D(consulta.getString("Hora2D"));
//                preOrder.setSolicitante(consulta.getString("Solicitante"));
                preOrder.setPedidoCliente(consulta.getString("PedidoCliente"));
                preOrder.setValor(consulta.getString("Valor"));
//                preOrder.setObs(consulta.getString("Obs"));
//                preOrder.setClassifSrv(consulta.getString("ClassifSrv"));
//                preOrder.setOperIncl(consulta.getString("OperIncl"));
//                preOrder.setDt_Incl(consulta.getString("Dt_Incl"));
//                preOrder.setHr_Incl(consulta.getString("Hr_Incl"));
                preOrder.setOS(consulta.getString("OS"));
//                preOrder.setChequesQtde(consulta.getString("ChequesQtde"));
//                preOrder.setChequesValor(consulta.getString("ChequesValor"));
                preOrder.setOperador(consulta.getString("Operador"));
                preOrder.setDt_Alter(consulta.getString("Dt_Alter"));
                preOrder.setHr_Alter(consulta.getString("Hr_Alter"));
//                preOrder.setOperExcl(consulta.getString("OperExcl"));
//                preOrder.setDt_Excl(consulta.getString("Dt_Excl"));
//                preOrder.setHr_Excl(consulta.getString("Hr_Excl"));
//                preOrder.setSituacao(consulta.getString("Situacao"));
//                preOrder.setFlag_Excl(consulta.getString("Flag_Excl"));
//                preOrder.setGuia(consulta.getString("GuiaPreOrder"));
//                preOrder.setSerie(consulta.getString("Serie"));
//                preOrder.setPedido(consulta.getString("Pedido"));

//                preOrder.setPreOrderVolOrdem(consulta.getString("Ordem"));
//                preOrder.setPreOrderVolQtde(consulta.getString("Qtde"));
//                preOrder.setPreOrderVolLacre(consulta.getString("Lacre"));
//                preOrder.setPreOrderVolTipo(consulta.getString("Tipo"));
//                preOrder.setPreOrderVolValor(consulta.getString("ValorLacre"));
                preOrder.setIEFat(consulta.getString("IEFat"));
                preOrder.setNRedFat(consulta.getString("NRedFat"));
                preOrder.setEndeFat(consulta.getString("EndeFat"));
                preOrder.setCGCFat(consulta.getString("CGCFat"));
                preOrder.setBaiFat(consulta.getString("BaiFat"));
                preOrder.setCidFat(consulta.getString("CidFat"));
                preOrder.setUFFat(consulta.getString("UFFat"));
                preOrder.setEndeOri(consulta.getString("EndeOri"));
                preOrder.setBaiOri(consulta.getString("BaiOri"));
                preOrder.setCidOri(consulta.getString("CidOri"));
                preOrder.setUFOri(consulta.getString("UFOri"));
                preOrder.setEndeDst(consulta.getString("EndeDst"));
                preOrder.setBaiDst(consulta.getString("BaiDst"));
                preOrder.setCidDst(consulta.getString("CidDst"));
                preOrder.setUFDst(consulta.getString("UFDst"));

                preOrder.setAssinatura(consulta.getString("Assinatura"));
                preOrder.setAssinaturaDestino(consulta.getString("AssinaturaDestino"));

                preOrder.setColetaOri(consulta.getString("ColetaOri"));
                preOrder.setVeiculoOri(consulta.getString("VeiculoOri"));
                preOrder.setRotaOri(consulta.getString("RotaOri"));
                preOrder.setHora1(consulta.getString("Hora1"));
                preOrder.setHora2(consulta.getString("Hora2"));
                preOrder.setColetaDst(consulta.getString("ColetaDst"));
                preOrder.setVeiculoDst(consulta.getString("VeiculoDst"));
                preOrder.setRotaDst(consulta.getString("RotaDst"));
                preOrder.setHoraChegada(consulta.getString("HoraChegada"));
                preOrder.setHoraSaida(consulta.getString("HoraSaida"));
                retorno.add(preOrder);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PreOrderDao.buscarPreOrderPaginado - " + e.getMessage());
        }
    }

    public List<PreOrder> buscarDetalhesPreOrder(String pedidoCliente, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT CONVERT(BigInt, PreOrder.Guia) GuiaPreOrder, CONVERT(BigInt, PreOrder.RPV) RPVPreOrder, PreOrder.*, \n"
                    + "PreOrderVol.Ordem, PreOrderVol.Qtde, PreOrderVol.Lacre, PreOrderVol.Tipo, PreOrderVol.Valor ValorLacre,\n"
                    + "Cliori.NRed NRed1, CliDst.NRed NRed2, Cliori.Agencia AgenciaOri, Cliori.SubAgencia SubAgenciaOri \n"
                    + "FROM PreOrder \n"
                    + "LEFT JOIN PreOrderVol on PreOrderVol.Sequencia = PreOrder.Sequencia \n"
                    + "                     and PreOrderVol.CodFil   = PreOrder.CodFil \n"
                    + "LEFT JOIN Clientes Cliori on Cliori.Codigo = PreOrder.CodCli1 \n"
                    + "                        and Cliori.CodFil = PreOrder.CodFil \n"
                    + "LEFT JOIN Clientes CliDst on CliDst.Codigo = PreOrder.CodCli2 \n"
                    + "                        and CliDst.CodFil = PreOrder.CodFil \n"
                    + "WHERE PreOrder.PedidoCliente = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pedidoCliente);
            consulta.select();

            List<PreOrder> retorno = new ArrayList<>();
            while (consulta.Proximo()) {
                PreOrder preOrder = new PreOrder();
                preOrder.setSequencia(consulta.getString("Sequencia"));
                preOrder.setCodFil(consulta.getString("CodFil"));
                preOrder.setBanco(consulta.getString("Banco"));
                preOrder.setAgencia(consulta.getString("Agencia"));
                preOrder.setSubAgencia(consulta.getString("SubAgencia"));
                preOrder.setRPV(consulta.getString("RPVPreOrder"));
                preOrder.setDtColeta(consulta.getString("DtColeta"));
                preOrder.setDtEntrega(consulta.getString("DtEntrega"));
                preOrder.setCodCli1(consulta.getString("CodCli1"));
                preOrder.setNRed1(consulta.getString("NRed1"));
                preOrder.setHora1O(consulta.getString("Hora1O"));
                preOrder.setHora2O(consulta.getString("Hora2O"));
                preOrder.setCodCli2(consulta.getString("CodCli2"));
                preOrder.setAgenciaOri(consulta.getString("AgenciaOri"));
                preOrder.setSubAgenciaOri(consulta.getString("SubAgenciaOri"));
                preOrder.setNRed2(consulta.getString("NRed2"));
                preOrder.setHora1D(consulta.getString("Hora1D"));
                preOrder.setHora2D(consulta.getString("Hora2D"));
                preOrder.setSolicitante(consulta.getString("Solicitante"));
                preOrder.setPedidoCliente(consulta.getString("PedidoCliente"));
                preOrder.setValor(consulta.getString("Valor"));
                preOrder.setObs(consulta.getString("Obs"));
                preOrder.setClassifSrv(consulta.getString("ClassifSrv"));
                preOrder.setOperIncl(consulta.getString("OperIncl"));
                preOrder.setDt_Incl(consulta.getString("Dt_Incl"));
                preOrder.setHr_Incl(consulta.getString("Hr_Incl"));
                preOrder.setOS(consulta.getString("OS"));
                preOrder.setChequesQtde(consulta.getString("ChequesQtde"));
                preOrder.setChequesValor(consulta.getString("ChequesValor"));
                preOrder.setOperador(consulta.getString("Operador"));
                preOrder.setDt_Alter(consulta.getString("Dt_Alter"));
                preOrder.setHr_Alter(consulta.getString("Hr_Alter"));
                preOrder.setOperExcl(consulta.getString("OperExcl"));
                preOrder.setDt_Excl(consulta.getString("Dt_Excl"));
                preOrder.setHr_Excl(consulta.getString("Hr_Excl"));
                preOrder.setSituacao(consulta.getString("Situacao"));
                preOrder.setFlag_Excl(consulta.getString("Flag_Excl"));
                preOrder.setGuia(consulta.getString("GuiaPreOrder"));
                preOrder.setSerie(consulta.getString("Serie"));
                preOrder.setPedido(consulta.getString("Pedido"));

                preOrder.setPreOrderVolOrdem(consulta.getString("Ordem"));
                preOrder.setPreOrderVolQtde(consulta.getString("Qtde"));
                preOrder.setPreOrderVolLacre(consulta.getString("Lacre"));
                preOrder.setPreOrderVolTipo(consulta.getString("Tipo"));
                preOrder.setPreOrderVolValor(consulta.getString("ValorLacre"));
                retorno.add(preOrder);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PreOrderDao.buscarDetalhesPreOrder - " + e.getMessage());
        }
    }

    public Integer totalPreOrder(Map filtros, Persistencia persistencia) throws Exception {
        try {
            Map<String, String> filtro = filtros;
            String sql = "select count(Distinct PedidoCliente) total \n"
                    + " FROM PreOrder \n"
                    + " LEFT JOIN PreOrderVol on PreOrderVol.Sequencia = PreOrder.Sequencia \n"
                    + "                       and PreOrderVol.CodFil   = PreOrder.CodFil \n"
                    + "left join OS_Vig on  OS_Vig.OS     = PreOrder.OS\n"
                    + "                 and OS_Vig.CodFil = PreOrder.CodFil\n"
                    + " WHERE PreOrder.Sequencia > 0 \n";

            if (filtro.get("CodCli").equals("")) {
                sql += " and"
                        + "(OS_Vig.Cliente in "
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? and PessoaCliAut.CodFil = PreOrder.CodFil) "
                        + " OR OS_Vig.clidst in "
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? and PessoaCliAut.CodFil = PreOrder.CodFil))";
            } else {
                sql += " and  (OS_Vig.Cliente = ? or OS_Vig.CliDst = ?) \n";
            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }

            Consulta consulta = new Consulta(sql, persistencia);

            if (filtro.get("CodCli").equals("")) {
                consulta.setString(filtro.get("CodPessoa"));
                consulta.setString(filtro.get("CodPessoa"));
            } else {
                consulta.setString(filtro.get("CodCli"));
                consulta.setString(filtro.get("CodCli"));
            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.select();
            int retorno = 0;
            while (consulta.Proximo()) {
                retorno = consulta.getInt("total");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PreORderDao.totalPreOrder - " + e.getMessage());
        }
    }

    /**
     * Retorna o pedido pelo lacre
     *
     * @param lacre
     * @param CodFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PreOrder> buscarPreOrder(String lacre, String CodFil, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT PreOrder.*, PreOrderVol.Ordem, PreOrderVol.Qtde, PreOrderVol.Lacre, PreOrderVol.Tipo, PreOrderVol.Valor ValorLacre "
                    + " FROM PreOrder "
                    + " LEFT JOIN PreOrderVol on PreOrderVol.Sequencia = PreOrder.Sequencia "
                    + "                       and PreOrderVol.CodFil   = PreOrder.CodFil "
                    + " WHERE PreOrderVol.Lacre = ? "
                    + "   AND PreOrderVol.CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(lacre);
            consulta.setString(CodFil);
            consulta.select();
            List<PreOrder> retorno = new ArrayList<>();
            while (consulta.Proximo()) {
                PreOrder preOrder = new PreOrder();
                preOrder.setSequencia(consulta.getString("Sequencia"));
                preOrder.setCodFil(consulta.getString("CodFil"));
                preOrder.setBanco(consulta.getString("Banco"));
                preOrder.setAgencia(consulta.getString("Agencia"));
                preOrder.setSubAgencia(consulta.getString("SubAgencia"));
                preOrder.setRPV(consulta.getString("RPV"));
                preOrder.setDtColeta(consulta.getString("DtColeta"));
                preOrder.setDtEntrega(consulta.getString("DtEntrega"));
                preOrder.setCodCli1(consulta.getString("CodCli1"));
                preOrder.setHora1O(consulta.getString("Hora1O"));
                preOrder.setHora2O(consulta.getString("Hora2O"));
                preOrder.setCodCli2(consulta.getString("CodCli2"));
                preOrder.setHora1D(consulta.getString("Hora1D"));
                preOrder.setHora2D(consulta.getString("Hora2D"));
                preOrder.setSolicitante(consulta.getString("Solicitante"));
                preOrder.setPedidoCliente(consulta.getString("PedidoCliente"));
                preOrder.setValor(consulta.getString("Valor"));
                preOrder.setObs(consulta.getString("Obs"));
                preOrder.setClassifSrv(consulta.getString("ClassifSrv"));
                preOrder.setOperIncl(consulta.getString("OperIncl"));
                preOrder.setDt_Incl(consulta.getString("Dt_Incl"));
                preOrder.setHr_Incl(consulta.getString("Hr_Incl"));
                preOrder.setOS(consulta.getString("OS"));
                preOrder.setChequesQtde(consulta.getString("ChequesQtde"));
                preOrder.setChequesValor(consulta.getString("ChequesValor"));
                preOrder.setOperador(consulta.getString("Operador"));
                preOrder.setDt_Alter(consulta.getString("Dt_Alter"));
                preOrder.setHr_Alter(consulta.getString("Hr_Alter"));
                preOrder.setOperExcl(consulta.getString("OperExcl"));
                preOrder.setDt_Excl(consulta.getString("Dt_Excl"));
                preOrder.setHr_Excl(consulta.getString("Hr_Excl"));
                preOrder.setSituacao(consulta.getString("Situacao"));
                preOrder.setFlag_Excl(consulta.getString("Flag_Excl"));

                retorno.add(preOrder);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PreOrderDao.buscarPreOrder - " + e.getMessage());
        }
    }

    /**
     * Retorna o pedido pelo lacre
     *
     * @param sequencia
     * @param CodFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public PreOrder getPreOrder(String sequencia, String CodFil, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT PreOrder.*, PreOrderVol.Ordem, PreOrderVol.Qtde, PreOrderVol.Lacre, PreOrderVol.Tipo, \n"
                    + " PreOrderVol.Valor ValorLacre, preordervol.Obs ObsLacre, "
                    + " CliDst.NRed NRed2 "
                    + " FROM PreOrder "
                    + " LEFT JOIN PreOrderVol on PreOrderVol.Sequencia = PreOrder.Sequencia "
                    + "                       and PreOrderVol.CodFil   = PreOrder.CodFil "
                    + " LEFT JOIN Clientes CliDst on CliDst.Codigo = PreOrder.CodCli2 "
                    + "                       and CliDst.CodFil   = PreOrder.CodFil "
                    + " WHERE PreOrder.sequencia = ? "
                    + "   AND PreOrder.CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(CodFil);
            consulta.select();
            PreOrder preOrder = new PreOrder();
            while (consulta.Proximo()) {
                preOrder.setSequencia(consulta.getString("Sequencia"));
                preOrder.setCodFil(consulta.getString("CodFil"));
                preOrder.setBanco(consulta.getString("Banco"));
                preOrder.setAgencia(consulta.getString("Agencia"));
                preOrder.setSubAgencia(consulta.getString("SubAgencia"));
                preOrder.setRPV(consulta.getString("RPV"));
                preOrder.setDtColeta(consulta.getString("DtColeta"));
                preOrder.setDtEntrega(consulta.getString("DtEntrega"));
                preOrder.setCodCli1(consulta.getString("CodCli1"));
                preOrder.setHora1O(consulta.getString("Hora1O"));
                preOrder.setHora2O(consulta.getString("Hora2O"));
                preOrder.setCodCli2(consulta.getString("CodCli2"));
                preOrder.setNRed2(consulta.getString("NRed2"));
                preOrder.setHora1D(consulta.getString("Hora1D"));
                preOrder.setHora2D(consulta.getString("Hora2D"));
                preOrder.setSolicitante(consulta.getString("Solicitante"));
                preOrder.setPedidoCliente(consulta.getString("PedidoCliente"));
                preOrder.setValor(consulta.getString("Valor"));
                preOrder.setObs(consulta.getString("Obs"));
                preOrder.setClassifSrv(consulta.getString("ClassifSrv"));
                preOrder.setOperIncl(consulta.getString("OperIncl"));
                preOrder.setDt_Incl(consulta.getString("Dt_Incl"));
                preOrder.setHr_Incl(consulta.getString("Hr_Incl"));
                preOrder.setOS(consulta.getString("OS"));
                preOrder.setChequesQtde(consulta.getString("ChequesQtde"));
                preOrder.setChequesValor(consulta.getString("ChequesValor"));
                preOrder.setOperador(consulta.getString("Operador"));
                preOrder.setDt_Alter(consulta.getString("Dt_Alter"));
                preOrder.setHr_Alter(consulta.getString("Hr_Alter"));
                preOrder.setOperExcl(consulta.getString("OperExcl"));
                preOrder.setDt_Excl(consulta.getString("Dt_Excl"));
                preOrder.setHr_Excl(consulta.getString("Hr_Excl"));
                preOrder.setSituacao(consulta.getString("Situacao"));
                preOrder.setFlag_Excl(consulta.getString("Flag_Excl"));
                preOrder.setPreOrderVolObs(consulta.getString("ObsLacre"));

            }
            consulta.close();
            return preOrder;
        } catch (Exception e) {
            throw new Exception("PreOrderDao.getPreOrder - " + e.getMessage() + "\r\n"
                    + " SELECT PreOrder.*, PreOrderVol.Ordem, PreOrderVol.Qtde, PreOrderVol.Lacre, PreOrderVol.Tipo, PreOrderVol.Valor ValorLacre "
                    + " FROM PreOrder "
                    + " LEFT JOIN PreOrderVol on PreOrderVol.Sequencia = PreOrder.Sequencia "
                    + "                       and PreOrderVol.CodFil   = PreOrder.CodFil "
                    + " WHERE PreOrder.sequencia = " + sequencia
                    + "   AND PreOrder.CodFil = " + CodFil);
        }
    }

    /**
     * Insere uma nova entrada na tabela PreOrder
     *
     * @param preOrder
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String inserirNovoPreOrder(PreOrder preOrder, Persistencia persistencia) throws Exception {
        for (int i = 0; i < 20; i++) {
            try {
                String sequencia = null;
                String sql = " (SELECT ISNULL(MAX(Sequencia),0) + 1 Sequencia FROM PreOrder) ";
                Consulta consulta = new Consulta(sql, persistencia);
                consulta.select();
                while (consulta.Proximo()) {
                    sequencia = consulta.getString("Sequencia");
                }
                consulta.close();

                sql = " INSERT INTO PreOrder (Sequencia, CodFil, Banco, Agencia, SubAgencia, DtColeta, DtEntrega, CodCli1, Hora1O, "
                        + " Hora2O, CodCli2, Hora1D, Hora2D, Solicitante, PedidoCliente, Valor, Obs, ClassifSrv, OperIncl, "
                        + " Dt_Incl, Hr_Incl, OS, ChequesQtde, ChequesValor, Operador, Dt_Alter, Hr_Alter, OperExcl, Dt_Excl, "
                        + " Hr_Excl, Situacao, Flag_Excl, Lote) VALUES "
                        + " (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";

                consulta = new Consulta(sql, persistencia);
                consulta.setString(sequencia);
                consulta.setString(preOrder.getCodFil());
                consulta.setString(preOrder.getBanco());
                consulta.setString(preOrder.getAgencia());
                consulta.setString(preOrder.getSubAgencia());
                consulta.setString(preOrder.getDtColeta());
                consulta.setString(preOrder.getDtEntrega());
                consulta.setString(preOrder.getCodCli1());
                consulta.setString(preOrder.getHora1O());
                consulta.setString(preOrder.getHora2O());
                consulta.setString(preOrder.getCodCli2());
                consulta.setString(preOrder.getHora1D());
                consulta.setString(preOrder.getHora2D());
                consulta.setString(preOrder.getSolicitante());
                consulta.setString(preOrder.getPedidoCliente());
                consulta.setString(preOrder.getValor());
                consulta.setString(preOrder.getObs());
                consulta.setString(preOrder.getClassifSrv());
                consulta.setString(preOrder.getOperIncl());
                consulta.setString(preOrder.getDt_Incl());
                consulta.setString(preOrder.getHr_Incl());
                consulta.setString(preOrder.getOS());
                consulta.setString(preOrder.getChequesQtde());
                consulta.setString(preOrder.getChequesValor());
                consulta.setString(FuncoesString.RecortaAteEspaço(preOrder.getOperador(), 0, 10));
                consulta.setString(preOrder.getDt_Alter());
                consulta.setString(preOrder.getHr_Alter());
                consulta.setString(FuncoesString.RecortaAteEspaço(preOrder.getOperExcl(), 0, 10));
                consulta.setString(preOrder.getDt_Excl());
                consulta.setString(preOrder.getHr_Excl());
                consulta.setString(preOrder.getSituacao());
                consulta.setString(preOrder.getFlag_Excl());
                consulta.setString(preOrder.getLote());
                consulta.insert();
                consulta.close();
                return sequencia;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        throw new Exception("PreOrderDao.inserirNovoPreOrder - Muitas tentativas sem sucesso.");
    }

    public List<PreOrder> listaPedidosRecentesCliente(String CodCli1, String CodFil, Persistencia persistencia) throws Exception {
        try {
            List<PreOrder> retorno = new ArrayList<>();
            String sql = " SELECT TOP 20 * "
                    + " FROM PreOrder "
                    + " WHERE CodCli1 = ? AND CodFil = ? "
                    + " ORDER BY Sequencia desc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(CodCli1);
            consulta.setString(CodFil);
            consulta.select();
            PreOrder preOrder;
            while (consulta.Proximo()) {
                preOrder = new PreOrder();
                preOrder.setSequencia(consulta.getString("Sequencia"));
                preOrder.setCodFil(consulta.getString("CodFil"));
                preOrder.setBanco(consulta.getString("Banco"));
                preOrder.setAgencia(consulta.getString("Agencia"));
                preOrder.setSubAgencia(consulta.getString("SubAgencia"));
                preOrder.setRPV(consulta.getString("RPV"));
                preOrder.setDtColeta(consulta.getString("DtColeta"));
                preOrder.setDtEntrega(consulta.getString("DtEntrega"));
                preOrder.setCodCli1(consulta.getString("CodCli1"));
                preOrder.setHora1O(consulta.getString("Hora1O"));
                preOrder.setHora2O(consulta.getString("Hora2O"));
                preOrder.setCodCli2(consulta.getString("CodCli2"));
                preOrder.setHora1D(consulta.getString("Hora1D"));
                preOrder.setHora2D(consulta.getString("Hora2D"));
                preOrder.setSolicitante(consulta.getString("Solicitante"));
                preOrder.setPedidoCliente(consulta.getString("PedidoCliente"));
                preOrder.setValor(consulta.getString("Valor"));
                preOrder.setObs(consulta.getString("Obs"));
                preOrder.setClassifSrv(consulta.getString("ClassifSrv"));
                preOrder.setOperIncl(consulta.getString("OperIncl"));
                preOrder.setDt_Incl(consulta.getString("Dt_Incl"));
                preOrder.setHr_Incl(consulta.getString("Hr_Incl"));
                preOrder.setOS(consulta.getString("OS"));
                preOrder.setChequesQtde(consulta.getString("ChequesQtde"));
                preOrder.setChequesValor(consulta.getString("ChequesValor"));
                preOrder.setOperador(consulta.getString("Operador"));
                preOrder.setDt_Alter(consulta.getString("Dt_Alter"));
                preOrder.setHr_Alter(consulta.getString("Hr_Alter"));
                preOrder.setOperExcl(consulta.getString("OperExcl"));
                preOrder.setDt_Excl(consulta.getString("Dt_Excl"));
                preOrder.setHr_Excl(consulta.getString("Hr_Excl"));
                preOrder.setSituacao(consulta.getString("Situacao"));
                preOrder.setFlag_Excl(consulta.getString("Flag_Excl"));
                retorno.add(preOrder);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PreOrderDao.listaPedidosRecentesCliente - " + e.getMessage());
        }
    }
}
