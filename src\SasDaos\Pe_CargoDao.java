/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Pe_Cargo;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
public class Pe_CargoDao {

    public void removerPe_Cargo(Pe_Cargo pe_cargo, Persistencia persistencia) throws Exception {
        try {
            String sql = " DELETE Pe_Cargo WHERE Codigo = ? AND Cod_Cargo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pe_cargo.getCodigo());
            consulta.setString(pe_cargo.getCod_Cargo());
            consulta.delete();
        } catch (Exception e) {
            throw new Exception("Pe_CargoDao.removerPe_Cargo - " + e.getMessage() + "\r\n"
                    + " DELETE Pe_Cargo WHERE Codigo = " + pe_cargo.getCodigo() + " AND Cod_Cargo = " + pe_cargo.getCod_Cargo());
        }
    }

    public void inserirPe_Cargo(Pe_Cargo pe_cargo, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO Pe_Cargo VALUES (?, ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pe_cargo.getCodigo());
            consulta.setString(pe_cargo.getCod_Cargo());
            consulta.setString(pe_cargo.getOperador());
            consulta.setString(pe_cargo.getDt_Alter());
            consulta.setString(pe_cargo.getHr_Alter());
            consulta.insert();
        } catch (Exception e) {
            throw new Exception("Pe_CargoDao.inserirPe_Cargo - " + e.getMessage() + "\r\n"
                    + " INSERT INTO Pe_Cargo VALUES (" + pe_cargo.getCodigo() + ", " + pe_cargo.getCod_Cargo() + ", " + pe_cargo.getOperador() + ", " + pe_cargo.getDt_Alter() + ", " + pe_cargo.getHr_Alter() + ")");
        }
    }

    /**
     *
     * @param lista: lista de cargos que Pessoa deve possuir
     * @param codigo: id da Pessoa
     * @param operador: nome de quem está executando método
     * @param persistencia
     * @throws Exception
     */
    public void mergeListaCargosDePessoa(List<Pe_Cargo> lista, String codigo, String operador, Persistencia persistencia) throws Exception {
        if (lista.isEmpty()) {
            return;
        }

        Consulta consulta = null;
        try {
            if (codigo == null || operador == null) {
                throw new IllegalArgumentException();
            }

            String sql = "MERGE Pe_Cargo target\n"
                    + "USING (\n"
                    + "    VALUES "
                    + lista.stream()
                            .map(t -> ("(?, ?, ?, GETDATE(), format(getdate(), 'HH:mm'))"))
                            .collect(Collectors.joining(",\n           "))
                    + "\n) src(codigo, cod_cargo, operador, Dt_Alter, Hr_Alter)\n"
                    + "ON target.codigo = src.codigo\n"
                    + "    AND target.cod_cargo = src.cod_cargo\n"
                    + "WHEN MATCHED THEN\n"
                    + "    UPDATE\n"
                    + "    SET Operador = src.Operador,\n"
                    + "        Dt_Alter = src.Dt_Alter,\n"
                    + "        Hr_Alter = src.Hr_Alter\n"
                    + "WHEN NOT MATCHED THEN\n"
                    + "    INSERT (codigo, cod_cargo, Operador, Dt_Alter, Hr_Alter)\n"
                    + "    VALUES (src.codigo, src.cod_cargo, src.Operador, src.Dt_Alter, src.Hr_Alter)\n"
                    + "WHEN NOT MATCHED BY SOURCE AND target.codigo = ? THEN\n"
                    + "    DELETE;";

            consulta = new Consulta(sql, persistencia);

            for (Pe_Cargo pe_cargo : lista) {
                // ordem: codigo, cod_cargo, operador
                consulta.setString(codigo);
                consulta.setString(pe_cargo.getCod_Cargo());
                consulta.setString(operador);
            }

            consulta.setString(codigo);
            consulta.insert();
        } catch (SQLException e) {
            throw e; // TODO
        } catch (Exception e) {
            throw e;
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    public boolean existePe_Cargo(String codigo, String cod_Cargo, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT * FROM Pe_Cargo WHERE Codigo = ? AND Cod_Cargo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.setString(cod_Cargo);
            consulta.select();
            boolean retorno = consulta.Proximo();
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Pe_CargoDao.existePe_Cargo - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM Pe_Cargo WHERE Codigo = " + codigo + " AND Cod_Cargo = " + cod_Cargo);
        }
    }

    public List<Pe_Cargo> listarCargosPretendidosPessoa(String codigo, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT Pe_Cargo.*, CONVERT(Varchar, Pe_Cargo.Dt_Alter, 112) Dt_AlterF, Descricao FROM Pe_Cargo \n"
                    + " LEFT JOIN Cargos ON Cargos.Codigo = Pe_Cargo.Cod_Cargo \n"
                    + " WHERE Pe_Cargo.Codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();
            List<Pe_Cargo> retorno = new ArrayList<>();
            Pe_Cargo pe_cargo;
            while (consulta.Proximo()) {
                pe_cargo = new Pe_Cargo();
                pe_cargo.setCodigo(consulta.getString("Codigo"));
                pe_cargo.setCod_Cargo(consulta.getString("Cod_Cargo"));
                pe_cargo.setOperador(consulta.getString("Operador"));
                pe_cargo.setDt_Alter(consulta.getString("Dt_AlterF"));
                pe_cargo.setHr_Alter(consulta.getString("Hr_Alter"));

                pe_cargo.setDescricao(consulta.getString("Descricao"));
                retorno.add(pe_cargo);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Pe_CargoDao.listarCargosPretendidosPessoa - " + e.getMessage() + "\r\n"
                    + " SELECT Pe_Cargo.*, CONVERT(Varchar, Dt_Alter, 112) Dt_AlterF, Descricao FROM Pe_Cargo \n"
                    + " LEFT JOIN Cargos ON Cargos.Codigo = Pe_Cargo.Cod_Cargo \n"
                    + " WHERE Pe_Cargo.Codigo =" + codigo);
        }
    }
}
