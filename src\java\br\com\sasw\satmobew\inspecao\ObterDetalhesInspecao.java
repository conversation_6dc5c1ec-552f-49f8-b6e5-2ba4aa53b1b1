/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satmobew.inspecao; 

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.PstInspecao;
import SasDaos.PstInspecaoDao;
import static SasLibrary.ValidarUsuario.ValidaUsuarioPonto;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import Xml.Xmls;
import br.com.sasw.satmobew.ValidarUsuario;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.format.DateTimeFormatter;
import java.util.List;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "ObterDetalhesInspecao", urlPatterns = {"/inspecao/ObterDetalhesInspecao"})
public class ObterDetalhesInspecao extends HttpServlet {
    
    private ArquivoLog logerro;
    private SasPoolPersistencia pool;
    
    /**
     * Cria a pool de persistencias na criação da servlet
     */
    @Override
    public void init(){
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter resp = response.getWriter(); 
            
        String resposta = "<?xml version=\"1.0\"?>";
        String param = request.getParameter("param");
        String codPessoa = request.getParameter("codpessoa");
        String senha = request.getParameter("senha");
        String codInspecao = request.getParameter("codInspecao");
        String data = request.getParameter("data");
        String matr = request.getParameter("matr");
        String secao = request.getParameter("secao");
        String codfil = request.getParameter("codfil");
        String dataAtual = request.getParameter("dataAtual");
        String horaAtual = request.getParameter("horaAtual");
        logerro = new ArquivoLog();
        
        if(null == dataAtual || dataAtual.equals("")) dataAtual = DataAtual.getDataAtual("SQL");
        if(null == horaAtual || horaAtual.equals("")) horaAtual = DataAtual.getDataAtual("HORA");
        
        try {
            // Gera o trace com os valores da requisição
            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codPessoa,  param, logerro);
            tStart = System.currentTimeMillis();
            
            //Cria a persistencia
            Persistencia persistencia = pool.getConexao(param);
            
            if (ValidaUsuarioPonto(codPessoa, senha, persistencia)) {
                DateTimeFormatter dd = DateTimeFormatter.ofPattern("yyyyMMdd");
                DateTimeFormatter dh = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
                PstInspecaoDao pstInspecaoDao = new PstInspecaoDao();
                List<PstInspecao> pstInspecoes = pstInspecaoDao.listaInspecoes(secao, codfil, data, matr, codInspecao, persistencia);
                
                resposta += Xmls.tag("resp", "1");
                resposta += Xmls.tag("qtdInspecoes", pstInspecoes.size());
                StringBuilder sb;                
                for(PstInspecao pstInspecao : pstInspecoes){
                    sb = new StringBuilder();
                    sb.append(Xmls.tag("CodInspecao", pstInspecao.getCodInspecao()))
                            .append(Xmls.tag("Secao", pstInspecao.getSecao()))
                            .append(Xmls.tag("Codfil", pstInspecao.getCodfil()))
                            .append(Xmls.tag("Data", pstInspecao.getData()))
                            .append(Xmls.tag("Sequencia", pstInspecao.getSequencia()))
                            .append(Xmls.tag("Pergunta", pstInspecao.getPergunta()))
                            .append(Xmls.tag("Resposta", pstInspecao.getResposta()))
                            .append(Xmls.tag("CaminhoImagem", pstInspecao.getCaminhoImagem()))
                            .append(Xmls.tag("CaminhoVideo", pstInspecao.getCaminhoVideo()))
                            .append(Xmls.tag("Matr", pstInspecao.getMatr()))
                            .append(Xmls.tag("Veiculo", pstInspecao.getVeiculo()))
                            .append(Xmls.tag("Local", pstInspecao.getLocal()))
                            .append(Xmls.tag("Operador", pstInspecao.getOperador()))
                            .append(Xmls.tag("Dt_Alter", pstInspecao.getDt_Alter()))
                            .append(Xmls.tag("Hora", pstInspecao.getHr_Alter()));   
                    resposta += Xmls.tag("inspecao", sb.toString());
                }
            }
            else {
                resposta += Xmls.tag("resp", "2");//erro ao validar usuáio
            }
            resp.print(resposta);
            // Opcional: gera um trace com a resposta do servidor
            Trace.gerarTrace(getServletContext(), this.getServletName(),resposta, codPessoa, param, logerro);
            
            // Fecha a conexão
            persistencia.FechaConexao();
            
            // Calcula o tempo gasto pelo servidor entre criar a persistencia e enviar a respota de volta ao android e gera o trace disso.
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0)+" segundos.\r\n", codPessoa, param, logerro); 
        } catch (Exception e) {
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Falha ObterInspecoes - "+ e.getMessage(), codPessoa, param, logerro);
            resp.print("<?xml version=\"1.0\"?>" + Xmls.tag("resp", "0")+Xmls.tag("erro", e.getMessage()));
        }
    }
    
    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

}
