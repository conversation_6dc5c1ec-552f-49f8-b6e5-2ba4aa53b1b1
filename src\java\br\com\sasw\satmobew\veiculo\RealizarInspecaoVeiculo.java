/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satmobew.veiculo;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.VeiculosInspec;
import SasDaos.VeiculosInspecDao;
import static SasLibrary.ValidarUsuario.ValidaUsuarioPonto;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import Xml.Xmls;
import br.com.sasw.satmobew.MultipartMap;
import br.com.sasw.satmobew.ValidarUsuario;
import br.com.sasw.satmobew.mensagem.Messages;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import javax.servlet.ServletException;
import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "RealizarInspecaoVeiculo", urlPatterns = {"/veiculo/RealizarInspecaoVeiculo"})
@MultipartConfig(location = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobEWTemp", maxFileSize = 50485760L) // 10MB.
public class RealizarInspecaoVeiculo extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;

    @Override
    public void init() {
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws java.io.UnsupportedEncodingException
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws UnsupportedEncodingException, IOException {
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        logerro = new ArquivoLog();
        String codPessoa = "SATMOBEW";
        String param = "SATMOBEW";
        try {
            long tStart = 0, tEnd;
            MultipartMap map = new MultipartMap(request, this);

            codPessoa = map.getParameter("codpessoa");
            String senha = map.getParameter("senha");
            param = map.getParameter("param");
            String empresa = param;

            String arquivos = map.getParameter("arquivos");
            String sequencia = map.getParameter("sequencia");
            String numero = map.getParameter("numero");
            String data = map.getParameter("data");
            String hora = map.getParameter("hora");
            String marcaModelo = map.getParameter("marcaModelo");
            String kMIni = map.getParameter("kMIni");
            if (null == kMIni) {
                kMIni = "";
            }
            String kMFim = map.getParameter("kMFim");
            if (null == kMFim) {
                kMFim = "";
            }
            String combustivel = map.getParameter("cobustivel");
            String problemaLuzes = map.getParameter("problemaLuzes");
            String relatoDanos = map.getParameter("relatoDanos");
            String relatoProblemas = map.getParameter("relatoProblemas");
            String operador = map.getParameter("operador");

            String dataAtual = map.getParameter("dataAtual");
            String horaAtual = map.getParameter("horaAtual");
            String idioma = map.getParameter("idioma");
            Messages messages = new Messages();
            messages.setIdioma(idioma);

            if (null == dataAtual || dataAtual.equals("")) {
                dataAtual = DataAtual.getDataAtual("SQL");
            }
            if (null == horaAtual || horaAtual.equals("")) {
                horaAtual = DataAtual.getDataAtual("HORA");
            }

            String retorno = "<?xml version=\"1.0\"?>";

            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codPessoa, param, logerro);
            tStart = System.currentTimeMillis();

            Persistencia persistencia = pool.getConexao(param);
            empresa = persistencia.getEmpresa().equals("SATSASEX") ? "SASW" : persistencia.getEmpresa().replace("SAT", "");

            if (ValidaUsuarioPonto(codPessoa, senha, persistencia)) {
                VeiculosInspec veiculosInspec = new VeiculosInspec();
                veiculosInspec.setNumero(numero);
                veiculosInspec.setData(data);
                veiculosInspec.setHora(hora);
                veiculosInspec.setMarcaModelo(marcaModelo);
                veiculosInspec.setKMIni(kMIni);
                veiculosInspec.setKMFim(kMFim);
                veiculosInspec.setCombustivel(combustivel);
                veiculosInspec.setProblemaLuzes(problemaLuzes);
                veiculosInspec.setRelatoDanos(relatoDanos);
                veiculosInspec.setRelatoProblemas(relatoProblemas.equals("") ? null : relatoProblemas);
                veiculosInspec.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
                veiculosInspec.setDt_Alter(dataAtual);
                veiculosInspec.setHr_Alter(horaAtual);
                veiculosInspec.setCodPessoaInspec(codPessoa);
                veiculosInspec = (VeiculosInspec) FuncoesString.removeAcentoObjeto(veiculosInspec);

                VeiculosInspecDao veiculosInspecDao = new VeiculosInspecDao();
                if (sequencia.equals("0")) {
                    sequencia = veiculosInspecDao.insereInspecao(veiculosInspec, persistencia);
                }

                StringBuilder imgKMIni = new StringBuilder();
                StringBuilder imgKMFim = new StringBuilder();
                StringBuilder imgCombustivel = new StringBuilder();
                StringBuilder imgRelatoDanos = new StringBuilder();
                if (arquivos != null) {
                    String[] arquivosArray = arquivos.split(";");
                    String url = "https://mobile.sasw.com.br:9091/satellite/fotos/" + param + "/inspecao_veiculo/" + data.replace("-", "")
                            + "/" + sequencia.replace(".0", "") + "/";
                    File file;
                    byte[] bFile;
                    FileInputStream fileInputStream;
                    FileOutputStream fileOuputStream;
                    for (String arquivo : arquivosArray) {
                        // se arquivo conter a palavra thumbnail, não salva, apenas 
                        if (arquivo.contains("thumbnail_")) {
                            arquivo = arquivo.replace("thumbnail_", "");
                        } else {
                            file = map.getFile(arquivo);
                            bFile = new byte[(int) file.length()];

                            //read file into bytes[]
                            fileInputStream = new FileInputStream(file);
                            fileInputStream.read(bFile);

                            File diretorio = new File("C:/xampp/htdocs/satellite/fotos/" + param + "/inspecao_veiculo/" + data.replace("-", "")
                                    + "/" + sequencia.replace(".0", "") + "/");
                            if (!diretorio.exists()) {
                                diretorio.mkdirs();  // cria diretórios caso não estejam criados
                            }

                            fileOuputStream = new FileOutputStream("C:/xampp/htdocs/satellite/fotos/" + param + "/inspecao_veiculo/" + data.replace("-", "")
                                    + "/" + sequencia.replace(".0", "") + "/" + arquivo);

                            fileOuputStream.write(bFile);
                            fileInputStream.close();
                            fileOuputStream.close();
                        }
                        if (arquivo.contains("imgKMIni")) {
                            imgKMIni.append(url).append(arquivo).append(";");
                        } else if (arquivo.contains("imgKMFim")) {
                            imgKMFim.append(url).append(arquivo).append(";");
                        } else if (arquivo.contains("imgCombustivel")) {
                            imgCombustivel.append(url).append(arquivo).append(";");
                        } else if (arquivo.contains("imgRelatoDanos")) {
                            imgRelatoDanos.append(url).append(arquivo).append(";");
                        }
                    }
                }

                veiculosInspec.setSequencia(sequencia);
                veiculosInspec.setImgKMIni(imgKMIni.toString());
                veiculosInspec.setImgKMFim(imgKMFim.toString());
                veiculosInspec.setImgCombustivel(imgCombustivel.toString());
                veiculosInspec.setImgRelatoDanos(imgRelatoDanos.toString());

                veiculosInspecDao.updateVeiculosInspec(veiculosInspec, persistencia);

//                ClientesDao clientesDao = new ClientesDao();
//                Clientes cliente = clientesDao.clientePosto(secao, codfil, persistencia);
//                
//                FiliaisDao filiaisDao = new FiliaisDao();
//                Filiais filial = filiaisDao.getFilial(codfil, persistencia);
//                
//                InputStream inputStream = RealizarRelatorio.class.getResourceAsStream("/relatorios/relatorioEW.html");
//                ByteArrayOutputStream result = new ByteArrayOutputStream();
//                byte[] buffer = new byte[1024];
//                int length;
//                while ((length = inputStream.read(buffer)) != -1) {
//                    result.write(buffer, 0, length);
//                }
//
//                String htmlEmail = result.toString("UTF-8");
//                String htmlAlt;
//                String htmlAnexo;
//                
//                inputStream.close();
//                result.close();
//                    
//                htmlEmail = htmlEmail.replace("@TituloPagina", messages.getMessage("RelatorioEvento")+" - "+sequencia);
//                
//                htmlEmail = htmlEmail.replace("@TituloRelatorio", messages.getMessage("RelatorioEvento").toUpperCase());
//                htmlEmail = htmlEmail.replace("@SubTituloRelatorio", cliente.getContato());
//                htmlEmail = htmlEmail.replace("@TituloInfo", "");
//                htmlEmail = htmlEmail.replace("@TituloEndereco", cliente.getEnde() + ", " + cliente.getBairro() + ". "+cliente.getCidade()+"/"+cliente.getEstado());
//                htmlEmail = htmlEmail.replace("@TituloTelefone", messages.getTelefone(filial.getFone()));
//                htmlEmail = htmlEmail.replace("@Detalhes", messages.getMessage("Detalhes"));
//                
//                inputStream = RealizarRelatorio.class.getResourceAsStream("/relatorios/EW_uma_coluna.html");
//                result = new ByteArrayOutputStream();
//                while ((length = inputStream.read(buffer)) != -1) {
//                    result.write(buffer, 0, length);
//                }
//                String padrao1coluna = result.toString();
//                
//                inputStream.close();
//                result.close();
//                
//                inputStream = RealizarRelatorio.class.getResourceAsStream("/relatorios/EW_duas_colunas.html");
//                result = new ByteArrayOutputStream();
//                while ((length = inputStream.read(buffer)) != -1) {
//                    result.write(buffer, 0, length);
//                }
//                String padrao2colunas = result.toString();
//                
//                inputStream.close();
//                result.close();
//                
//                inputStream = RealizarRelatorio.class.getResourceAsStream("/relatorios/EW_imagem.html");
//                result = new ByteArrayOutputStream();
//                while ((length = inputStream.read(buffer)) != -1) {
//                    result.write(buffer, 0, length);
//                }
//                String padraoImagem = result.toString();
//                
//                inputStream.close();
//                result.close();
//                
//                inputStream = RealizarRelatorio.class.getResourceAsStream("/relatorios/EW_imagem_botoes.html");
//                result = new ByteArrayOutputStream();
//                while ((length = inputStream.read(buffer)) != -1) {
//                    result.write(buffer, 0, length);
//                }
//                String padraoImagemBotoes = result.toString();
//                
//                inputStream.close();
//                result.close();
//                
//                inputStream = RealizarRelatorio.class.getResourceAsStream("/relatorios/EW_mapa.html");
//                result = new ByteArrayOutputStream();
//                while ((length = inputStream.read(buffer)) != -1) {
//                    result.write(buffer, 0, length);
//                }
//                String padraoMapa = result.toString();
//                
//                inputStream.close();
//                result.close();
//                            
//                StringBuilder relatorioEmail = new StringBuilder();
//                StringBuilder relatorioAlt   = new StringBuilder();
//                StringBuilder relatorioAnexo = new StringBuilder();
//                
//                relatorioEmail.append(padrao2colunas.replace("@Padrao", messages.getMessage("Posto")).replace("@TextoPadrao", cliente.getContato()));   
//                relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao",cliente.getNRed()+" - "+cliente.getNome()));
//                relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao",cliente.getEnde()+" - "+cliente.getBairro()));
//                relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao",cliente.getCidade()+"/"+cliente.getEstado()+" - "
//                 + messages.getCEP(cliente.getCEP())));
//                
//                relatorioEmail.append(padrao2colunas.replace("@Padrao", messages.getMessage("Ocorrencia")).replace("@TextoPadrao", tmktDetPst.getHistorico()));
//                relatorioEmail.append(padrao2colunas.replace("@Padrao", messages.getMessage("Detalhes")).replace("@TextoPadrao", tmktDetPst.getDetalhes().replace("\\N", "\n")));
//                
//                relatorioEmail.append(padrao2colunas.replace("@Padrao", messages.getMessage("Data")).replace("@TextoPadrao", dataHtml));
//                relatorioEmail.append(padrao2colunas.replace("@Padrao", messages.getMessage("Hora")).replace("@TextoPadrao", tmktDetPst.getHora()));
//                
//                Integer qtdeF = tmktDetPstDao.getQtdeFt(sequencia, persistencia);
//                relatorioEmail.append(padrao1coluna.replace("@Padrao", 
//                        qtdeF == 1 ? messages.getMessage("Foto") : ((qtdeF > 1) ? messages.getMessage("Fotos") : "")));
//                
//                relatorioAlt.append(relatorioEmail);
//                relatorioAnexo.append(relatorioEmail);
//                
//                String urlEmail = "https://mobile.sasw.com.br:9091/satellite/fotos/" + param + "/";
//                String urlAlt   = "https://mobile.sasw.com.br:9091/satellite/fotos/" + param + "/";
//                String urlAnexo = "http://localhost:9080/satellite/fotos/" + param + "/";
//                String urlAnexo = "file:\\C:\\xampp\\htdocs\\satellite\\fotos\\" + param + "\\";
//
//                String foto = FuncoesString.RecortaString(tmktDetPst.getData(), 0, 10).replaceAll("-", "" )+ "/" +
//                        FuncoesString.preencheCom(tmktDetPst.getSequencia().toPlainString().replace(".0", ""), "0", 8, 1);
//                String mapa = FuncoesString.RecortaString(tmktDetPst.getData(), 0, 10).replaceAll("-", "" )+ "/" +
//                        FuncoesString.preencheCom(tmktDetPst.getSequencia().toPlainString().replace(".0", ""), "0", 8, 1) +
//                        "_mapa.png";
//                    
//                for(int i = 1; i < qtdeF; i++){
//                    relatorioEmail.append(padraoImagem.replace("@ImagemId", "imggirar_"+i)
//                            .replace("@ImagemMensagem", messages.getMessage("ImagemNaoEncontrada"))
//                            .replace("@ImagemRelatorio", urlEmail + foto + "_" + i + ".jpg"));
//                    relatorioAnexo.append(padraoImagem.replace("@ImagemId", "imggirar_"+i)
//                            .replace("@ImagemMensagem", messages.getMessage("ImagemNaoEncontrada"))
//                            .replace("@ImagemRelatorio", urlAnexo + foto + "_" + i + ".jpg"));
//                    relatorioAlt.append(padraoImagemBotoes.replace("@ImagemId", "imggirar_"+i)
//                            .replace("@ImagemRelatorio",urlAlt + foto + "_" + i + ".jpg")
//                            .replace("@ImagemMensagem", messages.getMessage("ImagemNaoEncontrada"))
//                            .replace("@ImagemBack","back_"+i)
//                            .replace("@ImagemNext","next_"+i)
//                            .replace("@GirarEsquerda", messages.getMessage("Girar Esquerda"))
//                            .replace("@GirarDireita", messages.getMessage("Girar Direita")));
//                }
//                 
//                enviandoImagemMapa(mapaEstatico(latitude, longitude, cliente.getNRed()), param, tmktDetPst.getSequencia().toPlainString(), tmktDetPst.getData());
//                
//                relatorioEmail.append(padrao1coluna.replace("@Padrao", messages.getMessage("Mapa")));
//                relatorioAlt.append(padrao1coluna.replace("@Padrao", messages.getMessage("Mapa")));
//                relatorioAnexo.append(padrao1coluna.replace("@Padrao", messages.getMessage("Mapa")));
//                
//                relatorioEmail.append(padraoImagem.replace("@ImagemId", "mapa")
//                            .replace("@ImagemMensagem", messages.getMessage("ErroMapa"))
//                            .replace("@ImagemRelatorio", mapaEstatico(latitude, longitude, cliente.getNRed())));
//                relatorioAlt.append(padraoMapa.replace("@MensagemErroMapa", messages.getMessage("ErroMapa")));
//                relatorioAnexo.append(padraoImagem.replace("@ImagemId", "mapa")
//                            .replace("@ImagemMensagem", messages.getMessage("ErroMapa"))
//                            .replace("@ImagemRelatorio", mapaEstatico(latitude, longitude, cliente.getNRed())));
//               
//                htmlAlt   = htmlEmail.replace("@Relatorio", relatorioAlt.toString());
//                htmlAnexo = htmlEmail.replace("@Relatorio", relatorioAnexo.toString());
//                htmlEmail = htmlEmail.replace("@Relatorio", relatorioEmail.toString());
//                
//                htmlEmail = htmlEmail.replace("@Script", "");
//                htmlAnexo = htmlAnexo.replace("@Script", "");
//                htmlAlt = htmlAlt.replace("@Script", mapa(latitude, longitude, cliente.getNRed(),qtdeF));
//                
//                htmlEmail = htmlEmail.replace("@ImagemLogo", getLogo(empresa,"0"));
//                htmlAnexo = htmlAnexo.replace("@ImagemLogo", getLogoAnexo(empresa,"0"));
//                htmlAlt = htmlAlt.replace("@ImagemLogo", getLogo(empresa,"0"));
//                
//                String anexoHTML = empresa+"_report_"+sequencia.replace(".0", "")+".html";
//                String anexoPDF  = empresa+"_report_"+sequencia.replace(".0", "")+".pdf";                
//
//                htmlEmail = htmlEmail.replace("@URL", "https://mobile.sasw.com.br:9091/Satmobile/documentos/anexo-email/"+anexoHTML);
//                htmlAlt   = htmlAlt.replace("@URL", "");
//                htmlAnexo = htmlAnexo.replace("@URL", "");
//
//                htmlEmail = htmlEmail.replace("@MensagemUrl", messages.getMessage("MensagemUrl"));
//                htmlAlt   = htmlAlt.replace("@MensagemUrl", "");
//                htmlAnexo = htmlAnexo.replace("@MensagemUrl", "");
//                
//                File filePDFanexo = new File("C:/xampp/htdocs/satmobile/documentos/anexo-email/");
//                filePDFanexo.mkdirs();
//                OutputStream osPdf = new FileOutputStream("C:/xampp/htdocs/satmobile/documentos/anexo-email/"+anexoPDF);
//
//                Tidy tidy = new Tidy();
//                    tidy.setShowWarnings(false);
//                InputStream input = new ByteArrayInputStream(htmlAnexo.getBytes());
//                Document doc = tidy.parseDOM(input, null);
//                ITextRenderer renderer = new ITextRenderer();
//                renderer.setDocument(doc, null);
//                renderer.layout();
//                renderer.createPDF(osPdf);
//                input.close();
//                osPdf.close();
//                
//                File newHtmlFile = new File("C:/xampp/htdocs/satmobile/documentos/anexo-email/"+anexoHTML);
//                FileUtils.writeStringToFile(newHtmlFile, htmlAlt, StandardCharsets.UTF_8);
//
//                if(param.contains("GSI") || param.contains("PROSECUR") || param.contains("GLOVAL")){
//                    EmailsEnviarDao emailDao = new EmailsEnviarDao();
//                    EmailsEnviarAnexoDao emailanexodao = new EmailsEnviarAnexoDao();
//                    
//                    EmailsEnviar email1 = new EmailsEnviar();
//                    EmailsEnviar email2 = new EmailsEnviar();
//                    EmailsEnviar email3 = new EmailsEnviar();
//
//                    email1.setRemet_email("<EMAIL>");
//                    email2.setRemet_email("<EMAIL>");
//                    email3.setRemet_email("<EMAIL>");
//
//                    email1.setRemet_nome(operador);
//                    email2.setRemet_nome(operador);
//                    email3.setRemet_nome(operador);
//
//                    email1.setDest_nome(filial.getRazaoSocial());
//                    email2.setDest_nome(filial.getRazaoSocial());
//                    email3.setDest_nome(cliente.getContato());
//
//                    email1.setDest_email("<EMAIL>;<EMAIL>;<EMAIL>");
//                    email2.setDest_email("<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>");
//                    email3.setDest_email(cliente.getEmail());
//                    if(cliente.getEmail().equals("7350001")) email3.setDest_email("<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>");
//                    else email3.setDest_email(cliente.getEmail());
//
//                    email1.setParametro(param);
//                    email2.setParametro(param);
//                    email3.setParametro(param);
//                    email1.setCodFil(codfil);
//                    email2.setCodFil(codfil);
//                    email3.setCodFil(codfil);
//                    email1.setCodCli(cliente.getCodigo());
//                    email2.setCodCli(cliente.getCodigo());
//                    email3.setCodCli(cliente.getCodigo());
//                    
//                    String assunto =  empresa+" "+messages.getMessage("Relatorio")+" "+sequencia;
//
//                    email1.setAssunto(assunto);
//                    email1.setSmtp("smtplw.com.br");
//                    email1.setMensagem(htmlEmail.replace("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">\n",""));
//                    email1.setAut_login("sasw");
//                    email1.setAut_senha("xNiadJEj9607");
//                    email1.setPorta(587);
//
//                    email2.setAssunto(assunto);
//                    email2.setSmtp("smtplw.com.br");
//                    email2.setMensagem(htmlEmail.replace("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">\n",""));
//                    email2.setAut_login("sasw");
//                    email2.setAut_senha("xNiadJEj9607");
//                    email2.setPorta(587);
//
//                    email3.setAssunto(assunto);
//                    email3.setSmtp("smtplw.com.br");
//                    email3.setMensagem(htmlEmail.replace("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">\n",""));
//                    email3.setAut_login("sasw");
//                    email3.setAut_senha("xNiadJEj9607");
//                    email3.setPorta(587);
//
//                    Persistencia satellite = pool.getConexao("SATELLITE");
//                    String seq1 = emailDao.inserirEmail(email1, satellite);
//                    String seq2 = emailDao.inserirEmail(email2, satellite);
//                    
//                    EmailsEnviarAnexo emailanexo1 = new EmailsEnviarAnexo();
//                    emailanexo1.setSequencia(new BigDecimal(seq1));
//                    emailanexo1.setOrdem(1);
//                    emailanexo1.setEndAnexo(anexoPDF);
//                    emailanexo1.setNomeAnexo(anexoPDF);
//                    emailanexo1.setDescAnexo(anexoPDF);
//
//                    EmailsEnviarAnexo emailanexo2 = new EmailsEnviarAnexo();
//                    emailanexo2.setSequencia(new BigDecimal(seq2));
//                    emailanexo2.setOrdem(1);
//                    emailanexo2.setEndAnexo(anexoPDF);
//                    emailanexo2.setNomeAnexo(anexoPDF);
//                    emailanexo2.setDescAnexo(anexoPDF);
//
//                    emailanexodao.inserirAnexos(emailanexo1, satellite);
//                    emailanexodao.inserirAnexos(emailanexo2, satellite);
//                    
////                    if(!email3.getDest_email().equals("")){
////                        String seq3 = emailDao.inserirEmail(email3, satellite);
////                        EmailsEnviarAnexo emailanexo3 = new EmailsEnviarAnexo();
////                        emailanexo3.setSequencia(new BigDecimal(seq3));
////                        emailanexo3.setOrdem(1);
////                        emailanexo3.setEndAnexo(anexoPDF);
////                        emailanexo3.setNomeAnexo(anexoPDF);
////                        emailanexo3.setDescAnexo(anexoPDF);
////                        emailanexodao.inserirAnexos(emailanexo3, satellite);
////                    }
//                    
//                    satellite.FechaConexao();
//
//                } else{
//                    EmailsEnviar email = new EmailsEnviar();
//                    email.setRemet_email("<EMAIL>");
//                    email.setRemet_nome(operador);
//                    email.setDest_nome(filial.getRazaoSocial());
////                    email.setDest_email("<EMAIL>;<EMAIL>;<EMAIL>");
//                    email.setDest_email("<EMAIL>;<EMAIL>;<EMAIL>");
//
//                    email.setParametro(param);
//                    email.setCodFil(codfil);
//                    email.setCodCli(cliente.getCodigo());
//
//                    String assunto =  empresa+" "+messages.getMessage("Relatorio")+" "+sequencia;
//                    email.setAssunto(assunto);
//                    email.setSmtp("smtplw.com.br");
//                    email.setMensagem(htmlEmail.replace("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">\n",""));
//                    email.setAut_login("sasw");
//                    email.setAut_senha("xNiadJEj9607");
//                    email.setPorta(587);
//
//                    EmailsEnviarDao emailDao = new EmailsEnviarDao();
//
//                    Persistencia satellite = pool.getConexao("SATELLITE");
//                    String seq = emailDao.inserirEmail(email, satellite);
//
//                    EmailsEnviarAnexo emailanexo = new EmailsEnviarAnexo();
//                    emailanexo.setSequencia(new BigDecimal(seq));
//                    emailanexo.setOrdem(1);
//                    emailanexo.setEndAnexo(anexoPDF);
//                    emailanexo.setNomeAnexo(anexoPDF);
//                    emailanexo.setDescAnexo(anexoPDF);
//
//                    EmailsEnviarAnexoDao emailanexodao = new EmailsEnviarAnexoDao();
//                    emailanexodao.inserirAnexos(emailanexo, satellite);
//                    satellite.FechaConexao();
//                }
//                out.print(htmlAlt);
                retorno += "<resp>1</resp>"
                        + Xmls.tag("sequencia", sequencia)
                        + Xmls.tag("imgKMIni", veiculosInspec.getImgKMFim())
                        + Xmls.tag("imgKMFim", veiculosInspec.getImgCombustivel())
                        + Xmls.tag("imgCombustivel", veiculosInspec.getImgKMIni())
                        + Xmls.tag("imgRelatoDanos", veiculosInspec.getImgRelatoDanos());
            } else {
                retorno += "<resp>2</resp>";
            }
            persistencia.FechaConexao();
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0) + " segundos.\r\n", codPessoa, param, logerro);

            out.print(retorno);
//            map.cleanUp(request);
        } catch (Exception e) {
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Falha RealizarRelatorio - " + e.getMessage(), codPessoa, param, logerro);
            out.print("<?xml version=\"1.0\"?><resp>0</resp>");
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
