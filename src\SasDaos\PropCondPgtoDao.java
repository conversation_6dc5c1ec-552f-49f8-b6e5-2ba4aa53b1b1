/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PropCondPgto;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PropCondPgtoDao {

    /**
     * Lista todas as condições de pagamento de uma proposta
     *
     * @param numero
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PropCondPgto> listar(BigDecimal numero, BigDecimal codFil, Persistencia persistencia) throws Exception {
        List<PropCondPgto> retorno = new ArrayList<>();
        try {
            String sql = " select * from PropCondPgto"
                    + " where numero = ? and codfil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(numero);
            consulta.setBigDecimal(codFil);
            consulta.select();
            PropCondPgto condicao;
            while (consulta.Proximo()) {
                condicao = new PropCondPgto();
                condicao.setNumero(consulta.getBigDecimal("Numero"));
                condicao.setCodFil(consulta.getBigDecimal("CodFil"));
                condicao.setOpcao(consulta.getString("Opcao"));
                condicao.setCodPessoa(consulta.getBigDecimal("CodPessoa"));
                condicao.setDescricao(consulta.getString("Descricao"));
                condicao.setFator(consulta.getBigDecimal("Fator"));
                condicao.setOperador(consulta.getString("Operador"));
                condicao.setDt_Alter(consulta.getString("Dt_Alter"));
                condicao.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(condicao);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao lista condições de pagamento - " + e.getMessage());
        }
    }

    /**
     * Insere uma nova condição de pagamento
     *
     * @param condicao
     * @param persistencia
     * @throws Exception
     */
    public void inserirCondicao(PropCondPgto condicao, Persistencia persistencia) throws Exception {
        try {
            String sql = " insert into propcondpgto (Numero, CodFil, Opcao, CodPessoa, "
                    + " Descricao, Fator, Operador, Dt_Alter, Hr_Alter) "
                    + " values (?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(condicao.getNumero());
            consulta.setBigDecimal(condicao.getCodFil());
            consulta.setString(condicao.getOpcao());
            consulta.setBigDecimal(condicao.getCodPessoa());
            consulta.setString(condicao.getDescricao());
            consulta.setBigDecimal(condicao.getFator());
            consulta.setString(condicao.getOperador());
            consulta.setString(condicao.getDt_Alter());
            consulta.setString(condicao.getHr_Alter());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao inserir condição de pagamento - " + e.getMessage());
        }
    }

    /**
     * Exclui uma condição de pagamento de uma proposta.
     *
     * @param condicao
     * @param persistencia
     * @throws Exception
     */
    public void removerCondicao(PropCondPgto condicao, Persistencia persistencia) throws Exception {
        try {
            String sql = "delete from PropCondPgto where opcao = ? and numero = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(condicao.getOpcao());
            consulta.setBigDecimal(condicao.getNumero());
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao excluir condição de pagamento - " + e.getMessage());
        }
    }
}
