package SasDaos;

/**
 *
 * <AUTHOR>
 */
public class ProcessaGuiaDao {

//    String data_sql = Utilidades.DataAtual.getDataAtual("SQL");
//    String hrsaida = Utilidades.DataAtual.getDataAtual("HORA");
//    private String sql;
    /**
     * Delete Guias de volume cxf
     *
     * @param guia - Série da guia
     * @param serie - Número da guia
     * @param persistencia - Conexão ao banco
     * @throws java.lang.Exception - pode gerar exception
     */
//    public void deletaCxfguiasvol(Persistencia persistencia, String guia, String serie) throws Exception {
//
//        sql = "delete from cxfguiasvol "
//                + "where guia=? and Serie=?";
//        try {
//            Consulta consulta = new Consulta(sql, persistencia);
//            Mstat.setString(1, guia);
//            Mstat.setString(2, serie);
//            Mstat.executeUpdate();
//            Mstat.close();
//        } catch (SQLException e) {
//            throw new RuntimeException(e);
//        }
//    }
    /**
     * Atualiza rota percorrida
     *
     * @param persistencia - Conexão ao banco
     * @param sequencia - Seqüência de Rota
     * @param valor - Valor
     * @param parada - Número da parada
     * @throws java.lang.Exception - pode gerar exception
     */
//    public void atualizaRt_Perc(Persistencia persistencia, String sequencia, String valor, String parada) throws Exception {
//
//        sql = "UPDATE Rt_Perc set Valor=?"
//                + " WHERE Sequencia=? and Parada=?";
//        try {
//            Consulta consulta = new Consulta(sql, persistencia);
//            Mstat.setString(1, valor);
//            Mstat.setString(2, sequencia);
//            Mstat.setString(3, parada);
//            Mstat.executeUpdate();
//            Mstat.close();
//        } catch (SQLException e) {
//            throw new RuntimeException(e);
//        }
//    }
    /**
     * Adiciona guia
     *
     * @param persistencia - Conexão ao banco
     * @param sequencia - Seqüência de Rota
     * @param parada - Número da Parada
     * @param guia - Número da guia
     * @param serie - Número série
     * @param serieAnt - Número série anterior
     * @param valor - Valor
     * @param OS - Número OS
     * @param KM - Km
     * @param KMTerra - KmTerra
     * @param Operador - Operador
     * @param Dt_Alter - Data alteração
     * @param Hr_Alter - Hora alteração
     * @throws java.lang.Exception - pode gerar exception
     */
//    public void AdicionaRt_Guia(Persistencia persistencia, String sequencia, String parada,
//            String guia, String serie, String valor, String OS, String KM, String KMTerra,
//            String Operador, String Dt_Alter, String Hr_Alter) throws Exception {
//        sql = "insert into Rt_Guias (Sequencia,Parada,Guia,Serie,SerieAnt,"
//                + "Valor,OS,KM,KMTerra,Operador,Dt_Alter,Hr_Alter) "
//                + " values (?,?,?,?,?,?,?,?,?,?,?,?)";
//        try {
//            Consulta consulta = new Consulta(sql, persistencia);
//            Mstat.setString(1, sequencia);
//            Mstat.setInt(2, Integer.parseInt(parada));
//            Mstat.setString(3, guia);
//            Mstat.setString(4, serie);
//            Mstat.setString(5, serie);
//            Mstat.setString(6, valor);
//            Mstat.setString(7, OS);
//            Mstat.setString(8, KM);
//            Mstat.setString(9, KMTerra);
//            Mstat.setString(10, Operador);
//            Mstat.setString(11, Dt_Alter);//java.sql.Date.valueOf(Dt_Alter));
//            Mstat.setString(12, Hr_Alter);
//            Mstat.execute();
//            Mstat.close();
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//    }
    /**
     * Atualiza Rt_Guia
     *
     * @param persistencia - Conexão ao banco
     * @param valor - Valor
     * @param sequencia - Seqüência de Rota
     * @param parada - Número da Parada
     * @param guia - Número da guia
     * @param serie - Número série
     * @throws java.lang.Exception - pode gerar exception
     */
//    public void AtualizaRt_Guia(Persistencia persistencia, String valor, String sequencia, String parada,
//            String guia, String serie) throws Exception {
//
//        sql = "update Rt_Guias "
//                + " set Valor = ?"
//                + " where sequencia=?"
//                + " and parada=?"
//                + " and Guia=?"
//                + " and Serie=?";
//
//        try {
//            Consulta consulta = new Consulta(sql, persistencia);
//            Mstat.setString(1, valor);
//            Mstat.setString(2, sequencia);
//            Mstat.setString(3, parada);
//            Mstat.setString(4, guia);
//            Mstat.setString(5, serie);
//            Mstat.executeUpdate();
//            Mstat.close();
//            persistencia.FechaConexao();
//        } catch (SQLException e) {
//            throw new RuntimeException(e);
//        }
//    }
    /**
     * Adiciona GTV
     *
     * @param persistencia - Conexão ao banco
     * @param CodFil - Codigo filial
     * @param guia - Número da guia
     * @param serie - Número série
     * @param rota - Seqüência de Rota
     * @param parada - Número da Parada
     * @param OS - Número OS
     * @param Dt_alter - Data alteração
     * @param Hr_Alter - Hora alteração
     * @param obs - Observação
     * @throws java.lang.Exception - pode gerar exception
     */
//    public void AdicionaGTV(Persistencia persistencia, String CodFil, String guia, String serie,
//            String rota, String parada, BigDecimal OS, String Dt_alter, String Hr_Alter, String obs) throws Exception {
//
//        sql = "insert into GTV (CodFil,Guia,Serie,SeqRota,Parada,Situacao,OS,Operador,Dt_Alter,Hr_Alter,obs) "
//                + "values (?,?,?,?,?,8,?,'SATMOB',?,?,?)";
//
//        try {
//            Consulta consulta = new Consulta(sql, persistencia);
//            Mstat.setString(1, CodFil);
//            Mstat.setString(2, guia);
//            Mstat.setString(3, serie);
//            Mstat.setString(4, rota);
//            Mstat.setString(5, parada);
//            Mstat.setBigDecimal(6, OS);
//            Mstat.setString(7, Dt_alter);
//            Mstat.setString(8, Hr_Alter);
//            Mstat.setString(9, obs);
//            Mstat.execute();
//            Mstat.close();
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//    }
    /**
     * Atualiza GTV
     *
     * @param persistencia - Conexão ao banco
     * @param CodFil - Codigo filial
     * @param guia - Número da guia
     * @param serie - Número série
     * @param Dt_alter - Data alteração
     * @param Hr_Alter - Hora alteração
     * @param obs - Observação
     * @throws java.lang.Exception - pode gerar exception //
     */
//    public void atualizaGTV(Persistencia persistencia, String Dt_alter, String Hr_Alter, String obs,
//            BigDecimal CodFil, String guia, String serie) throws Exception {
//        sql = "update GTV set Situacao='8', "
//                + " Operador='SATMOB', "
//                + " Dt_Alter= ?"
//                + " Hr_Alter= ?"
//                + " Obs= ?"
//                + " where CodFil= ?"
//                + " Guia=?"
//                + " Serie=?";
//        try {
//            Consulta consulta = new Consulta(sql, persistencia);
//            Mstat.setString(1, Dt_alter);
//            Mstat.setString(2, Hr_Alter);
//            Mstat.setString(3, obs);
//            Mstat.setBigDecimal(4, CodFil);
//            Mstat.setString(5, guia);
//            Mstat.setString(6, serie);
//            Mstat.executeUpdate();
//            Mstat.close();
//            persistencia.FechaConexao();
//        } catch (SQLException e) {
//            throw new RuntimeException(e);
//        }
//    }
    /**
     * Verifica Os guias
     *
     * @param persistencia - Conexão ao banco
     * @param sequencia - Número da sequência
     * @param parada - Número da Parada
     * @return - Retorna um codcli1
     * @throws java.lang.Exception - pode gerar exception
     */
//    public String verificaOSGuia(Persistencia persistencia, String sequencia, String parada) throws Exception {
//        String codcli1 = "";
//        sql = "select codcli1 "
//                + " from Rt_Perc "
//                + " where sequencia=?"
//                + " and parada=?";
//        try {
//            Consulta rs = new Consulta(sql, persistencia);
//            rs.setString(sequencia);
//            rs.setInt(Integer.parseInt(parada));
//            rs.Executa();
//            while (rs.Proximo()) {
//                codcli1 = rs.getString("codcli1");
//            }
//            rs.Close();
//        } catch (SQLException e) {
//            throw new RuntimeException(e);
//        }
//        return codcli1;
//    }
    /**
     * Valida séries guias
     *
     * @param persistencia - Conexão ao banco
     * @param serie - Número da sequência
     * @return - Retorna boolean
     * @throws java.lang.Exception - pode gerar exception
     */
//    public boolean validaSerieGuia(Persistencia persistencia, String serie) throws Exception {
//        boolean retornar = false;
//        String sql = "select top 1 Codigo from GTVSeq "
//                + "where Serie=?";
//        try {
//            Consulta validaSG = new Consulta(sql, persistencia);
//            validaSG.setString(serie);
//            validaSG.Executa();
//            while (validaSG.Proximo()) {
//                retornar = true;
//            }
//            validaSG.Close();
//        } catch (SQLException e) {
//            throw new RuntimeException(e);
//        }
//        return retornar;
//    }
    /**
     * Compara clientes OS
     *
     * @param persistencia - Conexão ao banco
     * @param OS - Ordem de serviço
     * @param Cliente - Código cliente
     * @return - Retorna boolean
     * @throws java.lang.Exception - pode gerar exception
     */
//    public boolean ComparaClientesOS(Persistencia persistencia, BigDecimal OS, String Cliente) throws Exception {
//        boolean retorno;
//        String cliente = "";
//        String sql = "select Cliente from OS_Vig where OS=?";
//        try {
//            Consulta validaSG = new Consulta(sql, persistencia);
//            validaSG.setBigDecimal(OS);
//            validaSG.Executa();
//            while (validaSG.Proximo()) {
//                cliente = validaSG.getString(1);
//            }
//            retorno = cliente.equals(Cliente);
//            validaSG.Close();
//        } catch (SQLException e) {
//            throw new RuntimeException(e);
//        }
//        return retorno;
//    }
}
