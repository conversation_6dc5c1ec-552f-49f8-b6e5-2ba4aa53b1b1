/*
 */
package br.com.sasw.satmobew.mensagem;

import br.com.sasw.pacotesuteis.utilidades.Extenso;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.PreencheEsquerda;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarString;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.ResourceBundle;

/**
 *
 * <AUTHOR>
 */
public class Messages {
    
    private String idioma;
    private Locale locale;

    public Messages() {
    }

    public Messages(String idioma) {
        this.idioma = idioma;
        this.locale = new Locale(idioma);
    }
    
    public String getMessage(String key) {
        String message = "";
        try{
            message = ResourceBundle.getBundle("br.com.sasw.satmobew.mensagem.messages", this.locale).getString(key);
        } catch (Exception e){
            message = key;
        }
        return message;
    }
    
    /**
     * Retorna o valor do número por extenso (moeda)
     * @param valor
     * @return 
     */
    public String getValorExtenso(String valor) {
        String message;
        try{
            switch(idioma){
                case "en":
                    String[] cents = valor.split("\\.");
                    long dollars = Long.parseLong(cents[0]);
                    long cent = Long.parseLong(cents[1]) ;
                    valor = Extenso.valorPorExtensoIngles(dollars) + " dollars" + ((cent == 0) ? "" : " and " +Extenso.valorPorExtensoIngles( cent ) + " cents");
                    valor = "("+valor.toUpperCase()+" "+PreencheEsquerda("/", 40, "/ ")+")";
                    message = valor;
                    break;
                default:
                    message = "("+Extenso.valorPorExtenso(Double.parseDouble(valor)).toUpperCase()+" "+PreencheEsquerda("/", 40, "/ ")+")";
            }
        } catch (Exception e){
            message = valor;
        }
        return message;
    }
    
    /**
     * Retorna o número de telefone formatado
     * @param fone
     * @return 
     */
    public String getTelefone(String fone) {
        try {
            switch (this.idioma) {
                case "PT":
                    switch (fone.length()) {
                        case 7:
                            return formatarString(fone, "###-####");
                        case 8:
                            return formatarString(fone, "####-####");
                        case 9:
                            return formatarString(fone, "(##) ###-####");
                        case 10:
                            return formatarString(fone, "(##) ####-####");
                        case 11:
                            return formatarString(fone, "(##) #####-####");
                        case 12:
                            return formatarString(fone, "+############");
                        case 13:
                            return formatarString(fone, "+#############");
                        default:
                            break;
                    }
                    break;

                case "EN":
                    return formatarString(fone, "(###) ###-####");
            }
        } catch (Exception e) {
        }
        return fone;
    }
    
    public String getCEP(String cep) {
        String message;
        try{
            switch(idioma){
                case "en":
                    message = cep.startsWith("000") ? cep.replace("000", "") : cep;
                    break;
                default:
                    message = formatarString(cep, "#####-###");
            }
        } catch (Exception e){
            message = cep;
        }
        return message;
    }
    
    /**
     * Retorna a data no formato do idioma
     * @param data
     * @param formatoEntrada - formato de entrada: yyyy-MM-dd, yyyyMMdd, etc...
     * @return 
     */
    public String getData(String data, String formatoEntrada) {
        String message;
        try{
            LocalDate  datetime = LocalDate.parse(data, DateTimeFormatter.ofPattern(formatoEntrada));
            switch(idioma){
                case "en":
                    message = datetime.format(DateTimeFormatter.ofPattern("MM/dd/yyyy"));
                    break;
                default:
                    message = datetime.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"));
            }
        } catch (Exception e){
            message = data;
        }
        return message;
    }

    public void setIdioma(String idioma) {
        this.idioma = idioma;
        this.locale = new Locale(idioma);
    }
}
