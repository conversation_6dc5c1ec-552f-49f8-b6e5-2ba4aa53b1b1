/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satmobew.relatorio;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.PstLstRelat;
import SasBeansCompostas.LogsSatMobEW;
import SasDaos.PstLstRelatDao;
import static SasLibrary.ValidarUsuario.ValidaUsuarioPonto;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import Xml.Xmls;
import br.com.sasw.satmobew.ValidarUsuario;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "ObterMotivos", urlPatterns = {"/relatorio/ObterMotivos"})
public class ObterMotivos extends HttpServlet {
    
    private ArquivoLog logerro;
    private SasPoolPersistencia pool;
    
    /**
     * Cria a pool de persistencias na criação da servlet
     */
    @Override
    public void init(){
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter resp = response.getWriter(); 
            
        String resposta = "<?xml version=\"1.0\"?>";
        String param = request.getParameter("param");
        String codPessoa = request.getParameter("codpessoa");
        String senha = request.getParameter("senha");
        String dataAtual = request.getParameter("dataAtual");
        String horaAtual = request.getParameter("horaAtual");
        String codFil = request.getParameter("codfil");
        String secao = request.getParameter("secao") == null ? "" : request.getParameter("secao");
        logerro = new ArquivoLog();
        
        if(null == dataAtual || dataAtual.equals("")) dataAtual = DataAtual.getDataAtual("SQL");
        if(null == horaAtual || horaAtual.equals("")) horaAtual = DataAtual.getDataAtual("HORA");
        
        try {
            // Gera o trace com os valores da requisição
            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codPessoa,  param, logerro);
            tStart = System.currentTimeMillis();
            
            //Cria a persistencia
            Persistencia persistencia = pool.getConexao(param);
            
            if (ValidaUsuarioPonto(codPessoa, senha, persistencia)) {
                PstLstRelatDao ptLstRelatDao = new PstLstRelatDao();
                List<PstLstRelat> pstLstRelats = ptLstRelatDao.getRelatorios(secao, codFil, persistencia);
                StringBuilder mot, mots = new StringBuilder();
                for(PstLstRelat pstLstRelat : pstLstRelats){
                    mot = new StringBuilder();
                    pstLstRelat = (PstLstRelat) FuncoesString.removeAcentoObjeto(pstLstRelat);
                    mot.append(Xmls.tag("secao",pstLstRelat.getSecao()));
                    mot.append(Xmls.tag("codigo",pstLstRelat.getCodigo()));
                    mot.append(Xmls.tag("descricao",pstLstRelat.getDescricao()));
                    mot.append(Xmls.tag("foto",pstLstRelat.isFoto()));
                    mot.append(Xmls.tag("video",pstLstRelat.isVideo()));
                    mots.append(Xmls.tag("motivos", mot.toString()));
                }
                resposta += mots.toString();
            }
            else {
                resposta += Xmls.tag("resp", "2");//erro ao validar usuáio
            }
            resp.print(resposta);
            // Opcional: gera um trace com a resposta do servidor
            Trace.gerarTrace(getServletContext(), this.getServletName(),resposta, codPessoa, param, logerro);
            
            // Fecha a conexão
            persistencia.FechaConexao();
            
            // Calcula o tempo gasto pelo servidor entre criar a persistencia e enviar a respota de volta ao android e gera o trace disso.
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0)+" segundos.\r\n", codPessoa, param, logerro); 
        } catch (Exception e) {
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Falha - "+ e.getMessage(), codPessoa, param, logerro);
            resp.print("<?xml version=\"1.0\"?>" + Xmls.tag("resp", "0")+Xmls.tag("erro", e.getMessage()));
        }
    }

    private static void order(List<LogsSatMobEW> logs) {
        Collections.sort(logs, (Object o1, Object o2) -> {
            int sComp;
            try{
                LocalDate data1 = LocalDate.parse(((LogsSatMobEW) o1).getData(),DateTimeFormatter.ofPattern("yyyyMMdd"));
                LocalDate data2 = LocalDate.parse(((LogsSatMobEW) o2).getData(),DateTimeFormatter.ofPattern("yyyyMMdd"));
                sComp = data2.compareTo(data1);
            } catch (Exception e){
                sComp = -1;
            }
            
            if (sComp != 0) {
                return sComp;
            } else {
                try{
                    LocalTime hora1 = LocalTime.parse(((LogsSatMobEW) o1).getHora(),DateTimeFormatter.ofPattern("HH:mm"));
                    LocalTime hora2 = LocalTime.parse(((LogsSatMobEW) o2).getHora(),DateTimeFormatter.ofPattern("HH:mm"));
                    return hora2.compareTo(hora1);
                } catch (Exception e){
                    return -1;
                }
            }
        });
    }
    
    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

}
