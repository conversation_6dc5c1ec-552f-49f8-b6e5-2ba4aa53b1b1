package br.com.sasw.satmobew;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.RastrearEW;
import SasDaos.RastrearEWDao;
import static SasLibrary.ValidarUsuario.ValidaUsuarioPonto;
import Xml.Xmls;
import br.com.sasw.pacotesuteis.sasbeans.ComunicacoesDao;
import br.com.sasw.pacotesuteis.sasdaos.Comunicacoes;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.List;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "Rastrear", urlPatterns = {"/Rastrear"})
public class Rastrear extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;

    @Override
    public void init() {
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/plain");
        PrintWriter out = response.getWriter();
        String resposta = "<?xml version=\"1.0\"?>";

        String param = request.getParameter("param");
        String codpessoa = request.getParameter("codpessoa");
        String senha = request.getParameter("senha");
        String dataAtual = request.getParameter("dataAtual");
        String horaAtual = request.getParameter("horaAtual");

        String SEPARADOR = request.getParameter("separador");

        if (null == SEPARADOR || SEPARADOR.equals("")) {
            SEPARADOR = ";";
        }

        String[] pessoa = request.getParameter("pessoa").split(SEPARADOR);
        String[] codContato = request.getParameter("codContato").split(SEPARADOR);
        String[] latitude = request.getParameter("latitude").split(SEPARADOR);
        String[] longitude = request.getParameter("longitude").split(SEPARADOR);
        String[] data = request.getParameter("data").split(SEPARADOR);
        String[] hora = request.getParameter("hora").split(SEPARADOR);
        String[] acuracia = request.getParameter("acuracia").split(SEPARADOR);
        String[] origem = request.getParameter("origem").split(SEPARADOR);

        if (null == dataAtual || dataAtual.equals("")) {
            dataAtual = DataAtual.getDataAtual("SQL");
        }
        if (null == horaAtual || horaAtual.equals("")) {
            horaAtual = DataAtual.getDataAtual("HORA");
        }

        logerro = new ArquivoLog();

        try {
            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codpessoa, param, logerro);
            tStart = System.currentTimeMillis();

            Persistencia persistencia = pool.getConexao(param);

            if (null != persistencia) {
                if (ValidaUsuarioPonto(codpessoa, senha, persistencia)) {
                    resposta += Xmls.tag("resp", "1");
                    RastrearEWDao rastrearEWDao = new RastrearEWDao();
                    RastrearEW rastrearEW;
                    for (int i = 0; i < pessoa.length; i++) {
                        try {
                            rastrearEW = new RastrearEW();

                            rastrearEW.setHrTransf(horaAtual);
                            rastrearEW.setDtTransf(dataAtual);

                            rastrearEW.setCodPessoa(pessoa[i]);
                            rastrearEW.setCodContato(codContato[i]);
                            rastrearEW.setLatitude(latitude[i]);
                            rastrearEW.setLongitude(longitude[i]);
                            rastrearEW.setData(data[i]);
                            rastrearEW.setHora(hora[i]);
                            rastrearEW.setAcuracia(acuracia[i]);
                            rastrearEW.setOrigem(origem[i]);

                            rastrearEWDao.inserirRastrear(rastrearEW, persistencia);
                        } catch (Exception x) {
                            Trace.gerarTrace(getServletContext(), this.getServletName(), "Falha - " + x.getMessage(), codpessoa, param, logerro);
                        }
                    }
                } else {
                    resposta += Xmls.tag("resp", "2");//erro ao validar usuáio
                }
            }
                    
            try{
                StringBuilder aux;
                ComunicacoesDao comunicacoesDao = new ComunicacoesDao();
                List<Comunicacoes> comunicacoes = comunicacoesDao.listarComunicacoes(codpessoa, persistencia);
                for(Comunicacoes comunicacao : comunicacoes){
                    try{
                        aux = new StringBuilder();
                        aux.append(Xmls.tag("Numero", comunicacao.getNumero().replace(".0","")));
                        aux.append(Xmls.tag("DtCadastro", comunicacao.getDtCadastro()));
                        aux.append(Xmls.tag("HrCadastro", comunicacao.getHrCadastro()));
                        aux.append(Xmls.tag("Remetente", comunicacao.getRemetente()));
                        aux.append(Xmls.tag("Detalhes", comunicacao.getDetalhes()));

                        comunicacoesDao.marcarMensagemEnviada(comunicacao.getNumero(), persistencia);
                        
                        resposta += Xmls.tag("mensagem", aux.toString());
                    } catch (Exception x2){
                        Trace.gerarTrace(getServletContext(), this.getServletName(), "Falha - " + x2.getMessage(), codpessoa, param, logerro);
                    }
                    
                }
            } catch (Exception x) {
                Trace.gerarTrace(getServletContext(), this.getServletName(), "Falha - " + x.getMessage(), codpessoa, param, logerro);
            }

            persistencia.FechaConexao();
            out.print(resposta);
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0) + " segundos.\r\n", codpessoa, param, logerro);
        } catch (Exception e) {
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Falha - " + e.getMessage(), codpessoa, param, logerro);
            //define saida por texto
            response.setContentType("text/plain");
            //escreve a resposta no buffer de saida
            out.print("<?xml version=\"1.0\"?><resp>0</resp><erro>" + e.getMessage() + "</erro>");
        } finally {
            Trace.Erros(getServletContext(), request, logerro);
            out.close();
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

}
