/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PropServItens;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PropServItensDao {

    /**
     * Lista os seriços de uma proposta
     *
     * @param numero
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PropServItens> listarServicos(BigDecimal numero, Persistencia persistencia) throws Exception {
        List<PropServItens> retorno = new ArrayList<>();
        try {
            String sql = "select * from PropServItens where numero = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(numero);
            consulta.select();
            PropServItens servico;
            while (consulta.Proximo()) {
                servico = new PropServItens();
                servico.setNumero(consulta.getBigDecimal("Numero"));
                servico.setCodFil(consulta.getBigDecimal("CodFil"));
                servico.setOrdem(consulta.getBigDecimal("Ordem"));
                servico.setTipoPosto(consulta.getString("TipoPosto"));
                servico.setTipoCalc(consulta.getString("TipoCalc"));
                servico.setDescricao(consulta.getString("Descricao"));
                servico.setValor(consulta.getBigDecimal("Valor"));
                servico.setValorRot(consulta.getBigDecimal("ValorRot"));
                servico.setFranquiaRot(consulta.getBigDecimal("FranquiaRot"));
                servico.setValorEve(consulta.getBigDecimal("ValorEve"));
                servico.setFranquiaEve(consulta.getBigDecimal("FranquiaEve"));
                servico.setValorEsp(consulta.getBigDecimal("ValorEsp"));
                servico.setFranquiaEsp(consulta.getBigDecimal("FranquiaEsp"));
                servico.setValorAst(consulta.getBigDecimal("ValorAst"));
                servico.setFranquiaAst(consulta.getBigDecimal("FranquiaAst"));
                servico.setOS(consulta.getBigDecimal("OS"));
                servico.setOperIncl(consulta.getString("OperIncl"));
                servico.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
                servico.setHr_Incl(consulta.getString("Hr_Incl"));
                servico.setOperador(consulta.getString("Operador"));
                servico.setDt_Alter(consulta.getString("Dt_Alter"));
                servico.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(servico);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Failed to list serviços - " + e.getMessage());
        }
    }
}
