package br.com.sasw.satmobew;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Clientes;
import SasBeans.Funcion;
import SasBeans.InspecoesItens;
import SasBeans.Veiculos;
import SasBeansCompostas.Login;
import SasBeansCompostas.PstServTipoPosto;
import SasDaos.ClientesDao;
import SasDaos.FuncionDao;
import SasDaos.InspecoesItensDao;
import SasDaos.LoginDao;
import SasDaos.PstServDao;
import SasDaos.RHEscalaDao;
import SasDaos.VeiculosDao;
import Xml.Xmls;
import br.com.sasw.pacotesuteis.sasbeans.InspecoesItensLista;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.removeAcentoObjeto;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "ValidarPosto", urlPatterns = {"/ValidarPosto"})
public class ValidarPosto extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;

    @Override
    public void init() {
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/plain");
        PrintWriter out = response.getWriter();

        String param = request.getParameter("param");
        String senha = request.getParameter("senha");
        String secao = request.getParameter("secao");
        String codfil = request.getParameter("codfil");
        String cliente = request.getParameter("cliente");
        String codpessoa = request.getParameter("codpessoa");
        String nomepessoa = request.getParameter("nomepessoa");
        String dataAtual = request.getParameter("dataAtual");
        String horaAtual = request.getParameter("horaAtual");
        String latitudeMobile = request.getParameter("latitude");
        String longitudeMobile = request.getParameter("longitude");

        if (null == dataAtual || dataAtual.equals("")) {
            dataAtual = DataAtual.getDataAtual("SQL");
        }
        if (null == horaAtual || horaAtual.equals("")) {
            horaAtual = DataAtual.getDataAtual("HORA");
        }

        logerro = new ArquivoLog();

        try {
            long tStart = 0, tEnd;
            tStart = System.currentTimeMillis();

            String xml = "<?xml version=\"1.0\"?>";
            Persistencia persistencia = pool.getConexao(param);

            if (null != persistencia) {
                Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codpessoa, param, logerro);

                boolean alteracaoBase = false;
                String novoParam = null;
                boolean prestador = codpessoa.length() == 6 && codpessoa.charAt(0) == '8';
                List<Login> login = LoginDao.LoginMobilePonto(codpessoa, nomepessoa, prestador,
                        codfil, secao, dataAtual, persistencia);

                if (login.isEmpty() || (!senha.equals(login.get(0).getPessoa().getPWWeb()) && !senha.equals(login.get(0).getPessoa().getPWPortal())) && (persistencia.getEmpresa().equals("SATINTERFORT") || persistencia.getEmpresa().equals("SATSERVITE"))) {
                    alteracaoBase = true;

                    if (persistencia.getEmpresa().equals("SATSERVITE")) {
                        persistencia = pool.getConexao("SATINTERFORT");
                        novoParam = "SATINTERFORT";
                    } else {
                        persistencia = pool.getConexao("SATSERVITE");
                        novoParam = "SATSERVITE";
                    }

                    login = LoginDao.LoginMobilePonto(codpessoa, nomepessoa, prestador, codfil, secao, dataAtual, persistencia);
                }

                if (login.isEmpty()) {
                    Trace.gerarTrace(getServletContext(), this.getServletName(), "Codigo incorreto", codpessoa, param, logerro);
                    xml += "<resp>mobilelogin_1</resp>"; //codigo incorreto
                } else if (!senha.equals(login.get(0).getPessoa().getPWWeb()) && !senha.equals(login.get(0).getPessoa().getPWPortal())) {
                    Trace.gerarTrace(getServletContext(), this.getServletName(), "Senha incorreta - " + codpessoa, codpessoa, param, logerro);
                    xml += "<resp>mobilelogin_3</resp>"; //senha incorreta
                } else {

                    if (alteracaoBase) {
                        ClientesDao clientesDao = new ClientesDao();
                        List<Clientes> clientes = clientesDao.listaClientesDistancia(latitudeMobile, longitudeMobile, "10", persistencia);

                        if (clientes.size() == 0) {
                            clientes = clientesDao.listaClientesDistancia(latitudeMobile, longitudeMobile, "100", persistencia);

                            if (clientes.size() == 0) {
                                clientes = clientesDao.listaClientesDistancia(latitudeMobile, longitudeMobile, "1000", persistencia);

                                if (clientes.size() == 0) {
                                    clientes = clientesDao.listaClientesDistancia(latitudeMobile, longitudeMobile, "1=2000", persistencia);
                                }
                            }
                        }

                        StringBuilder clis = new StringBuilder(), aux;
                        for (Clientes cli : clientes) {
                            aux = new StringBuilder();
                            aux.append(Xmls.tag("codfil", cli.getCodFil().toBigInteger()))
                                    .append(Xmls.tag("nred", cli.getNRed()))
                                    .append(Xmls.tag("ende", cli.getEnde()))
                                    .append(Xmls.tag("codigo", cli.getCodCli()))
                                    .append(Xmls.tag("lat", cli.getLatitude()))
                                    .append(Xmls.tag("lon", cli.getLongitude()))
                                    .append(Xmls.tag("dist", cli.getLimite()))
                                    .append(Xmls.tag("filial", cli.getInterfExt()));
                            clis.append(Xmls.tag("cliente", aux.toString()));
                        }
                        xml += "<resp>3</resp>";
                        xml += Xmls.tag("data", clis.append(Xmls.tag("parametro", novoParam)).toString());
                    } else if (prestador) {
                        PstServDao pstServDao = new PstServDao();
                        List<PstServTipoPosto> postos = pstServDao.listarPostosClienteSatMobEW(codfil, cliente, persistencia);

                        StringBuilder pst = new StringBuilder(), aux;
                        for (PstServTipoPosto posto : postos) {
                            if (posto.getPstServ().getSecao().equals(secao)) {
                                aux = new StringBuilder();
                                aux.append(Xmls.tag("codfil", posto.getPstServ().getCodFil().toBigInteger()))
                                        .append(Xmls.tag("secao", posto.getPstServ().getSecao()))
                                        .append(Xmls.tag("local", posto.getPstServ().getLocal()))
                                        .append(Xmls.tag("ende", posto.getPstServ().getEndereco()))
                                        .append(Xmls.tag("descricao", posto.getPstServ().getDescContrato()));
                                pst.append(Xmls.tag("pstserv", aux.toString()));
                            }
                        }
                        xml += "<resp>2</resp>";
                        xml += Xmls.tag("data", pst.toString());
                    } else {
                        String dtCompet = dataAtual;
                        DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");
                        DateTimeFormatter HHmm = DateTimeFormatter.ofPattern("HH:mm");
                        DateTimeFormatter yyyyMMddHHmm = DateTimeFormatter.ofPattern("yyyyMMddHH:mm");
                        FuncionDao funcionDao = new FuncionDao();

                        /**
                         * Se a batida for antes do meio dia e não houverem
                         * batidas anteriores, verificar se no dia da semana
                         * anterior há previsão de trabalho diuturno (se for
                         * quarta verificar parâmetro de terça). Nesse caso o
                         * sistema deve considerar que é saída do dia anterior.
                         * Essa batida receberá DtCompet do dia anterior.
                         */
                        LocalTime h = LocalTime.parse(horaAtual, HHmm);
                        if (h.isBefore(LocalTime.parse("08:00", HHmm))) {
                            /**
                             * 0 - Domingo 1 1 - Segunda 1 2 - Terça 1 3 -
                             * Quarta 1 4 - Quinta 1 5 - Sexta 1 6 - Sábado 1 7
                             * - Feriado 1 = 11111111
                             */
                            String diuturno = funcionDao.getDiuTurnoFuncion(login.get(0).getFuncion().getMatr().toPlainString(), persistencia);

                            /**
                             * 1 - segunda, 2 - terça, 3 - quarta, 4 - quinta, 5
                             * - sexta, 6 - sábado, 7 - domingo
                             */
                            int ontem;
                            if (LocalDate.parse(dataAtual, yyyyMMdd).getDayOfWeek().getValue() == 7) {
                                ontem = 0;
                            } else {
                                ontem = LocalDate.parse(dataAtual, yyyyMMdd).getDayOfWeek().getValue() - 1;
                            }

                            /**
                             * Verifica se a escala do dia anterior é notura.
                             */
                            if (diuturno.charAt(ontem) == '1') {
                                dtCompet = LocalDate.parse(dataAtual, yyyyMMdd).minusDays(1).format(yyyyMMdd);
                            }
                        }

                        List<Login> loginEW;
                        if (dtCompet.equals(dataAtual)) {
                            loginEW = login;
                        } else {
                            if (null != secao) {
                                loginEW = LoginDao.LoginMobilePonto(codpessoa, nomepessoa, false, codfil, secao, dtCompet, persistencia);
                            } else {
                                loginEW = LoginDao.LoginMobilePonto(codpessoa, nomepessoa, dtCompet, persistencia);
                            }
                        }

                        if (!loginEW.get(0).getFuncion().getSecao().equals(secao) || "".equals(secao)) {
                            boolean supervisor = LoginDao.isSupervisorRondas(loginEW.get(0).getPessoa().getCodigo().toPlainString(), persistencia);

                            PstServDao pstServDao = new PstServDao();
                            List<PstServTipoPosto> postos = pstServDao.listarPostosClienteSatMobEW(codfil, cliente, persistencia);

                            StringBuilder pst = new StringBuilder(), aux, sugestoes = new StringBuilder();
                            boolean sugerir = true;
                            if (param.contains("PROSECUR") || param.contains("GLOVAL")) {
                                RHEscalaDao rhEscalaDao = new RHEscalaDao();
                                long tolerancia = rhEscalaDao.getTolerancia(loginEW.get(0).getFuncion().getMatr().toPlainString(), persistencia);

                                LocalTime hrInicioPosto, hrFimPosto;
                                LocalDateTime hrDateInicioPosto, hrDateFimPosto, hrDateAtual = LocalDateTime.parse(dtCompet + horaAtual, yyyyMMddHHmm);
                                boolean tolerado;

                                for (PstServTipoPosto posto : postos) {
                                    BigDecimal totalHorasDias = BigDecimal.ZERO;
                                    try {
                                        /**
                                         * 1 - segunda, 2 - terça, 3 - quarta, 4
                                         * - quinta, 5 - sexta, 6 - sábado, 7 -
                                         * domingo
                                         */
                                        switch (LocalDate.parse(dtCompet, yyyyMMdd).getDayOfWeek().getValue()) {
                                            case 1:
                                                totalHorasDias = totalHorasDias.add(new BigDecimal(posto.getTipoPosto().getChdiu2()));
                                                totalHorasDias = totalHorasDias.add(new BigDecimal(posto.getTipoPosto().getChnot2()));
                                                break;
                                            case 2:
                                                totalHorasDias = totalHorasDias.add(new BigDecimal(posto.getTipoPosto().getChdiu3()));
                                                totalHorasDias = totalHorasDias.add(new BigDecimal(posto.getTipoPosto().getChnot3()));
                                                break;
                                            case 3:
                                                totalHorasDias = totalHorasDias.add(new BigDecimal(posto.getTipoPosto().getChdiu4()));
                                                totalHorasDias = totalHorasDias.add(new BigDecimal(posto.getTipoPosto().getChnot4()));
                                                break;
                                            case 4:
                                                totalHorasDias = totalHorasDias.add(new BigDecimal(posto.getTipoPosto().getChdiu5()));
                                                totalHorasDias = totalHorasDias.add(new BigDecimal(posto.getTipoPosto().getChnot5()));
                                                break;
                                            case 5:
                                                totalHorasDias = totalHorasDias.add(new BigDecimal(posto.getTipoPosto().getChdiu6()));
                                                totalHorasDias = totalHorasDias.add(new BigDecimal(posto.getTipoPosto().getChnot6()));
                                                break;
                                            case 6:
                                                totalHorasDias = totalHorasDias.add(new BigDecimal(posto.getTipoPosto().getChdiu7()));
                                                totalHorasDias = totalHorasDias.add(new BigDecimal(posto.getTipoPosto().getChnot7()));
                                                break;
                                            case 7:
                                                totalHorasDias = totalHorasDias.add(new BigDecimal(posto.getTipoPosto().getChdiu1()));
                                                totalHorasDias = totalHorasDias.add(new BigDecimal(posto.getTipoPosto().getChnot1()));
                                                break;
                                        }
                                    } catch (Exception e2) {
                                        totalHorasDias = BigDecimal.ZERO;
                                    }
                                    if (totalHorasDias.compareTo(BigDecimal.ZERO) != 0) {
                                        tolerado = false;
                                        try {
                                            hrInicioPosto = LocalTime.parse(posto.getPstServ().getHrPadraoIni(), HHmm);
                                            hrFimPosto = LocalTime.parse(posto.getPstServ().getHrPadraoFim(), HHmm);
                                            if (hrFimPosto.isBefore(hrInicioPosto)) {
                                                hrDateInicioPosto = LocalDateTime.parse(dtCompet + posto.getPstServ().getHrPadraoIni(), yyyyMMddHHmm);
                                                LocalDate dtAtual = LocalDate.parse(dtCompet, yyyyMMdd).plusDays(1);
                                                hrDateFimPosto = LocalDateTime.parse(dtAtual.format(yyyyMMdd) + posto.getPstServ().getHrPadraoFim(), yyyyMMddHHmm);
                                            } else {
                                                hrDateInicioPosto = LocalDateTime.parse(dtCompet + posto.getPstServ().getHrPadraoIni(), yyyyMMddHHmm);
                                                hrDateFimPosto = LocalDateTime.parse(dtCompet + posto.getPstServ().getHrPadraoFim(), yyyyMMddHHmm);
                                            }
                                            if (hrDateAtual.isAfter(hrDateInicioPosto.minusMinutes(tolerancia)) && hrDateAtual.isBefore(hrDateFimPosto.plusMinutes(tolerancia))) {
                                                tolerado = true;
                                            }
                                        } catch (Exception e) {
                                            tolerado = true;
                                        }
                                        if (tolerado || supervisor) {
                                            sugerir = false;
                                            aux = new StringBuilder();
                                            aux.append(Xmls.tag("codfil", posto.getPstServ().getCodFil().toBigInteger()))
                                                    .append(Xmls.tag("secao", posto.getPstServ().getSecao()))
                                                    .append(Xmls.tag("local", posto.getPstServ().getLocal()))
                                                    .append(Xmls.tag("ende", posto.getPstServ().getEndereco()))
                                                    .append(Xmls.tag("descricao", posto.getPstServ().getDescContrato()));
                                            pst.append(Xmls.tag("pstserv", aux.toString()));
                                        } else {
                                            aux = new StringBuilder();
                                            aux.append(Xmls.tag("sugestao", posto.getPstServ().getHrPadraoIni()));
                                            sugestoes.append(Xmls.tag("sugestoes", aux.toString()));
                                        }
                                    }
                                }
                            } else {
                                for (PstServTipoPosto posto : postos) {
                                    aux = new StringBuilder();
                                    aux.append(Xmls.tag("codfil", posto.getPstServ().getCodFil().toBigInteger()))
                                            .append(Xmls.tag("secao", posto.getPstServ().getSecao()))
                                            .append(Xmls.tag("local", posto.getPstServ().getLocal()))
                                            .append(Xmls.tag("ende", posto.getPstServ().getEndereco()))
                                            .append(Xmls.tag("descricao", posto.getPstServ().getDescContrato()));
                                    pst.append(Xmls.tag("pstserv", aux.toString()));
                                }
                            }
                            xml += "<resp>2</resp>";
                            xml += Xmls.tag("data", pst.append(sugerir ? sugestoes.toString() : "").toString());
                            //                        if(sugerir) xml += Xmls.tag("data", sugestoes.toString());
                        } else {
                            xml += "<resp>1</resp>";

                            // Regra para carregar posto padrão se Função <> "P"
                            // Obs.: Regra passada por Marcos em 25/05/2021
                            PstServDao pstServDao = new PstServDao();
                            List<PstServTipoPosto> postos = pstServDao.listarPostosClienteSatMobEW(codfil, persistencia);

                            String secaofuncion = pstServDao.buscarSecaoFuncion(codpessoa, codfil, persistencia);

                            if (null == secaofuncion || secaofuncion.equals("")) {
                                secaofuncion = secao;
                            }

                            StringBuilder pst = new StringBuilder(), aux;
                            for (PstServTipoPosto posto : postos) {
                                if (posto.getPstServ().getSecao().equals(secaofuncion)) {
                                    aux = new StringBuilder();
                                    aux.append(Xmls.tag("codfil", posto.getPstServ().getCodFil().toBigInteger()))
                                            .append(Xmls.tag("secao", posto.getPstServ().getSecao()))
                                            .append(Xmls.tag("local", posto.getPstServ().getLocal()))
                                            .append(Xmls.tag("ende", posto.getPstServ().getEndereco()))
                                            .append(Xmls.tag("descricao", posto.getPstServ().getDescContrato()));
                                    pst.append(Xmls.tag("pstserv", aux.toString()));
                                }
                            }

                            if (!pst.toString().equals("")) {
                                xml += Xmls.tag("data", pst.toString());
                            }
                        }
                    }
                }

                if (param.contains("SATPISCINAFACIL") || param.contains("SATSASEX")) {
                    InspecoesItensDao inspecoesItensDao = new InspecoesItensDao();
                    List<InspecoesItens> inspecoesItens = inspecoesItensDao.getItensInspecao("1", persistencia);
                    xml += Xmls.tag("qtdItensInspecao", inspecoesItens.size());
                    StringBuilder aux, auxLista;
                    for (InspecoesItens item : inspecoesItens) {
                        aux = new StringBuilder();
                        aux.append(Xmls.tag("codigo", item.getCodigo()))
                                .append(Xmls.tag("sequencia", item.getSequencia()))
                                .append(Xmls.tag("pergunta", item.getPergunta()))
                                .append(Xmls.tag("tiporesp", item.getTipoResp()))
                                .append(Xmls.tag("obrigatorio", item.getObrigatorio()))
                                .append(Xmls.tag("foto", item.getFoto()))
                                .append(Xmls.tag("video", item.getVideo()))
                                .append(Xmls.tag("nlista", item.getItensLista().size()));
                        for (InspecoesItensLista itemLista : item.getItensLista()) {
                            auxLista = new StringBuilder();
                            auxLista.append(Xmls.tag("codigo", itemLista.getCodigo()));
                            auxLista.append(Xmls.tag("sequencia", itemLista.getSequencia()));
                            auxLista.append(Xmls.tag("ordem", itemLista.getOrdem()));
                            auxLista.append(Xmls.tag("item", itemLista.getItem()));
                            auxLista.append(Xmls.tag("descricao", itemLista.getDescricao()));
                            aux.append(Xmls.tag("lista", auxLista.toString()));
                        }
                        xml += Xmls.tag("itens", aux.toString());
                    }

                    DateTimeFormatter dd = DateTimeFormatter.ofPattern("yyyyMMdd");
                    FuncionDao funcionDao = new FuncionDao();
                    List<Funcion> funcionarios = funcionDao.listaFuncionariosRHPontoDet(codfil, secao,
                            LocalDate.parse(dataAtual, dd).minusDays(7).format(dd), dataAtual, persistencia);
                    xml += Xmls.tag("qtdFuncionarios", funcionarios.size());

                    for (Funcion funcionario : funcionarios) {
                        aux = new StringBuilder();
                        aux.append(Xmls.tag("matr", funcionario.getMatr().toBigInteger().toString()))
                                .append(Xmls.tag("nome", funcionario.getNome()));
                        xml += Xmls.tag("funcionarios", aux.toString());
                    }

                    VeiculosDao veiculosDao = new VeiculosDao();
                    List<Veiculos> veiculos = veiculosDao.getVeiculos(codfil, persistencia);
                    StringBuilder vei, veis = new StringBuilder();
                    xml += Xmls.tag("qtdVeiculos", veiculos.size());

                    for (Veiculos veiculo : veiculos) {
                        veiculo = (Veiculos) removeAcentoObjeto(veiculo);
                        vei = new StringBuilder();
                        vei.append(Xmls.tag("numero", veiculo.getNumero()));
                        vei.append(Xmls.tag("modelo", veiculo.getPlaca() + " " + veiculo.getObs()));
                        veis.append(Xmls.tag("veiculos", vei.toString()));
                    }
                    xml += veis.toString();
                }

                out.print(xml);

                Trace.gerarTrace(getServletContext(), this.getServletName(), xml, codpessoa, param, logerro);

                persistencia.FechaConexao();

            } else {
                out.print("<?xml version=\"1.0\"?><resp>mobilelogin_6</resp>");
            }

            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0) + " segundos.\r\n", codpessoa, param, logerro);
        } catch (Exception e) {
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Falha - " + e.getMessage(), codpessoa, param, logerro);
            //define saida por texto
            response.setContentType("text/plain");
            PrintWriter resp = response.getWriter();
            //escreve a resposta no buffer de saida
            resp.print("<?xml version=\"1.0\"?><resp>0</resp><erro>" + e.getMessage() + "</erro>");
        } finally {
            Trace.Erros(getServletContext(), request, logerro);
            out.close();
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

}
