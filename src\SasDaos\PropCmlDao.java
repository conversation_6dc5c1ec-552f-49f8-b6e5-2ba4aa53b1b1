/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PropCml;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class PropCmlDao {

    /**
     * Listagem paginada de propostas para o SatMobWeb
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<PropCml> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<PropCml> retorno = new ArrayList<>();
        try {
            String sql = "SELECT  * "
                    + "FROM    ( SELECT    ROW_NUMBER() OVER ( ORDER BY propcml.numero desc ) AS RowNum, "
                    + " propcml.*, contatos.nome nomeContato, pessoa.nome nomeConsultor"
                    + "          FROM      propcml"
                    + " left join contatos on contatos.codigo = propcml.codcontato "
                    + " left join pessoa on pessoa.codigo = propcml.codpessoa "
                    + " WHERE ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + "propcml.numero IS NOT null) AS RowConstrainedResult "
                    + "WHERE   RowNum >= ?"
                    + "    AND RowNum < ? "
                    + "ORDER BY RowNum";
            Consulta consult = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.setInt(primeiro + 1);
            consult.setInt(primeiro + 1 + linhas);
            consult.select();
            PropCml proposta;
            while (consult.Proximo()) {
                proposta = new PropCml();
                proposta.setCodContato(consult.getBigDecimal("CodContato"));
                proposta.setCodFil(consult.getBigDecimal("CodFil").toBigInteger().toString());
                proposta.setCodigo(consult.getBigDecimal("Codigo"));
                proposta.setCodModelo(consult.getBigDecimal("CodModelo"));
                proposta.setCodPessoa(consult.getBigDecimal("CodPessoa"));
                proposta.setCodPessoa2(consult.getBigDecimal("CodPessoa2"));
                proposta.setContato(consult.getString("Contato"));
                proposta.setContrato(consult.getString("Contrato"));
                proposta.setData(consult.getLocalDate("Data"));
                proposta.setDescricao(consult.getString("Descricao"));
                proposta.setDescricao2(consult.getString("Descricao2"));
                proposta.setDescricao3(consult.getString("Descricao3"));
                proposta.setDescricao4(consult.getString("Descricao4"));
                proposta.setDt_Alter(consult.getString("Dt_Alter"));
                proposta.setDt_Incl(consult.getLocalDate("Dt_Incl"));
                proposta.setDtEntrega(consult.getLocalDate("DtEntrega"));
                proposta.setDtLibera(consult.getLocalDate("DtLibera"));
                proposta.setDtValidade(consult.getLocalDate("DtValidade"));
                proposta.setEmail(consult.getString("email"));
                proposta.setExtraordin(consult.getString("Extraordin"));
                proposta.setGarantia(consult.getString("Garantia"));
                proposta.setHr_Alter(consult.getString("Hr_Alter"));
                proposta.setHr_Incl(consult.getString("Hr_Incl"));
                proposta.setHrLibera(consult.getString("HrLibera"));
                proposta.setNumero(consult.getBigDecimal("Numero"));
                proposta.setObsLibera(consult.getString("ObsLibera"));
                proposta.setOperador(consult.getString("Operador"));
                proposta.setOperIncl(consult.getString("OperIncl"));
                proposta.setOperLibera(consult.getString("OperLibera"));
                proposta.setPrazoEntrega(consult.getString("PrazoEntrega"));
                proposta.setProposta(consult.getBigDecimal("Proposta"));
                proposta.setRefProp(consult.getString("RefProp"));
                proposta.setSexo(consult.getString("Sexo"));
                proposta.setSituacao(consult.getBigDecimal("Situacao"));
                if (null != proposta.getSituacao()) {
                    switch (proposta.getSituacao().intValue()) {
                        case 10:
                            proposta.setNomeSituacao("Triagem");
                            break;
                        case 20:
                            proposta.setNomeSituacao("Avaliação comercial");
                            break;
                        case 30:
                            proposta.setNomeSituacao("Avaliação operacional");
                            break;
                        case 40:
                            proposta.setNomeSituacao("Definição de custos");
                            break;
                        case 50:
                            proposta.setNomeSituacao("Elaboração");
                            break;
                        case 55:
                            proposta.setNomeSituacao("Pendente de Liberação");
                            break;
                        case 60:
                            proposta.setNomeSituacao("Analise cliente");
                            break;
                        case 70:
                            proposta.setNomeSituacao("Aprovadas");
                            break;
                        case 80:
                            proposta.setNomeSituacao("Recusadas");
                            break;
                        case 90:
                            proposta.setNomeSituacao("Contrato gerado");
                            break;
                        default:
                    }
                }
                proposta.setTipoProp(consult.getString("TipoProp"));
                proposta.setValidade(consult.getString("Validade"));
                proposta.setValorPrevFat(consult.getBigDecimal("ValorPrevFat"));
                proposta.setValorProd(consult.getBigDecimal("ValorProd"));
                proposta.setValorServ(consult.getBigDecimal("ValorServ"));
                proposta.setNomeContato(consult.getString("nomeContato"));
                proposta.setNomeConsultor(consult.getString("nomeConsultor"));
                retorno.add(proposta);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar pesquisa de propostas - \r\n" + e.getMessage());
        }
    }

    /**
     * Conta o número de produtos cadastrados no banco
     *
     * @param filtros
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer totalPropostasMobWeb(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "select count(*) total from propcml"
                    + " WHERE ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + "numero IS NOT null";
            Consulta consult = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao contar propostas - \r\n" + e.getMessage());
        }
    }

    /**
     * Busca o próximo número para uma proposta nova.
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public BigDecimal getNumero(Persistencia persistencia) throws Exception {
        try {
            String sql = "Select isnull(MAX(numero),0)+1 numero "
                    + " FROM propcml ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            BigDecimal retorno = BigDecimal.ZERO;
            while (consult.Proximo()) {
                retorno = consult.getBigDecimal("numero");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar próximo número de sequência - " + e.getMessage());
        }
    }

    /**
     * Insere uma nova proposta
     *
     * @param proposta
     * @param persistencia
     * @throws Exception
     */
    public void inserirProposta(PropCml proposta, Persistencia persistencia) throws Exception {
        try {
            String sql = "insert into propcml (numero, codfil, valorprod, data, operincl,"
                    + " operador, hr_incl, hr_alter, dt_alter, dt_incl, contato, email, descricao, situacao,"
                    + " codpessoa, codcontato, refprop, sexo, prazoentrega, garantia, validade)"
                    + " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(proposta.getNumero());
            consulta.setBigDecimal(proposta.getCodFil());
            consulta.setBigDecimal(proposta.getValorProd());
            consulta.setDate(DataAtual.LC2Date(proposta.getData()));
            consulta.setString(proposta.getOperIncl());
            consulta.setString(proposta.getOperador());
            consulta.setString(proposta.getHr_Incl());
            consulta.setString(proposta.getHr_Alter());
            consulta.setString(proposta.getDt_Alter());
            consulta.setDate(DataAtual.LC2Date(proposta.getDt_Incl()));
            consulta.setString(proposta.getContato());
            consulta.setString(proposta.getEmail());
            consulta.setString(proposta.getDescricao());
            consulta.setBigDecimal(proposta.getSituacao());
            consulta.setBigDecimal(proposta.getCodPessoa());
            consulta.setBigDecimal(proposta.getCodContato());
            consulta.setString(proposta.getRefProp());
            consulta.setString(proposta.getSexo());
            consulta.setString(proposta.getPrazoEntrega());
            consulta.setString(proposta.getGarantia());
            consulta.setString(proposta.getValidade());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao inserir proposta - " + e.getMessage());
        }
    }

    /**
     * Atualiza um proposta
     *
     * @param proposta
     * @param persistencia
     * @throws Exception
     */
    public void atualizarProposta(PropCml proposta, Persistencia persistencia) throws Exception {
        try {
            String sql = "update propcml set valorprod = ?, operador = ?, hr_alter = ?, dt_alter = ?, contato = ?,"
                    + " email = ?, descricao = ?, situacao = ?, codpessoa = ?, codcontato = ?, refprop = ?, sexo = ?,"
                    + " garantia = ?, prazoentrega = ?, validade = ?"
                    + " where numero = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(proposta.getValorProd());
            consulta.setString(proposta.getOperador());
            consulta.setString(proposta.getHr_Alter());
            consulta.setString(proposta.getDt_Alter());
            consulta.setString(proposta.getContato());
            consulta.setString(proposta.getEmail());
            consulta.setString(proposta.getDescricao());
            consulta.setBigDecimal(proposta.getSituacao());
            consulta.setBigDecimal(proposta.getCodPessoa());
            consulta.setBigDecimal(proposta.getCodContato());
            consulta.setString(proposta.getRefProp());
            consulta.setString(proposta.getSexo());
            consulta.setString(proposta.getGarantia());
            consulta.setString(proposta.getPrazoEntrega());
            consulta.setString(proposta.getValidade());
            consulta.setBigDecimal(proposta.getNumero());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar proposta - " + e.getMessage());
        }
    }

    /**
     * Busca o detalhe de um modelo de proposta
     *
     * @param codModelo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String getPropCmlModDetalhe(BigDecimal codModelo, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select detalhe"
                    + " FROM PropCmlMod"
                    + " WHERE codigo = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codModelo);
            consult.select();
            String retorno = new String();
            while (consult.Proximo()) {
                retorno = retorno + consult.getString("detalhe");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar detalhe do modelo de propostas - " + e.getMessage());
        }
    }

    /**
     * Edita o codigo de modelo de uma proposta
     *
     * @param proposta
     * @param persistencia
     * @throws Exception
     */
    public void atualiarCodModelo(PropCml proposta, Persistencia persistencia) throws Exception {
        try {
            String sql = "update propcml set codmodelo = ? "
                    + " where numero = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(proposta.getCodModelo());
            consulta.setBigDecimal(proposta.getNumero());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar modelo de proposta - " + e.getMessage());
        }
    }
}
