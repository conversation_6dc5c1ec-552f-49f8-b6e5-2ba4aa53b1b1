/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satmobew.inspecaov2;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Funcion;
import SasBeans.InspecoesItens;
import SasBeans.Veiculos;
import SasDaos.FuncionDao;
import SasDaos.InspecoesItensDao;
import SasDaos.VeiculosDao;
import static SasLibrary.ValidarUsuario.ValidaUsuarioPonto;
import Xml.Xmls;
import br.com.sasw.pacotesuteis.sasbeans.InspecoesItensLista;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import br.com.sasw.satmobew.ValidarUsuario;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "ObterItensInspecaoV2", urlPatterns = {"/inspecaov2/ObterItensInspecao"})
public class ObterItensInspecao extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;
    
    @Override
    public void init(){
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

     /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
    
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        logerro = new ArquivoLog();
            
        String codPessoa = request.getParameter("codpessoa");
        String senha = request.getParameter("senha");
        String param = request.getParameter("param");
        String codigo = request.getParameter("codigo");
        String secao = request.getParameter("secao");
        String codfil = request.getParameter("codfil");
        String dataAtual = request.getParameter("dataAtual");
        String horaAtual = request.getParameter("horaAtual");

        if(null == dataAtual || dataAtual.equals("")) dataAtual = DataAtual.getDataAtual("SQL");
        if(null == horaAtual || horaAtual.equals("")) horaAtual = DataAtual.getDataAtual("HORA");
        
        String retorno = "<?xml version=\"1.0\"?>";

        try {
            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codPessoa,  param, logerro);
            tStart = System.currentTimeMillis();
            Persistencia persistencia = pool.getConexao(param);
            if (ValidaUsuarioPonto(codPessoa, senha, persistencia)) {
                InspecoesItensDao inspecoesItensDao = new InspecoesItensDao();
                List<InspecoesItens> inspecoesItens = inspecoesItensDao.getItensInspecao(codigo, persistencia);
                retorno += Xmls.tag("qtdItensInspecao", inspecoesItens.size());
                StringBuilder aux, auxLista;
                for(InspecoesItens item : inspecoesItens){
                    aux = new StringBuilder();
                    aux.append(Xmls.tag("codigo", item.getCodigo()))
                            .append(Xmls.tag("sequencia", item.getSequencia()))
                            .append(Xmls.tag("pergunta", item.getPergunta()))
                            .append(Xmls.tag("tiporesp", item.getTipoResp()))
                            .append(Xmls.tag("obrigatorio", item.getObrigatorio()))
                            .append(Xmls.tag("foto", item.getFoto()))
                            .append(Xmls.tag("video", item.getVideo()))
                            .append(Xmls.tag("nlista", item.getItensLista().size()));       
                    for(InspecoesItensLista itemLista : item.getItensLista()){
                        auxLista = new StringBuilder();
                        auxLista.append(Xmls.tag("codigo", itemLista.getCodigo()));
                        auxLista.append(Xmls.tag("sequencia", itemLista.getSequencia()));
                        auxLista.append(Xmls.tag("ordem", itemLista.getOrdem()));
                        auxLista.append(Xmls.tag("item", itemLista.getItem()));
                        auxLista.append(Xmls.tag("descricao", itemLista.getDescricao()));
                        aux.append(Xmls.tag("lista", auxLista.toString()));
                    }
                    retorno += Xmls.tag("itens", aux.toString());
                }
                
                DateTimeFormatter dd = DateTimeFormatter.ofPattern("yyyyMMdd");
                FuncionDao funcionDao = new FuncionDao();
                List<Funcion> funcionarios = funcionDao.listaFuncionariosRHPontoDet(codfil, secao,
                        LocalDate.parse(dataAtual, dd).minusDays(7).format(dd), dataAtual, persistencia);
                retorno += Xmls.tag("qtdFuncionarios", funcionarios.size());
               
                for(Funcion funcionario : funcionarios){
                    aux = new StringBuilder();
                    aux.append(Xmls.tag("matr", funcionario.getMatr().toBigInteger().toString()))
                            .append(Xmls.tag("nome", funcionario.getNome()));                    
                    retorno += Xmls.tag("funcionarios", aux.toString());
                }
                
                VeiculosDao veiculosDao = new VeiculosDao();
                List<Veiculos> veiculos = veiculosDao.getVeiculos(codfil, persistencia);
                StringBuilder vei, veis = new StringBuilder();
                retorno += Xmls.tag("qtdVeiculos", veiculos.size());
                
                for(Veiculos veiculo : veiculos){
                    veiculo = (Veiculos) FuncoesString.removeAcentoObjeto(veiculo);
                    vei = new StringBuilder();
                    vei.append(Xmls.tag("numero",veiculo.getNumero()));
                    vei.append(Xmls.tag("modelo",veiculo.getPlaca()+" "+veiculo.getObs()));
                    veis.append(Xmls.tag("veiculos", vei.toString()));
                }
                retorno += veis.toString();
                
            } else {
                retorno += "<resp>2</resp>";
            }
            out.print(retorno);
            persistencia.FechaConexao();
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0)+" segundos.\r\n", codPessoa, param, logerro); 
        }catch(Exception e){
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Falha ObterItensInspecao - " + e.getMessage(), codPessoa, param, logerro);
            out.print("<?xml version=\"1.0\"?><resp>0</resp>");
        }
    }
    
    
    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
