/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satmobew.rotaPessoa;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.PessoaTrajeto;
import SasDaos.PessoaTrajetoDao;
import static SasLibrary.ValidarUsuario.ValidaUsuario;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import br.com.sasw.satmobew.*;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "InserirPessoaTrajeto", urlPatterns = {"/rotaPessoa/InserirPessoaTrajeto"})
public class InserirPessoaTrajeto extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;
    
    @Override
    public void init(){
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

     /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
    
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/plain");
        PrintWriter resp = response.getWriter();
        logerro = new ArquivoLog();
        
        String senha = request.getParameter("senha");
        String param = request.getParameter("param");
        String codPessoa = request.getParameter("codpessoa");
        String codfil = request.getParameter("codfil");
        String operador = request.getParameter("operador");
        String dataAtual = request.getParameter("dataAtual");
        String horaAtual = request.getParameter("horaAtual");
        String idioma = request.getParameter("idioma");

        if(null == dataAtual || dataAtual.equals("")) dataAtual = DataAtual.getDataAtual("SQL");
        if(null == horaAtual || horaAtual.equals("")) horaAtual = DataAtual.getDataAtual("HORA");
        
        String retorno = "<?xml version=\"1.0\"?>";

        try {
            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codPessoa,  param, logerro);
            tStart = System.currentTimeMillis();
            Persistencia persistencia = pool.getConexao(param);
            if (ValidaUsuario(codPessoa, senha, persistencia)) {
                String ordem        = request.getParameter("ordem");
                String tipo         = request.getParameter("tipo");
                String hrCheg       = request.getParameter("hrCheg");
                String hrSaida      = request.getParameter("hrSaida");
                String tempo        = request.getParameter("tempo");
                String codContato   = request.getParameter("codContato");
                String codCli       = request.getParameter("codCli");
                String secao        = request.getParameter("secao");
                
                PessoaTrajeto pessoaTrajeto = new PessoaTrajeto();
                pessoaTrajeto.setCodPessoa(codPessoa);
                pessoaTrajeto.setDtCompet(dataAtual);
                pessoaTrajeto.setOrdem(ordem);
                pessoaTrajeto.setTipo(tipo);
                pessoaTrajeto.setHrCheg(hrCheg);
                pessoaTrajeto.setHrSaida(hrSaida);
                pessoaTrajeto.setTempo(tempo);
                pessoaTrajeto.setCodContato(codContato);
                pessoaTrajeto.setCodCli(codCli);
                pessoaTrajeto.setSecao(secao);
                pessoaTrajeto.setCodFil(codfil);
                pessoaTrajeto.setOperador(FuncoesString.RecortaAteEspaço(operador,0, 10));
                pessoaTrajeto.setDt_Alter(dataAtual);
                pessoaTrajeto.setHr_Alter(horaAtual);
                
                PessoaTrajetoDao pessoaTrajetoDao = new PessoaTrajetoDao();
                if(ordem.equals("0")){
                    String novaOrdem = pessoaTrajetoDao.inserirTrajeto(pessoaTrajeto, persistencia);
                    if(novaOrdem == null) retorno += "<resp>0</resp>";
                    else retorno += "<resp>1</resp><ordem>"+novaOrdem+"</ordem>";
                } else {
                    pessoaTrajetoDao.finalizaTrajeto(pessoaTrajeto, persistencia);
                    retorno += "<resp>1</resp><ordem>0</ordem>";
                }
            } else {
                retorno += "<resp>2</resp>";
            }
            resp.print(retorno);
            persistencia.FechaConexao();
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0)+" segundos.\r\n", codPessoa, param, logerro); 
        }catch(Exception e){
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Falha InserirPessoaTrajeto - " + e.getMessage(), codPessoa, param, logerro);
            resp.print("<?xml version=\"1.0\"?><resp>0</resp>");
        }
    }
    
    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
