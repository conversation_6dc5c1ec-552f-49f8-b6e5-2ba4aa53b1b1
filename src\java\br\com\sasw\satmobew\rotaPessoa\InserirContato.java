/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satmobew.rotaPessoa;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Contatos;
import SasDaos.ContatosDao;
import static SasLibrary.ValidarUsuario.ValidaUsuario;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import br.com.sasw.satmobew.*;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "InserirContato", urlPatterns = {"/rotaPessoa/InserirContato"})
public class InserirContato extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;
    
    @Override
    public void init(){
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

     /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
    
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/plain");
        PrintWriter resp = response.getWriter();
        logerro = new ArquivoLog();
        
        String senha = request.getParameter("senha");
        String param = request.getParameter("param");
        String codPessoa = request.getParameter("codpessoa");
        String codfil = request.getParameter("codfil");
        String operador = request.getParameter("operador");
        String dataAtual = request.getParameter("dataAtual");
        String horaAtual = request.getParameter("horaAtual");
        String idioma = request.getParameter("idioma");

        if(null == dataAtual || dataAtual.equals("")) dataAtual = DataAtual.getDataAtual("SQL");
        if(null == horaAtual || horaAtual.equals("")) horaAtual = DataAtual.getDataAtual("HORA");
        
        String retorno = "<?xml version=\"1.0\"?>";

        try {
            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codPessoa,  param, logerro);
            tStart = System.currentTimeMillis();
            Persistencia persistencia = pool.getConexao(param);
            if (ValidaUsuario(codPessoa, senha, persistencia)) {
                String latitude     = request.getParameter("latitude");
                String longitude    = request.getParameter("longitude");
                String nome         = request.getParameter("nome");
                String endereco     = request.getParameter("endereco");
                String bairro       = request.getParameter("bairro");
                String uf           = request.getParameter("uf");
                String cidade       = request.getParameter("cidade");
                String cep          = request.getParameter("cep");
                String fone1        = request.getParameter("fone1");
                
                Contatos contato = new Contatos();
                contato.setCodFil(new BigDecimal(codfil));
                contato.setNome(FuncoesString.RecortaString(nome, 0, 60));
                contato.setFantasia(FuncoesString.RecortaString(nome, 0, 40));
                contato.setEndereco(FuncoesString.RecortaString(endereco, 0, 60));
                contato.setBairro(FuncoesString.RecortaString(bairro, 0, 40));
                contato.setUF(FuncoesString.RecortaString(uf, 0, 2));
                contato.setCidade(FuncoesString.RecortaString(cidade, 0, 40));
                contato.setCEP(cep);
                contato.setFone1(fone1);
                contato.setSituacao(BigDecimal.ONE);
                contato.setDtSituacao(dataAtual);
                contato.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
                contato.setDt_Alter(dataAtual);
                contato.setHr_Alter(horaAtual);
                contato.setOperIncl(FuncoesString.RecortaAteEspaço(operador, 0, 10));
                contato.setLatitude(FuncoesString.RecortaString(latitude, 0, 20));
                contato.setLongitude(FuncoesString.RecortaString(longitude, 0, 20));
                
                ContatosDao contatosDao = new ContatosDao();
                String codigo = contatosDao.cadastrar((Contatos) FuncoesString.removeAcentoObjetoMaiusculo(contato), persistencia);
                retorno += "<resp>1</resp><codigo>"+codigo+"</codigo>";
            } else {
                retorno += "<resp>2</resp>";
            }
            resp.print(retorno);
            persistencia.FechaConexao();
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0)+" segundos.\r\n", codPessoa, param, logerro); 
        }catch(Exception e){
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Falha InserirContato - " + e.getMessage(), codPessoa, param, logerro);
            resp.print("<?xml version=\"1.0\"?><resp>0</resp>");
        }
    }
    
    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
