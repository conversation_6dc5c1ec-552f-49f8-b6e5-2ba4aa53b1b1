/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PstDepenRondaDao {

    public String getHorarios(String Codigo, String Secao, String CodFil, Persistencia persistencia) {
        StringBuilder retorno = new StringBuilder();
        try {
            String sql = " select hora "
                    + " FROM PstDepenRonda "
                    + " WHERE secao = ? and codigo = ? and codfil = ? and tipo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Secao);
            consulta.setString(Codigo);
            consulta.setString(CodFil);
            consulta.setString("SegSex");
            consulta.select();
            List<Integer> horas = new ArrayList<>();
            while (consulta.Proximo()) {
                horas.add(consulta.getInt("hora"));
            }
            StringBuilder horasBinario = new StringBuilder();
            for (int i = 0; i <= 23; i++) {
                horasBinario.append(horas.contains(i) ? "1" : "0");
            }
            consulta.Close();
            retorno.append(new BigInteger(horasBinario.toString(), 2).toString()).append(";");
        } catch (Exception e) {
            retorno.append("0").append(";");
        }
        try {
            String sql = " select hora "
                    + " FROM PstDepenRonda "
                    + " WHERE secao = ? and codigo = ? and codfil = ? and tipo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Secao);
            consulta.setString(Codigo);
            consulta.setString(CodFil);
            consulta.setString("Sab");
            consulta.select();
            List<Integer> horas = new ArrayList<>();
            while (consulta.Proximo()) {
                horas.add(consulta.getInt("hora"));
            }
            StringBuilder horasBinario = new StringBuilder();
            for (int i = 0; i <= 23; i++) {
                horasBinario.append(horas.contains(i) ? "1" : "0");
            }
            consulta.Close();
            retorno.append(new BigInteger(horasBinario.toString(), 2).toString()).append(";");
        } catch (Exception e) {
            retorno.append("0").append(";");
        }
        try {
            String sql = " select hora "
                    + " FROM PstDepenRonda "
                    + " WHERE secao = ? and codigo = ? and codfil = ? and tipo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Secao);
            consulta.setString(Codigo);
            consulta.setString(CodFil);
            consulta.setString("Dom");
            consulta.select();
            List<Integer> horas = new ArrayList<>();
            while (consulta.Proximo()) {
                horas.add(consulta.getInt("hora"));
            }
            StringBuilder horasBinario = new StringBuilder();
            for (int i = 0; i <= 23; i++) {
                horasBinario.append(horas.contains(i) ? "1" : "0");
            }
            consulta.Close();
            retorno.append(new BigInteger(horasBinario.toString(), 2).toString()).append(";");
        } catch (Exception e) {
            retorno.append("0").append(";");
        }
        return retorno.toString();
    }

    /**
     * Insere um horário de ronda para determinada depêndencia do posto
     *
     * @param Codigo
     * @param Secao
     * @param CodFil
     * @param Tipo
     * @param Hora
     * @param Operador
     * @param Dt_Alter
     * @param Hr_Alter
     * @param persistencia
     * @throws Exception
     */
    public void inserePstDepenRonda(String Codigo, String Secao, String CodFil, String Tipo, int Hora, String Operador,
            String Dt_Alter, String Hr_Alter, Persistencia persistencia) throws Exception {
        try {
            String sql = " insert into PstDepenRonda (Secao, CodFil, Codigo, Tipo, Hora, Operador, Dt_Alter, Hr_Alter)"
                    + " values (?,?,?,?,?,?,?,?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Secao);
            consulta.setString(CodFil);
            consulta.setString(Codigo);
            consulta.setString(Tipo);
            consulta.setInt(Hora);
            consulta.setString(Operador);
            consulta.setString(Dt_Alter);
            consulta.setString(Hr_Alter);
            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("PstDepenRondaDao.inserePstDepenRonda - " + e.getMessage() + "\r\n"
                    + " insert into PstDepenRonda (Secao, CodFil, Codigo, Tipo, Hora, Operador, Dt_Alter, Hr_Alter)"
                    + " values (" + Secao + "," + CodFil + "," + Codigo + "," + Tipo + "," + Hora + "," + Operador + "," + Dt_Alter + "," + Hr_Alter + ")");
        }
    }

    /**
     * Verifica a existência de uma entrada na tabela PstDepenRonda
     *
     * @param Secao
     * @param Codigo
     * @param CodFil
     * @param Tipo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean existePstDepenRonda(String Secao, String Codigo, String CodFil, String Tipo, int hora, Persistencia persistencia) throws Exception {
        boolean retorno = false;
        try {
            String sql = " select * "
                    + " FROM PstDepenRonda "
                    + " WHERE secao = ? and codigo = ? and codfil = ? and tipo = ? and hora = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Secao);
            consulta.setString(Codigo);
            consulta.setString(CodFil);
            consulta.setString(Tipo);
            consulta.setInt(hora);
            consulta.select();
            while (consulta.Proximo()) {
                retorno = true;
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstDepenRondaDao.existePstDepenRonda - " + e.getMessage() + "\r\n"
                    + " select * "
                    + " FROM PstDepenRonda "
                    + " WHERE secao = " + Secao + " and codigo = " + Codigo + " and codfil = " + CodFil + " and tipo = " + Tipo + " and hora = " + hora);
        }
    }

    /**
     * Apaga entrada de horário na tabela PstDepenRonda e retorna o comando para
     * SASLog
     *
     * @param Codigo
     * @param Secao
     * @param CodFil
     * @param Tipo
     * @param Hora
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String deletePstDepenRonda(String Codigo, String Secao, String CodFil, String Tipo, int Hora, Persistencia persistencia) throws Exception {
        try {
            String sql = " delete from PstDepenRonda where secao = ? and codfil = ? and codigo = ? and tipo = ? and hora = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Secao);
            consulta.setString(CodFil);
            consulta.setString(Codigo);
            consulta.setString(Tipo);
            consulta.setInt(Hora);
            consulta.delete();
            consulta.close();
            return " delete from PstDepenRonda where secao = " + Secao + " and codfil = " + CodFil + " and codigo = " + Codigo + ""
                    + " and tipo = " + Tipo + " and hora = " + Hora;
        } catch (Exception e) {
            throw new Exception("PstDepenRondaDao.deletePstDepenRonda - " + e.getMessage() + "\r\n"
                    + " delete from PstDepenRonda where secao = " + Secao + " and codfil = " + CodFil + " and codigo = " + Codigo + ""
                    + " and tipo = " + Tipo + " and hora = " + Hora);
        }
    }
}
