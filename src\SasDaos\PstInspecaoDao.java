/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.ColumnModel;
import SasBeans.Inspecoes;
import SasBeans.InspecoesItens;
import SasBeans.LinhaModel;
import SasBeans.PstInspecao;
import SasBeansCompostas.LogsSatMobEW;
import SasBeansCompostas.PstInspecaoRelatorio;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class PstInspecaoDao {

    /**
     * Busca todas as inspeções realizadas em determinada data
     *
     * @param secao
     * @param codFil
     * @param data
     * @param matr
     * @param codInspecao
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PstInspecao> listaInspecoesItens(String codigo, Persistencia persistencia) throws Exception {
        List<PstInspecao> retorno = new ArrayList<>();
        String sql = "SELECT\n"
                + "    PstInspecao.Codigo, PstInspecao.Secao, PstInspecao.CodFil, CONVERT(VarChar, PstInspecao.Data, 112) Data, \n"
                + "    PstInspecao.Sequencia, PstInspecao.Pergunta,\n"
                + "    CASE WHEN InspecoesItensLista.Descricao IS NOT NULL THEN InspecoesItensLista.Descricao ELSE PstInspecao.Resposta END Resposta, \n"
                + "    PstInspecao.CaminhoImagem, PstInspecao.CaminhoVideo, \n";
        if (persistencia.getEmpresa().contains("SATSASEX")
                || persistencia.getEmpresa().contains("SATPISCINAFACIL")) {
            sql += " PstInspecao.CaminhoAudio, \n";
        } else {
            sql += " '' CaminhoAudio, \n";
        }
        sql += "    PstInspecao.Matr, PstInspecao.Veiculo, PstInspecao.CodInspecao, PstInspecao.Latitude, PstInspecao.Longitude, \n"
                + "    PstInspecao.CodOperador, PstInspecao.Operador, PstInspecao.Dt_Alter, PstInspecao.Hr_Alter, PstServ.Local,\n"
                + "    Pessoa.Nome Inspetor\n"
                + "FROM\n"
                + "    PstInspecao\n"
                + "LEFT JOIN \n"
                + "    InspecoesItens ON InspecoesItens.Sequencia = PstInspecao.Sequencia\n"
                + "                   AND InspecoesItens.Pergunta = PstInspecao.Pergunta\n"
                + "LEFT JOIN \n"
                + "    InspecoesItensLista ON InspecoesItensLista.Codigo = InspecoesItens.Codigo\n"
                + "                        AND InspecoesItensLista.Sequencia = InspecoesItens.Sequencia\n"
                + "                        AND InspecoesItensLista.Item = PstInspecao.Resposta\n"
                + "LEFT JOIN \n"
                + "    PstServ ON PstServ.Secao = PstInspecao.Secao\n"
                + "            AND PstServ.Codfil = PstInspecao.CodFil \n"
                + "LEFT JOIN \n"
                + "    Pessoa ON Pessoa.Codigo = PstInspecao.CodOperador\n"
                + "WHERE \n"
                + "    PstInspecao.Codigo = ?\n"
                + "GROUP BY \n"
                + "    PstInspecao.Codigo, PstInspecao.Secao, PstInspecao.CodFil, PstInspecao.Data, PstInspecao.Sequencia, \n"
                + "    PstInspecao.Pergunta, PstInspecao.Resposta, InspecoesItensLista.Descricao, PstInspecao.CaminhoImagem,\n";
        if (persistencia.getEmpresa().contains("SATSASEX")
                || persistencia.getEmpresa().contains("SATPISCINAFACIL")) {
            sql += " PstInspecao.CaminhoAudio, \n";
        }
        sql += "    PstInspecao.CaminhoVideo, PstInspecao.Matr, PstInspecao.Veiculo, PstInspecao.CodInspecao, \n"
                + "    PstInspecao.Latitude, PstInspecao.Longitude, PstInspecao.CodOperador, PstInspecao.Operador, \n"
                + "    PstInspecao.Dt_Alter, PstInspecao.Hr_Alter, PstServ.Local, Pessoa.Nome";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();
            PstInspecao pstInspecao;
            while (consulta.Proximo()) {
                pstInspecao = new PstInspecao();
                pstInspecao.setCodInspecao(consulta.getString("CodInspecao"));
                pstInspecao.setSecao(consulta.getString("Secao"));
                pstInspecao.setCodfil(consulta.getString("Codfil"));
                pstInspecao.setData(consulta.getString("Data"));
                pstInspecao.setSequencia(consulta.getString("Sequencia"));
                pstInspecao.setPergunta(consulta.getString("Pergunta"));
                pstInspecao.setResposta(consulta.getString("Resposta"));
                pstInspecao.setCaminhoImagem(consulta.getString("CaminhoImagem"));
                pstInspecao.setCaminhoVideo(consulta.getString("CaminhoVideo"));
                pstInspecao.setCaminhoAudio(consulta.getString("CaminhoAudio"));
                pstInspecao.setMatr(consulta.getString("Matr"));
                pstInspecao.setVeiculo(consulta.getString("Veiculo"));
                pstInspecao.setOperador(consulta.getString("Operador"));
                pstInspecao.setDt_Alter(consulta.getString("Dt_Alter"));
                pstInspecao.setHr_Alter(consulta.getString("Hr_Alter"));
                pstInspecao.setLocal(consulta.getString("Local"));
                pstInspecao.setInspetor(consulta.getString("Inspetor"));
                pstInspecao.setCodigo(consulta.getString("Codigo"));
                retorno.add(pstInspecao);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstInspecaoDao.listaInspecoesItens - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public void inserirResumo(String codFil, String data, String codOperador, String latitude, String longitude, Persistencia persistencia) throws Exception {
        try {
            String sql = "Declare @codfil int;\n"
                    + "Declare @codPessoa int;\n"
                    + "Declare @data date;\n"
                    + "Declare @codRelatorio int;\n"
                    + "Declare @codTotalizador int;\n"
                    + "\n"
                    + "Declare @nomePessoa Varchar(100);\n"
                    + "\n"
                    + "Declare @inspecaoRS int;\n"
                    + "Declare @inspecaoC int;\n"
                    + "Declare @inspecaoTB int;\n"
                    + "Declare @inspecaoO int;\n"
                    + "Declare @inspecaoPE int;\n"
                    + "Declare @inspecaoTotal int;\n"
                    + "\n"
                    + "Declare @imoveisInspecionados int;\n"
                    + "Declare @imoveisRecusados int;\n"
                    + "Declare @imoveisFechados int;\n"
                    + "\n"
                    + "Declare @qtdAmostras int;\n"
                    + "\n"
                    + "Declare @depositosInspecionados int;\n"
                    + "Declare @depositosTratados int;\n"
                    + "Declare @depositosEliminados int;\n"
                    + "\n"
                    + "Declare @depPotenciaA1 int;\n"
                    + "Declare @depPotenciaA2 int;\n"
                    + "Declare @depPotenciaB int;\n"
                    + "Declare @depPotenciaC int;\n"
                    + "Declare @depPotenciaD1 int;\n"
                    + "Declare @depPotenciaD2 int;\n"
                    + "Declare @depPotenciaE int;\n"
                    + "Declare @depPotenciaTotal int;\n"
                    + "Declare @lat Varchar(20);\n"
                    + "Declare @long Varchar(20);\n"
                    + "\n"
                    + "Declare @existeResumo int;\n"
                    + "Declare @codInspecao int;\n"
                    + "\n"
                    + "Declare @secao Varchar(20);\n"
                    + "\n"
                    + "Select @codRelatorio = Codigo from Inspecoes where Descricao = 'BOLETIM DE TRABALHO DE CAMPO';\n"
                    + "Select @codTotalizador = Codigo from Inspecoes where Descricao = 'RESUMO DO TRABALHO DE CAMPO';\n"
                    + "\n"
                    + "--Definindo parametros---------------------------\n"
                    + "set @codfil = ?;\n"
                    + "set @data = ?;\n"
                    + "set @codPessoa = ?;\n"
                    + "set @lat = ?;\n"
                    + "set @long = ?;\n"
                    + "-------------------------------------------------\n"
                    + "Select @secao = Funcion.Secao, @nomePessoa = Pessoa.Nome\n"
                    + "from Pessoa (Nolock)\n"
                    + "Left join Funcion (Nolock)  on Funcion.Matr = Pessoa.Matr\n"
                    + "Where pessoa.Codigo = @codPessoa;\n"
                    + "\n"
                    + "Select @inspecaoRS = count(*)\n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and pergunta = '14/41 - TIPO IMÓVEL (RS - RESIDÊNCIA,  C - COMÉRCIO, TB - TERRENO BALDIO, O - OUTROS)'\n"
                    + "  and Resposta = 'RS';\n"
                    + "\n"
                    + "Select @inspecaoC = count(*)\n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and pergunta = '14/41 - TIPO IMÓVEL (RS - RESIDÊNCIA,  C - COMÉRCIO, TB - TERRENO BALDIO, O - OUTROS)'\n"
                    + "  and Resposta = 'C';\n"
                    + "\n"
                    + "Select @inspecaoTB = count(*)\n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and pergunta = '14/41 - TIPO IMÓVEL (RS - RESIDÊNCIA,  C - COMÉRCIO, TB - TERRENO BALDIO, O - OUTROS)'\n"
                    + "  and Resposta = 'TB';\n"
                    + "\n"
                    + "Select @inspecaoO = count(*)\n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and pergunta = '14/41 - TIPO IMÓVEL (RS - RESIDÊNCIA,  C - COMÉRCIO, TB - TERRENO BALDIO, O - OUTROS)'\n"
                    + "  and Resposta = 'O';\n"
                    + "\n"
                    + "Select @inspecaoPE = count(*)\n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and pergunta = '14/41 - TIPO IMÓVEL (RS - RESIDÊNCIA,  C - COMÉRCIO, TB - TERRENO BALDIO, O - OUTROS)'\n"
                    + "  and Resposta = 'PE';\n"
                    + "\n"
                    + "set @inspecaoTotal = isnull(@inspecaoRS,0)+ isnull(@inspecaoC,0) + isnull(@inspecaoTB,0) + isnull(@inspecaoO,0);\n"
                    + "\n"
                    + "\n"
                    + "Select @imoveisRecusados = count(*)\n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and pergunta = '13/41 - STATUS IMÓVEL (PEND. REC. FECH.)'\n"
                    + "  and Resposta = 'REC';\n"
                    + "\n"
                    + "Select @imoveisFechados = count(*)\n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and pergunta = '13/41 - STATUS IMÓVEL (PEND. REC. FECH.)'\n"
                    + "  and Resposta = 'FECH';\n"
                    + "\n"
                    + "Select @imoveisInspecionados = count(*)\n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and pergunta = '13/41 - STATUS IMÓVEL (PEND. REC. FECH.)'\n"
                    + "  and Resposta <> 'REC'\n"
                    + "  and Resposta <> 'FECH';\n"
                    + "\n"
                    + "Select @qtdAmostras = sum(Convert(bigint,Resposta))\n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and pergunta = '29/41 - N° DE AMOSTRAS COLETADAS INIC/FINAL';\n"
                    + "\n"
                    + "Select @depositosInspecionados = sum(Convert(bigint,Resposta))\n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and pergunta in( \n"
                    + "  '15/41 - DEPÓSITOS INSPECIONADO A1 (CAIXA DAGUA LIGADA À REDE - DEPÓSITOS ELEVADOS)',\n"
                    + "  '17/41 - DEPÓSITOS INSPECIONADO - A2 (DEPÓSITOS AO NÍVEL DO SOLO: CONSUMO DOMÉSTICO - BARRIL, TINA, TONEL, TAMBOR, DEPÓSITO DE BARRO, TANQUE, POÇO, CISTERNA, CACIMBA)',\n"
                    + "  '19/41 - DEPÓSITOS INSPECIONADO B (VASOS/FRASCOS COM ÁGUA, VASOS DE PLANTAS, PRATOS, PINGADEIRAS, RECIPIENTES DE DEGELO, BEBEDOUROS EM GERAL, FONTES DE ORNAMENTOS, MATERIAIS/DEPÓSITOS CONSTR)',\n"
                    + "  '21/41 - DEPÓSITOS INSPECIONADO C (TANQUES/DEPÓSITOS EM OBRAS, BORRACHARIAS E HORTAS, CALHAS E LAJES EM DESNÍVEIS, SANITÁRIOS EM DESUSO, PISCINAS NÃO TRATADAS, FONTES ORNAMENTAIS,  FLOREIRAS',\n"
                    + "  '23/41 - DEPÓSITOS INSPECIONADO D1 (PNEUS E OUTROS MATERIAIS RODANTES, MANCHÕES, CÂMARAS)',\n"
                    + "  '25/41 - DEPÓSITOS INSPECIONADO D2 (LIXO, RECIPIENTES, PLÁSTICOS, GARRAFAS, LATAS, SUCATAS EM PÁTIOS, FERROS-VELHOS E RECICLADORAS, ENTULHO)',\n"
                    + "  '27/41 - DEPÓSITOS INSPECIONADO E (AXILAS DE FOLHAS, BROMÉLIAS, ETC, BURACOS EM ÁRVORES E EM ROCHAS, CASCAS, RESTOS DE ANIMAIS, CASCOS, CARAPAÇAS)')\n"
                    + "\n"
                    + "Select @depositosTratados = sum(Convert(bigint,Resposta))\n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and pergunta in( \n"
                    + "'16/41 - DEPÓSITOS TRATADO A1 (CAIXA DAGUA LIGADA À REDE - DEPÓSITOS ELEVADOS)',\n"
                    + "'18/41 - DEPÓSITOS TRATADO - A2 (DEPÓSITOS AO NÍVEL DO SOLO: CONSUMO DOMÉSTICO - BARRIL, TINA, TONEL, TAMBOR, DEPÓSITO DE BARRO, TANQUE, POÇO, CISTERNA, CACIMBA)',\n"
                    + "'20/41 - DEPÓSITOS TRATADO B (VASOS/FRASCOS COM ÁGUA, VASOS DE PLANTAS, PRATOS, PINGADEIRAS, RECIPIENTES DE DEGELO, BEBEDOUROS EM GERAL, FONTES DE ORNAMENTOS, MATERIAIS/DEPÓSITOS CONSTR)',\n"
                    + "'22/41 - DEPÓSITOS TRATADO C (TANQUES/DEPÓSITOS EM OBRAS, BORRACHARIAS E HORTAS, CALHAS E LAJES EM DESNÍVEIS, SANITÁRIOS EM DESUSO, PISCINAS NÃO TRATADAS, FONTES ORNAMENTAIS,  FLOREIRAS',\n"
                    + "'24/41 - DEPÓSITOS TRATADO D1 (PNEUS E OUTROS MATERIAIS RODANTES, MANCHÕES, CÂMARAS)',\n"
                    + "'26/41 - DEPÓSITOS TRATADO D2 (LIXO, RECIPIENTES, PLÁSTICOS, GARRAFAS, LATAS, SUCATAS EM PÁTIOS, FERROS-VELHOS E RECICLADORAS, ENTULHO)',\n"
                    + "'28/41 - DEPÓSITOS TRATADO E (AXILAS DE FOLHAS, BROMÉLIAS, ETC, BURACOS EM ÁRVORES E EM ROCHAS, CASCAS, RESTOS DE ANIMAIS, CASCOS, CARAPAÇAS)');\n"
                    + "\n"
                    + "Select @depositosEliminados = sum(Convert(bigint,Resposta))\n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and pergunta in('31/41 - DEPÓSITOS ELIMINADOS');\n"
                    + "\n"
                    + "Select @depPotenciaA1 = sum(Convert(bigint,Resposta))\n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and pergunta in( \n"
                    + "  '15/41 - DEPÓSITOS INSPECIONADO A1 (CAIXA DAGUA LIGADA À REDE - DEPÓSITOS ELEVADOS)');\n"
                    + "\n"
                    + "Select @depPotenciaA2 = sum(Convert(bigint,Resposta))\n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and pergunta in( \n"
                    + "  '17/41 - DEPÓSITOS INSPECIONADO - A2 (DEPÓSITOS AO NÍVEL DO SOLO: CONSUMO DOMÉSTICO - BARRIL, TINA, TONEL, TAMBOR, DEPÓSITO DE BARRO, TANQUE, POÇO, CISTERNA, CACIMBA)');\n"
                    + "\n"
                    + "\n"
                    + "Select @depPotenciaB = sum(Convert(bigint,Resposta))\n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and pergunta in( \n"
                    + "  '19/41 - DEPÓSITOS INSPECIONADO B (VASOS/FRASCOS COM ÁGUA, VASOS DE PLANTAS, PRATOS, PINGADEIRAS, RECIPIENTES DE DEGELO, BEBEDOUROS EM GERAL, FONTES DE ORNAMENTOS, MATERIAIS/DEPÓSITOS CONSTR)');\n"
                    + "\n"
                    + "Select @depPotenciaC = sum(Convert(bigint,Resposta))\n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and pergunta in( \n"
                    + "  '21/41 - DEPÓSITOS INSPECIONADO C (TANQUES/DEPÓSITOS EM OBRAS, BORRACHARIAS E HORTAS, CALHAS E LAJES EM DESNÍVEIS, SANITÁRIOS EM DESUSO, PISCINAS NÃO TRATADAS, FONTES ORNAMENTAIS,  FLOREIRAS');\n"
                    + "\n"
                    + "Select @depPotenciaD1 = sum(Convert(bigint,Resposta))\n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and pergunta in( \n"
                    + "  '23/41 - DEPÓSITOS INSPECIONADO D1 (PNEUS E OUTROS MATERIAIS RODANTES, MANCHÕES, CÂMARAS)');\n"
                    + "\n"
                    + "Select @depPotenciaD2 = sum(Convert(bigint,Resposta))\n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and pergunta in( \n"
                    + "  '25/41 - DEPÓSITOS INSPECIONADO D2 (LIXO, RECIPIENTES, PLÁSTICOS, GARRAFAS, LATAS, SUCATAS EM PÁTIOS, FERROS-VELHOS E RECICLADORAS, ENTULHO)');\n"
                    + "\n"
                    + "Select @depPotenciaE = sum(Convert(bigint,Resposta))\n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and pergunta in( \n"
                    + "  '27/41 - DEPÓSITOS INSPECIONADO E (AXILAS DE FOLHAS, BROMÉLIAS, ETC, BURACOS EM ÁRVORES E EM ROCHAS, CASCAS, RESTOS DE ANIMAIS, CASCOS, CARAPAÇAS)');\n"
                    + "\n"
                    + "set @depPotenciaTotal = isnull(@depPotenciaA1,0) + isnull(@depPotenciaA2,0)  + isnull(@depPotenciaB,0) + isnull(@depPotenciaC,0) + isnull(@depPotenciaD1,0) + isnull(@depPotenciaD2,0) + isnull(@depPotenciaE,0);\n"
                    + "\n"
                    + "\n"
                    + "--Verificando existencia resumo na data para operador\n"
                    + "Select @existeResumo = isnull(count(*),0) \n"
                    + "from PstInspecao(NoLock)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador;\n"
                    + "\n"
                    + "if(@existeResumo <= 0) begin\n"
                    + "	Select @codInspecao = isnull(max(Codigo),0)+1 from PstInspecao;\n"
                    + "	Insert into PstInspecao (Codigo, Secao, CodFil,Data,Sequencia,Pergunta,CodInspecao, CodOperador, Operador, Dt_Alter, Hr_Alter)	\n"
                    + "	Select @codInspecao, @secao,@codfil,@data,Sequencia,Pergunta,Codigo, @codPessoa, @codPessoa, Convert(Date,Getdate()), Substring(Convert(Varchar,GetDate(),108),1,5) \n"
                    + "	From InspecoesItens where Codigo = @codTotalizador;			\n"
                    + "end;\n"
                    + "\n"
                    + "Update PstInspecao set Latitude = @lat, Longitude = @long, Hr_Alter = Substring(Convert(Varchar,GetDate(),108),1,5),\n"
                    + "Matr = 0, Veiculo = 0\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador;\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @nomePessoa\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'RESPONSÁVEL';\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @inspecaoRS\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO RESIDÊNCIA';\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @inspecaoC\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO COMÉRCIO';\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @inspecaoTB\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO TB';\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @inspecaoPE\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO PE';\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @inspecaoO\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO OUTROS';\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @inspecaoTotal\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TOTAL';\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @imoveisInspecionados\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'NÚMERO DE IMÓVEIS INSPECIONADOS - TOTAL';\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @imoveisRecusados\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'NÚMERO DE IMÓVEIS RECUSADOS - TOTAL';\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @imoveisFechados\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'NÚMERO DE IMÓVEIS FECHADOS - TOTAL';\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @qtdAmostras\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'AMOSTRAS - TOTAL COLETADAS';\n"
                    + "\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @depositosInspecionados\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'NÚMERO DE DEPÓSITOS INSPECIONADOS - TOTAL';\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @depositosTratados\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'NÚMERO DE DEPÓSITOS TRATADOS - TOTAL';\n"
                    + "  \n"
                    + "Update PstInspecao set Resposta = isnull(@depositosEliminados,0)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'NÚMERO DE DEPÓSITOS ELIMINADOS - TOTAL';\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = Isnull(@depPotenciaA1,0)\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - A1';\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @depPotenciaA2\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - A2';\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @depPotenciaB\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - B';\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @depPotenciaC\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - C';\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @depPotenciaD1\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - D1';\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @depPotenciaD2\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - D2';\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @depPotenciaE\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - E';\n"
                    + "\n"
                    + "Update PstInspecao set Resposta = @depPotenciaTotal\n"
                    + "where data = @data \n"
                    + "  and CodOperador = @codPessoa \n"
                    + "  and Codfil = @codfil\n"
                    + "  and CodInspecao = @codTotalizador\n"
                    + "  and Pergunta = 'NÚMERO DE DEPÓSITOS POTENCIAIS - TOTAL';";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(data);
            consulta.setString(codOperador);
            consulta.setString(latitude);
            consulta.setString(longitude);
            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    /**
     * Obtém a próxima sequência disponível
     *
     * @param pstInspecao
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String obterSequencia(PstInspecao pstInspecao, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT ISNULL(MAX(sequencia),0)+1 AS sequencia "
                    + " FROM PstInspecao "
                    + " WHERE Secao = ? AND CodFil = ? AND Data = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pstInspecao.getSecao());
            consulta.setString(pstInspecao.getCodfil());
            consulta.setString(pstInspecao.getData());
            consulta.select();
            String sequencia = null;
            while (consulta.Proximo()) {
                sequencia = consulta.getString("sequencia");
            }
            consulta.Close();
            return sequencia;
        } catch (Exception e) {
            throw new Exception("PstInspecaoDao.obterSequencia - " + e.getMessage() + "\r\n"
                    + " SELECT ISNULL(MAX(sequencia),0)+1 AS sequencia "
                    + " FROM PstInspecao "
                    + " WHERE Secao = " + pstInspecao.getSecao() + " AND CodFil = " + pstInspecao.getCodfil() + " AND Data = " + pstInspecao.getData());
        }
    }

    /**
     *
     * @param sequencia
     * @param pstInspecao
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean inserirInspecao(String sequencia, PstInspecao pstInspecao, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO PstInspecao (Codigo, Secao, Codfil, Data, Sequencia, Pergunta, Resposta, CaminhoImagem, CaminhoVideo, Matr, Veiculo, \n"
                    + " Dt_Alter, Hr_Alter, Operador, CodInspecao, Latitude, Longitude, CodOperador) \n"
                    + " values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pstInspecao.getCodigo());
            consulta.setString(pstInspecao.getSecao());
            consulta.setString(pstInspecao.getCodfil());
            consulta.setString(pstInspecao.getData());
            consulta.setString(sequencia);
            consulta.setString(pstInspecao.getPergunta());
            consulta.setString(pstInspecao.getResposta());
            consulta.setString(pstInspecao.getCaminhoImagem());
            consulta.setString(pstInspecao.getCaminhoVideo());
            consulta.setString(pstInspecao.getMatr());
            consulta.setString(pstInspecao.getVeiculo());
            consulta.setString(pstInspecao.getDt_Alter());
            consulta.setString(pstInspecao.getHr_Alter());
            consulta.setString(pstInspecao.getOperador());
            consulta.setString(pstInspecao.getCodInspecao());
            consulta.setString(pstInspecao.getLatitude());
            consulta.setString(pstInspecao.getLongitude());
            consulta.setString(pstInspecao.getCodOperador());
            return consulta.insert() > 0;
        } catch (Exception e) {
            throw new Exception("PstInspecaoDao.inserirInspecao - " + e.getMessage() + "\r\n"
                    + " INSERT INTO PstInspecao (Secao, Codfil, Data, Sequencia, Pergunta, Resposta, CaminhoImagem, CaminhoVideo, Matr, Veiculo, \n"
                    + " Dt_Alter, Hr_Alter, Operador, CodInspecao, Latitude, Longitude) \n"
                    + " values (" + pstInspecao.getSecao() + "," + pstInspecao.getCodfil() + "," + pstInspecao.getData() + ","
                    + sequencia + "," + pstInspecao.getPergunta() + "," + pstInspecao.getResposta() + "," + pstInspecao.getCaminhoImagem() + ","
                    + pstInspecao.getCaminhoVideo() + "," + pstInspecao.getMatr() + "," + pstInspecao.getVeiculo() + "," + pstInspecao.getDt_Alter() + ","
                    + pstInspecao.getHr_Alter() + "," + pstInspecao.getOperador() + "," + pstInspecao.getCodInspecao() + "," + pstInspecao.getLatitude() + "," + pstInspecao.getLongitude() + ")");
        }
    }

    /**
     *
     * @param sequencia
     * @param pstInspecao
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String inserirInspecaoCodigo(String sequencia, PstInspecao pstInspecao, Persistencia persistencia) throws Exception {
        try {
            String sql = " DECLARE @Codigo Float\n"
                    + " SET @Codigo = (SELECT ISNULL(MAX(Codigo),0) + 1 Codigo FROM PstInspecao)\n"
                    + " INSERT INTO PstInspecao (Codigo, Secao, Codfil, Data, Sequencia, Pergunta, Resposta, CaminhoImagem, CaminhoVideo, Matr, Veiculo, \n"
                    + " Dt_Alter, Hr_Alter, Operador, CodInspecao, Latitude, Longitude, CodOperador) \n"
                    + " values (@Codigo, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) "
                    + " SELECT @Codigo Codigo ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pstInspecao.getSecao());
            consulta.setString(pstInspecao.getCodfil());
            consulta.setString(pstInspecao.getData());
            consulta.setString(sequencia);
            consulta.setString(pstInspecao.getPergunta());
            consulta.setString(pstInspecao.getResposta());
            consulta.setString(pstInspecao.getCaminhoImagem());
            consulta.setString(pstInspecao.getCaminhoVideo());
            consulta.setString(pstInspecao.getMatr());
            consulta.setString(pstInspecao.getVeiculo());
            consulta.setString(pstInspecao.getDt_Alter());
            consulta.setString(pstInspecao.getHr_Alter());
            consulta.setString(pstInspecao.getOperador());
            consulta.setString(pstInspecao.getCodInspecao());
            consulta.setString(pstInspecao.getLatitude());
            consulta.setString(pstInspecao.getLongitude());
            consulta.setString(pstInspecao.getCodOperador());
            consulta.select();
            String codigo = null;
            if (consulta.Proximo()) {
                codigo = consulta.getString("Codigo");
            }
            consulta.close();
            return codigo;
        } catch (Exception e) {
            throw new Exception("PstInspecaoDao.inserirInspecao - " + e.getMessage() + "\r\n"
                    + " INSERT INTO PstInspecao (Secao, Codfil, Data, Sequencia, Pergunta, Resposta, CaminhoImagem, CaminhoVideo, Matr, Veiculo, \n"
                    + " Dt_Alter, Hr_Alter, Operador, CodInspecao, Latitude, Longitude) \n"
                    + " values (" + pstInspecao.getSecao() + "," + pstInspecao.getCodfil() + "," + pstInspecao.getData() + ","
                    + sequencia + "," + pstInspecao.getPergunta() + "," + pstInspecao.getResposta() + "," + pstInspecao.getCaminhoImagem() + ","
                    + pstInspecao.getCaminhoVideo() + "," + pstInspecao.getMatr() + "," + pstInspecao.getVeiculo() + "," + pstInspecao.getDt_Alter() + ","
                    + pstInspecao.getHr_Alter() + "," + pstInspecao.getOperador() + "," + pstInspecao.getCodInspecao() + "," + pstInspecao.getLatitude() + "," + pstInspecao.getLongitude() + ")");
        }
    }

    /**
     * Atualiza os caminhos de imagem e video de uma inspeção
     *
     * @param sequencia
     * @param pstInspecao
     * @param persistencia
     * @throws Exception
     */
    public void atualizarCaminhos(String sequencia, PstInspecao pstInspecao, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE PstInspecao SET CaminhoImagem = ?, CaminhoVideo = ? "
                    + " WHERE Sequencia = ? AND Secao = ? AND CodFil = ? AND Data = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pstInspecao.getCaminhoImagem());
            consulta.setString(pstInspecao.getCaminhoVideo());
            consulta.setString(sequencia);
            consulta.setString(pstInspecao.getSecao());
            consulta.setString(pstInspecao.getCodfil());
            consulta.setString(pstInspecao.getData());
            consulta.update();
        } catch (Exception e) {
            throw new Exception("PstInspecaoDao.atualizarCaminhos - " + e.getMessage() + "\r\n"
                    + " UPDATE PstInspecao SET CaminhoImagem = " + pstInspecao.getCaminhoImagem() + ", CaminhoVideo = " + pstInspecao.getCaminhoVideo()
                    + " WHERE Sequencia = " + sequencia + " AND Secao = " + pstInspecao.getSecao() + " AND "
                    + " CodFil = " + pstInspecao.getCodfil() + " AND Data = " + pstInspecao.getData());
        }
    }

    /**
     * Atualiza os caminhos de imagem e video de uma inspeção
     *
     * @param sequencia
     * @param pstInspecao
     * @param persistencia
     * @throws Exception
     */
    public void atualizarCaminhoAudio(String sequencia, PstInspecao pstInspecao, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE PstInspecao SET CaminhoAudio = ? "
                    + " WHERE Sequencia = ? AND Secao = ? AND CodFil = ? AND Data = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pstInspecao.getCaminhoAudio());
            consulta.setString(sequencia);
            consulta.setString(pstInspecao.getSecao());
            consulta.setString(pstInspecao.getCodfil());
            consulta.setString(pstInspecao.getData());
            consulta.update();
        } catch (Exception e) {
            throw new Exception("PstInspecaoDao.atualizarCaminhoAudio - " + e.getMessage() + "\r\n"
                    + " UPDATE PstInspecao SET CaminhoAudio = " + pstInspecao.getCaminhoAudio()
                    + " WHERE Sequencia = " + sequencia + " AND Secao = " + pstInspecao.getSecao() + " AND "
                    + " CodFil = " + pstInspecao.getCodfil() + " AND Data = " + pstInspecao.getData());
        }
    }

    /**
     * Busca todas as inspeções realizadas em determinada data
     *
     * @param data
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PstInspecao> listaInspecoesAgrupadas(String data, String codFil, Persistencia persistencia) throws Exception {
        try {
            List<PstInspecao> retorno = new ArrayList<>();
            String sql = " SELECT max(PstInspecao.Hr_Alter) Hora, Inspecoes.Descricao + (select TOP 1 CASE WHEN Inspecoes.Descricao = 'Entrada e Saída de Viaturas' THEN ' - ' + X.Resposta ELSE '' END from PstInspecao AS X where X.data = pstinspecao.data order by sequencia) Descricao, PstInspecao.Secao, PstInspecao.CodFil, "
                    + " PstInspecao.Data, PstInspecao.Matr, PstServ.Local, PstInspecao.CodInspecao, Funcion.Nome "
                    + " FROM PstInspecao "
                    + " LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao "
                    + "                 AND PstServ.Codfil = PstInspecao.CodFil "
                    + " LEFT JOIN Funcion ON Funcion.Matr = PstInspecao.Matr "
                    + "                AND Funcion.Codfil = PstInspecao.Codfil "
                    + " LEFT JOIN Inspecoes ON Inspecoes.Codigo = PstInspecao.CodInspecao "
                    + " WHERE PstInspecao.Data = ? AND PstInspecao.CodFil = ? "
                    + " GROUP BY Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, PstInspecao.Data, PstInspecao.Matr, "
                    + " PstInspecao.CodInspecao, PstServ.Local, Funcion.Nome "
                    + " ORDER BY Hora DESC ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codFil);
            consulta.select();
            PstInspecao pstInspecao;
            while (consulta.Proximo()) {
                pstInspecao = new PstInspecao();
                pstInspecao.setSecao(consulta.getString("Secao"));
                pstInspecao.setCodfil(consulta.getString("Codfil"));
                pstInspecao.setData(consulta.getString("Data"));
                pstInspecao.setPergunta(consulta.getString("Descricao"));
                pstInspecao.setResposta(consulta.getString("Nome"));
                pstInspecao.setCodInspecao(consulta.getString("CodInspecao"));
                pstInspecao.setMatr(consulta.getString("Matr"));
                pstInspecao.setLocal(consulta.getString("Local"));
                pstInspecao.setHr_Alter(consulta.getString("Hora"));
                retorno.add(pstInspecao);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstInspecaoDao.listaTodosPontos - " + e.getMessage() + "\r\n"
                    + " SELECT * "
                    + " FROM PstInspecao "
                    + " WHERE Data = " + data
                    + " ORDER BY Dt_Alter DESC, Hr_Alter DESC");
        }
    }

    public List<PstInspecao> listaInspecoesAgrupadas(String data, Persistencia persistencia) throws Exception {
        try {
            List<PstInspecao> retorno = new ArrayList<>();
            String sql = " SELECT max(PstInspecao.Hr_Alter) Hora, Inspecoes.Descricao + (select TOP 1 CASE WHEN Inspecoes.Descricao = 'Entrada e Saída de Viaturas' THEN ' - ' + X.Resposta ELSE '' END from PstInspecao AS X where X.data = pstinspecao.data order by sequencia) Descricao, PstInspecao.Secao, PstInspecao.CodFil, "
                    + " PstInspecao.Data, PstInspecao.Matr, PstServ.Local, PstInspecao.CodInspecao, Funcion.Nome "
                    + " FROM PstInspecao "
                    + " LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao "
                    + "                 AND PstServ.Codfil = PstInspecao.CodFil "
                    + " LEFT JOIN Funcion ON Funcion.Matr = PstInspecao.Matr "
                    + "                AND Funcion.Codfil = PstInspecao.Codfil "
                    + " LEFT JOIN Inspecoes ON Inspecoes.Codigo = PstInspecao.CodInspecao "
                    + " WHERE PstInspecao.Data = ? "
                    + " GROUP BY Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, PstInspecao.Data, PstInspecao.Matr, "
                    + " PstInspecao.CodInspecao, PstServ.Local, Funcion.Nome "
                    + " ORDER BY Hora DESC ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.select();
            PstInspecao pstInspecao;
            while (consulta.Proximo()) {
                pstInspecao = new PstInspecao();
                pstInspecao.setSecao(consulta.getString("Secao"));
                pstInspecao.setCodfil(consulta.getString("Codfil"));
                pstInspecao.setData(consulta.getString("Data"));
                pstInspecao.setPergunta(consulta.getString("Descricao"));
                pstInspecao.setResposta(consulta.getString("Nome"));
                pstInspecao.setCodInspecao(consulta.getString("CodInspecao"));
                pstInspecao.setMatr(consulta.getString("Matr"));
                pstInspecao.setLocal(consulta.getString("Local"));
                pstInspecao.setHr_Alter(consulta.getString("Hora"));
                retorno.add(pstInspecao);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstInspecaoDao.listaTodosPontos - " + e.getMessage() + "\r\n"
                    + " SELECT max(PstInspecao.Hr_Alter) Hora, Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, "
                    + " PstInspecao.Data, PstInspecao.Matr, PstServ.Local, PstInspecao.CodInspecao, Funcion.Nome "
                    + " FROM PstInspecao "
                    + " LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao "
                    + "                 AND PstServ.Codfil = PstInspecao.CodFil "
                    + " LEFT JOIN Funcion ON Funcion.Matr = PstInspecao.Matr "
                    + "                AND Funcion.Codfil = PstInspecao.Codfil "
                    + " LEFT JOIN Inspecoes ON Inspecoes.Codigo = PstInspecao.CodInspecao "
                    + " WHERE PstInspecao.Data = " + data
                    + " GROUP BY Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, PstInspecao.Data, PstInspecao.Matr, "
                    + " PstInspecao.CodInspecao, PstServ.Local, Funcion.Nome "
                    + " ORDER BY Hora DESC");
        }
    }

    /**
     * Busca as inspeções agrupadas já utilizando o código e o código do
     * operador
     *
     * @param data
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PstInspecao> listaInspecoesAgrupadasCodigo(String data, Persistencia persistencia) throws Exception {
        try {
            List<PstInspecao> retorno = new ArrayList<>();
            String sql = " SELECT Pessoa.Nome Inspetor, PstInspecao.Codigo, max(PstInspecao.Hr_Alter) Hora,\n"
                    + " Inspecoes.Descricao + (select TOP 1 CASE WHEN Inspecoes.Descricao = 'Entrada e Saída de Viaturas' THEN ' - ' + X.Resposta ELSE '' END from PstInspecao AS X where X.data = pstinspecao.data order by sequencia) Descricao, PstInspecao.Secao, PstInspecao.CodFil, \n"
                    + "PstInspecao.Data, PstInspecao.Matr, PstServ.Local, PstInspecao.CodInspecao, Funcion.Nome \n"
                    + "FROM PstInspecao \n"
                    + "LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao \n"
                    + "                AND PstServ.Codfil = PstInspecao.CodFil \n"
                    + "LEFT JOIN Funcion ON Funcion.Matr = PstInspecao.Matr \n"
                    + "               AND Funcion.Codfil = PstInspecao.Codfil \n"
                    + "LEFT JOIN Pessoa ON Pessoa.Codigo = PstInspecao.CodOperador \n"
                    + "LEFT JOIN Inspecoes ON Inspecoes.Codigo = PstInspecao.CodInspecao \n"
                    + "WHERE PstInspecao.Data = ?\n"
                    + "GROUP BY Pessoa.Nome, PstInspecao.Codigo, Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, PstInspecao.Data, PstInspecao.Matr, \n"
                    + "PstInspecao.CodInspecao, PstServ.Local, Funcion.Nome \n"
                    + "ORDER BY CASE WHEN Descricao = 'RESUMO DO TRABALHO DE CAMPO' THEN '0' ELSE Descricao END, Hora DESC ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.select();
            PstInspecao pstInspecao;
            while (consulta.Proximo()) {
                pstInspecao = new PstInspecao();
                pstInspecao.setCodigo(consulta.getString("Codigo"));
                pstInspecao.setSecao(consulta.getString("Secao"));
                pstInspecao.setCodfil(consulta.getString("Codfil"));
                pstInspecao.setData(consulta.getString("Data"));
                pstInspecao.setPergunta(consulta.getString("Descricao"));
                pstInspecao.setResposta(consulta.getString("Nome"));
                pstInspecao.setCodInspecao(consulta.getString("CodInspecao"));
                pstInspecao.setMatr(consulta.getString("Matr"));
                pstInspecao.setLocal(consulta.getString("Local"));
                pstInspecao.setHr_Alter(consulta.getString("Hora"));
                pstInspecao.setInspetor(consulta.getString("Inspetor"));
                retorno.add(pstInspecao);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstInspecaoDao.listaInspecoesAgrupadasCodigo - " + e.getMessage() + "\r\n"
                    + " SELECT Pessoa.Nome Inspetor, PstInspecao.Codigo, max(PstInspecao.Hr_Alter) Hora, Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, \n"
                    + "PstInspecao.Data, PstInspecao.Matr, PstServ.Local, PstInspecao.CodInspecao, Funcion.Nome \n"
                    + "FROM PstInspecao \n"
                    + "LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao \n"
                    + "                AND PstServ.Codfil = PstInspecao.CodFil \n"
                    + "LEFT JOIN Funcion ON Funcion.Matr = PstInspecao.Matr \n"
                    + "               AND Funcion.Codfil = PstInspecao.Codfil \n"
                    + "LEFT JOIN Pessoa ON Pessoa.Codigo = PstInspecao.CodOperador \n"
                    + "LEFT JOIN Inspecoes ON Inspecoes.Codigo = PstInspecao.CodInspecao \n"
                    + "WHERE PstInspecao.Data = " + data + "\n"
                    + "GROUP BY Pessoa.Nome, PstInspecao.Codigo, Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, PstInspecao.Data, PstInspecao.Matr, \n"
                    + "PstInspecao.CodInspecao, PstServ.Local, Funcion.Nome \n"
                    + "ORDER BY Hora DESC ");
        }
    }

    /**
     * Busca todas as inspeções realizadas em determinada data
     *
     * @param secao
     * @param codFil
     * @param data
     * @param matr
     * @param codInspecao
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PstInspecao> listaInspecoes(String secao, String codFil, String data, String matr, String codInspecao,
            Persistencia persistencia) throws Exception {
        try {
            List<PstInspecao> retorno = new ArrayList<>();
            String sql = " SELECT COALESCE(InspecoesItensLista.Descricao, PstInspecao.Resposta) RespostaTratada, \n"
                    + "    PstInspecao.Codigo, PstInspecao.Secao, PstInspecao.CodFil, CONVERT(VarChar, PstInspecao.Data, 112) Data, \n"
                    + "    PstInspecao.Sequencia, PstInspecao.Pergunta,\n"
                    + "    PstInspecao.CaminhoImagem, PstInspecao.CaminhoVideo, \n";
            if (persistencia.getEmpresa().contains("SATSASEX")
                    || persistencia.getEmpresa().contains("SATPISCINAFACIL")) {
                sql += " PstInspecao.CaminhoAudio, \n";
            } else {
                sql += " '' CaminhoAudio, \n";
            }
            sql += "    PstInspecao.Matr, PstInspecao.Veiculo, PstInspecao.CodInspecao, PstInspecao.Latitude, PstInspecao.Longitude, \n"
                    + "    PstInspecao.CodOperador, PstInspecao.Operador, PstInspecao.Dt_Alter, PstInspecao.Hr_Alter, \n"
                    + " PstServ.Local \n"
                    + " FROM PstInspecao "
                    + " LEFT JOIN InspecoesItensLista\n"
                    + "  ON PstInspecao.CodInspecao = InspecoesItensLista.Codigo\n"
                    + " AND PstInspecao.Sequencia = InspecoesItensLista.Sequencia\n"
                    + " AND PstInspecao.Resposta = InspecoesItensLista.Item"
                    + " LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao "
                    + "                 AND PstServ.Codfil = PstInspecao.CodFil "
                    + " WHERE PstInspecao.Data = ? AND PstInspecao.CodFil = ? AND PstInspecao.Secao = ?"
                    + "                      AND PstInspecao.Codigo = ? AND PstInspecao.Matr = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codFil);
            consulta.setString(secao);
            consulta.setString(codInspecao);
            consulta.setString(matr);
            consulta.select();
            PstInspecao pstInspecao;
            while (consulta.Proximo()) {
                pstInspecao = new PstInspecao();
                pstInspecao.setCodInspecao(consulta.getString("CodInspecao"));
                pstInspecao.setSecao(consulta.getString("Secao"));
                pstInspecao.setCodfil(consulta.getString("Codfil"));
                pstInspecao.setData(consulta.getString("Data"));
                pstInspecao.setSequencia(consulta.getString("Sequencia"));
                pstInspecao.setPergunta(consulta.getString("Pergunta"));
                pstInspecao.setResposta(consulta.getString("RespostaTratada"));
                pstInspecao.setCaminhoImagem(consulta.getString("CaminhoImagem"));
                pstInspecao.setCaminhoVideo(consulta.getString("CaminhoVideo"));
                pstInspecao.setCaminhoAudio(consulta.getString("CaminhoAudio"));
                pstInspecao.setMatr(consulta.getString("Matr"));
                pstInspecao.setVeiculo(consulta.getString("Veiculo"));
                pstInspecao.setOperador(consulta.getString("Operador"));
                pstInspecao.setDt_Alter(consulta.getString("Dt_Alter"));
                pstInspecao.setHr_Alter(consulta.getString("Hr_Alter"));
                pstInspecao.setLocal(consulta.getString("Local"));
                retorno.add(pstInspecao);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstInspecaoDao.listaTodosPontos - " + e.getMessage() + "\r\n"
                    + " SELECT * "
                    + " FROM PstInspecao "
                    + " WHERE Data = " + data
                    + " ORDER BY Dt_Alter DESC, Hr_Alter DESC");
        }
    }

/**
     * Busca as inspeções agrupadas já utilizando o código e o código do
     * operador
     * @param codPessoa
     * @param data
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PstInspecao> listaInspecoesAgrupadasCodPessoa(String codPessoa, String data, Persistencia persistencia) throws Exception {
        try {
            List<PstInspecao> retorno = new ArrayList<>();
            String sql = " SELECT Pessoa.Nome Inspetor, PstInspecao.Codigo, max(PstInspecao.Hr_Alter) Hora,\n"
                    + " Inspecoes.Descricao + (select TOP 1 CASE WHEN Inspecoes.Descricao = 'Entrada e Saída de Viaturas' THEN ' - ' + X.Resposta ELSE '' END from PstInspecao AS X where X.data = pstinspecao.data order by sequencia) Descricao, PstInspecao.Secao, PstInspecao.CodFil, \n"
                    + "PstInspecao.Data, PstInspecao.Matr, PstServ.Local, PstInspecao.CodInspecao, Funcion.Nome \n"
                    + "FROM PstInspecao \n"
                    + "LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao \n"
                    + "                AND PstServ.Codfil = PstInspecao.CodFil \n"
                    + "LEFT JOIN Funcion ON Funcion.Matr = PstInspecao.Matr \n"
                    + "               AND Funcion.Codfil = PstInspecao.Codfil \n"
                    + "LEFT JOIN Pessoa ON Pessoa.Codigo = PstInspecao.CodOperador \n"
                    + "LEFT JOIN Inspecoes ON Inspecoes.Codigo = PstInspecao.CodInspecao \n"
                    + "WHERE Pessoa.Codigo = "+codPessoa
                    + " and PstInspecao.Data = ?\n"
                    + "GROUP BY Pessoa.Nome, PstInspecao.Codigo, Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, PstInspecao.Data, PstInspecao.Matr, \n"
                    + "PstInspecao.CodInspecao, PstServ.Local, Funcion.Nome \n"
                    + "ORDER BY CASE WHEN Descricao = 'RESUMO DO TRABALHO DE CAMPO' THEN '0' ELSE Descricao END, Hora DESC ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.select();
            PstInspecao pstInspecao;
            while (consulta.Proximo()) {
                pstInspecao = new PstInspecao();
                pstInspecao.setCodigo(consulta.getString("Codigo"));
                pstInspecao.setSecao(consulta.getString("Secao"));
                pstInspecao.setCodfil(consulta.getString("Codfil"));
                pstInspecao.setData(consulta.getString("Data"));
                pstInspecao.setPergunta(consulta.getString("Descricao"));
                pstInspecao.setResposta(consulta.getString("Nome"));
                pstInspecao.setCodInspecao(consulta.getString("CodInspecao"));
                pstInspecao.setMatr(consulta.getString("Matr"));
                pstInspecao.setLocal(consulta.getString("Local"));
                pstInspecao.setHr_Alter(consulta.getString("Hora"));
                pstInspecao.setInspetor(consulta.getString("Inspetor"));
                retorno.add(pstInspecao);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstInspecaoDao.listaInspecoesAgrupadasCodigo - " + e.getMessage() + "\r\n"
                    + " SELECT Pessoa.Nome Inspetor, PstInspecao.Codigo, max(PstInspecao.Hr_Alter) Hora, Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, \n"
                    + "PstInspecao.Data, PstInspecao.Matr, PstServ.Local, PstInspecao.CodInspecao, Funcion.Nome \n"
                    + "FROM PstInspecao \n"
                    + "LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao \n"
                    + "                AND PstServ.Codfil = PstInspecao.CodFil \n"
                    + "LEFT JOIN Funcion ON Funcion.Matr = PstInspecao.Matr \n"
                    + "               AND Funcion.Codfil = PstInspecao.Codfil \n"
                    + "LEFT JOIN Pessoa ON Pessoa.Codigo = PstInspecao.CodOperador \n"
                    + "LEFT JOIN Inspecoes ON Inspecoes.Codigo = PstInspecao.CodInspecao \n"
                    + "WHERE PstInspecao.Data = " + data + "\n"
                    + "GROUP BY Pessoa.Nome, PstInspecao.Codigo, Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, PstInspecao.Data, PstInspecao.Matr, \n"
                    + "PstInspecao.CodInspecao, PstServ.Local, Funcion.Nome \n"
                    + "ORDER BY Hora DESC ");
        }
    }

    
    
    /**
     * Busca todas as inspeções realizadas em determinada data
     *
     * @param secao
     * @param codFil
     * @param data
     * @param matr
     * @param codInspecao
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PstInspecao> listaInspecoes(String codigo, Persistencia persistencia) throws Exception {
        try {
            List<PstInspecao> retorno = new ArrayList<>();
            String sql = " SELECT ISNULL(InspecoesItensLista.Descricao, PstInspecao.Resposta) RespostaTratada, \n"
                    + "    PstInspecao.Codigo, PstInspecao.Secao, PstInspecao.CodFil, CONVERT(VarChar, PstInspecao.Data, 112) Data, \n"
                    + "    PstInspecao.Sequencia, PstInspecao.Pergunta,\n"
                    + "    PstInspecao.CaminhoImagem, PstInspecao.CaminhoVideo, \n";
            if (persistencia.getEmpresa().contains("SATSASEX")
                    || persistencia.getEmpresa().contains("SATPISCINAFACIL")) {
                sql += " PstInspecao.CaminhoAudio, \n";
            } else {
                sql += " '' CaminhoAudio, \n";
            }
            sql += "    PstInspecao.Matr, PstInspecao.Veiculo, PstInspecao.CodInspecao, PstInspecao.Latitude, PstInspecao.Longitude, \n"
                    + "    PstInspecao.CodOperador, PstInspecao.Operador, PstInspecao.Dt_Alter, PstInspecao.Hr_Alter, \n"
                    + " PstServ.Local, Pessoa.Nome Inspetor \n"
                    + " FROM PstInspecao \n"
                    + " LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao \n"
                    + "                 AND PstServ.Codfil = PstInspecao.CodFil \n"
                    + " LEFT JOIN Pessoa ON Pessoa.Codigo = PstInspecao.CodOperador \n"
                    + " LEFT JOIN InspecoesItensLista\n"
                    + "  ON PstInspecao.CodInspecao = InspecoesItensLista.Codigo\n"
                    + " AND PstInspecao.Sequencia = InspecoesItensLista.Sequencia\n"
                    + " AND PstInspecao.Resposta = InspecoesItensLista.Item"
                    + " WHERE PstInspecao.Codigo = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();
            PstInspecao pstInspecao;
            while (consulta.Proximo()) {
                pstInspecao = new PstInspecao();
                pstInspecao.setCodInspecao(consulta.getString("CodInspecao"));
                pstInspecao.setSecao(consulta.getString("Secao"));
                pstInspecao.setCodfil(consulta.getString("Codfil"));
                pstInspecao.setData(consulta.getString("Data"));
                pstInspecao.setSequencia(consulta.getString("Sequencia"));
                pstInspecao.setPergunta(consulta.getString("Pergunta"));
                pstInspecao.setResposta(consulta.getString("RespostaTratada"));
                pstInspecao.setCaminhoImagem(consulta.getString("CaminhoImagem"));
                pstInspecao.setCaminhoVideo(consulta.getString("CaminhoVideo"));
                pstInspecao.setCaminhoAudio(consulta.getString("CaminhoAudio"));
                pstInspecao.setMatr(consulta.getString("Matr"));
                pstInspecao.setVeiculo(consulta.getString("Veiculo"));
                pstInspecao.setOperador(consulta.getString("Operador"));
                pstInspecao.setDt_Alter(consulta.getString("Dt_Alter"));
                pstInspecao.setHr_Alter(consulta.getString("Hr_Alter"));
                pstInspecao.setLocal(consulta.getString("Local"));
                pstInspecao.setInspetor(consulta.getString("Inspetor"));
                pstInspecao.setCodigo(consulta.getString("Codigo"));
                retorno.add(pstInspecao);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstInspecaoDao.listaInspecoes - " + e.getMessage() + "\r\n"
                    + " SELECT PstInspecao.*, PstServ.Local "
                    + " FROM PstInspecao "
                    + " LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao "
                    + "                 AND PstServ.Codfil = PstInspecao.CodFil "
                    + " WHERE PstInspecao.Codigo = " + codigo);
        }
    }

    public List<PstInspecao> listagemPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            List<PstInspecao> retorno = new ArrayList<>();
            String sql = " SELECT * FROM ( SELECT ROW_NUMBER() OVER (ORDER BY max(PstInspecao.Hr_Alter) DESC) AS RowNum, "
                    + " max(PstInspecao.Hr_Alter) Hora, Inspecoes.Descricao + (select TOP 1 CASE WHEN Inspecoes.Descricao = 'Entrada e Saída de Viaturas' THEN ' - ' + X.Resposta ELSE '' END from PstInspecao AS X where X.data = pstinspecao.data order by sequencia) Descricao, PstInspecao.Secao, PstInspecao.CodFil, "
                    + " CONVERT(VarChar, PstInspecao.Data, 112) Data, PstInspecao.Matr, PstServ.Local, PstInspecao.CodInspecao, Funcion.Nome, \n"
                    + " PstInspecao.CodOperador, Pessoa.Nome Inspetor, PstInspecao.Latitude, PstInspecao.Longitude, PstInspecao.Codigo \n"
                    + " FROM PstInspecao "
                    + " LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao "
                    + "                 AND PstServ.Codfil = PstInspecao.CodFil "
                    + " LEFT JOIN Funcion ON Funcion.Matr = PstInspecao.Matr "
                    + "                AND Funcion.Codfil = PstInspecao.Codfil \n"
                    + " LEFT JOIN Pessoa ON Pessoa.Codigo = PstInspecao.CodOperador \n"
                    + " LEFT JOIN Inspecoes ON Inspecoes.Codigo = PstInspecao.CodInspecao "
                    + " WHERE PstInspecao.codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?) ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            sql += " GROUP BY Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, PstInspecao.Data, PstInspecao.Matr, "
                    + " PstInspecao.CodInspecao, PstServ.Local, Funcion.Nome, \n"
                    + " PstInspecao.CodOperador, Pessoa.Nome, PstInspecao.Latitude, PstInspecao.Longitude, PstInspecao.Codigo \n"
                    + " ) AS RowConstrainedResult"
                    + " WHERE RowNum >= ? AND RowNum < ? "
                    + " ORDER BY RowNum ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
            consulta.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();
            PstInspecao pstInspecao;
            while (consulta.Proximo()) {
                pstInspecao = new PstInspecao();
                pstInspecao.setSecao(consulta.getString("Secao"));
                pstInspecao.setCodfil(consulta.getString("Codfil"));
                pstInspecao.setData(consulta.getString("Data"));
                pstInspecao.setHr_Alter(consulta.getString("Hora"));
                pstInspecao.setSequencia(consulta.getString("CodInspecao"));
                pstInspecao.setCodigo(consulta.getString("Codigo"));
                pstInspecao.setCodInspecao(consulta.getString("CodInspecao"));
                pstInspecao.setPergunta(consulta.getString("Descricao"));
                pstInspecao.setResposta(consulta.getString("Nome"));
//                pstInspecao.setCaminhoImagem(consulta.getString("CaminhoImagem"));
//                pstInspecao.setCaminhoVideo(consulta.getString("CaminhoVideo"));
                pstInspecao.setMatr(consulta.getString("Matr"));
//                pstInspecao.setVeiculo(consulta.getString("Veiculo"));
                pstInspecao.setLatitude(consulta.getString("Latitude"));
                pstInspecao.setLongitude(consulta.getString("Longitude"));
                pstInspecao.setLocal(consulta.getString("Local"));
                pstInspecao.setInspetor(consulta.getString("Inspetor"));
                pstInspecao.setCodOperador(consulta.getString("CodOperador"));
                pstInspecao.setLocal(consulta.getString("Local"));
                retorno.add(pstInspecao);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstInspecaoDao.listagemPaginada - " + e.getMessage());
        }
    }

    public int contagem(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            int retorno = 0;
            String sql = " SELECT COUNT(*) quantidade FROM ( SELECT ROW_NUMBER() OVER (ORDER BY max(PstInspecao.Hr_Alter) DESC) AS RowNum, "
                    + " max(PstInspecao.Hr_Alter) Hora, Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, "
                    + " PstInspecao.Data, PstInspecao.Matr, PstServ.Local, PstInspecao.CodInspecao, Funcion.Nome "
                    + " FROM PstInspecao "
                    + " LEFT JOIN PstServ ON PstServ.Secao = PstInspecao.Secao "
                    + "                 AND PstServ.Codfil = PstInspecao.CodFil "
                    + " LEFT JOIN Funcion ON Funcion.Matr = PstInspecao.Matr "
                    + "                AND Funcion.Codfil = PstInspecao.Codfil "
                    + " LEFT JOIN Inspecoes ON Inspecoes.Codigo = PstInspecao.CodInspecao "
                    + " WHERE PstInspecao.codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?) ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            sql += " GROUP BY Inspecoes.Descricao, PstInspecao.Secao, PstInspecao.CodFil, PstInspecao.Data, PstInspecao.Matr, "
                    + " PstInspecao.CodInspecao, PstServ.Local, Funcion.Nome) AS RowConstrainedResult ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
            consulta.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.select();
            while (consulta.Proximo()) {
                retorno = consulta.getInt("quantidade");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstInspecaoDao.contagem - " + e.getMessage());
        }
    }

    public List<Inspecoes> listaItensInspecoes(Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            List<Inspecoes> Retorno = new ArrayList<>();
            Inspecoes inspecoes;

            sql = "SELECT * FROM inspecoes ORDER BY Descricao";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            while (consulta.Proximo()) {
                inspecoes = new Inspecoes();

                inspecoes.setCodigo(consulta.getString("Codigo"));
                inspecoes.setDescricao(consulta.getString("Descricao"));
                inspecoes.setDt_Alter(consulta.getString("Dt_Alter"));
                inspecoes.setHr_Alter(consulta.getString("Hr_Alter"));
                inspecoes.setOperador(consulta.getString("Operador"));

                Retorno.add(inspecoes);
            }
            consulta.Close();

            return Retorno;
        } catch (Exception e) {
            throw new Exception("PstInspecaoDao.listaInspecoes - " + e.getMessage() + "\n\n" + sql);
        }
    }

    public List<InspecoesItens> listarInspecoesItens(String codigo, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            List<InspecoesItens> retorno = new ArrayList<>();

            sql = "select * FROM inspecoesItens WHERE Codigo = ? ORDER BY Sequencia";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(codigo);
            consulta.select();

            InspecoesItens inpecoesItens;

            while (consulta.Proximo()) {
                inpecoesItens = new InspecoesItens();

                inpecoesItens.setCodigo(consulta.getString("Codigo"));
                inpecoesItens.setSequencia(consulta.getString("Sequencia"));
                inpecoesItens.setPergunta(consulta.getString("Pergunta"));
                inpecoesItens.setTipoResp(consulta.getString("TipoResp"));
                inpecoesItens.setFoto(consulta.getString("Foto"));
                inpecoesItens.setVideo(consulta.getString("Video"));

                retorno.add(inpecoesItens);
            }
            consulta.Close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("PstInspecaoDao.listarInspecoesItens - " + e.getMessage() + "\n\n" + sql);
        }
    }

    public List<PstInspecao> listaInspecoesExec(String data, String codFil, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            List<PstInspecao> Retorno = new ArrayList<>();
            PstInspecao pstInspecao;

            sql = "IF OBJECT_ID('tempDB..##tDias') IS NOT NULL drop table ##tDias;\n"
                    + " DECLARE @dataInicio date, @dataFim date;\n"
                    + " SET @dataFim    = ?;\n"
                    + " SET @dataInicio = (SELECT DATEADD(day, -30, @dataFim));\n"
                    + " CREATE TABLE ##tDias(dia date);\n"
                    + " While @dataFim >= @dataInicio \n"
                    + " Begin \n"
                    + "    INSERT into ##tDias(dia) VALUES(@dataInicio);\n"
                    + "    Set @dataInicio = (SELECT DateAdd(Day, 1, @dataInicio));\n"
                    + " End\n";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.insert();

            sql = " SELECT DISTINCT\n"
                    + " ISNULL(PstInspecao.Codigo,'') Codigo,\n"
                    + " ISNULL(A.dia,'') dia,\n"
                    + " ISNULL(Inspecoes.Descricao + (select TOP 1 CASE WHEN Inspecoes.Descricao = 'Entrada e Saída de Viaturas' THEN ' - ' + X.Resposta ELSE '' END from PstInspecao AS X where X.data = pstinspecao.data order by sequencia),'') descricao,\n"
                    + " ISNULL(PstServ.Posto,'') Posto,\n"
                    + " ISNULL(Pessoa.Nome,'') Nome\n"
                    + " FROM ##tDias AS A\n"
                    + " LEFT JOIN PstInspecao  \n"
                    + "  ON PstInspecao.CodFil = ? \n"
                    + " AND A.dia = PstInspecao.data\n"
                    + " LEFT JOIN inspecoes\n"
                    + "   ON PstInspecao.CodInspecao = inspecoes.Codigo\n"
                    + " LEFT JOIN Funcion\n"
                    + "   ON PstInspecao.Matr   = Funcion.Matr\n"
                    + "  AND PstInspecao.CodFil = Funcion.CodFil\n"
                    + " LEFT JOIN Pessoa\n"
                    + "   ON PstInspecao.CodOperador = Pessoa.Codigo\n"
                    + " LEFT JOIN PstServ\n"
                    + "   ON PstInspecao.Secao = PstServ.Secao\n"
                    + " ORDER BY ISNULL(A.dia,'') DESC, ISNULL(inspecoes.descricao,'');";

            consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.select();

            while (consulta.Proximo()) {
                pstInspecao = new PstInspecao();

                pstInspecao.setCodigo(consulta.getString("Codigo"));
                pstInspecao.setData(consulta.getString("dia"));
                pstInspecao.setPergunta(consulta.getString("descricao"));
                pstInspecao.setLocal(consulta.getString("Posto"));
                pstInspecao.setInspetor(consulta.getString("Nome"));

                Retorno.add(pstInspecao);
            }
            consulta.Close();

            return Retorno;
        } catch (Exception e) {
            throw new Exception("PstInspecaoDao.listaInspecoes - " + e.getMessage() + "\n\n" + sql);
        }
    }

    /**
     * Busca inspeção a partir de codFil e seção
     *
     * @param codFil
     * @param secao
     * @param dataIni data inicial
     * @param dataFim data final
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<LogsSatMobEW> listaPorSecaoIntervalo(String codFil, String secao, String dataIni, String dataFim, Persistencia persistencia) throws Exception {
        String sql = "SELECT PstInspecao.Codigo,\n"
                + "       PstInspecao.CodFil,\n"
                + "       PstServ.Secao,\n"
                + "       PstServ.Local,\n"
                + "       PstInspecao.Data,\n"
                + "       PstInspecao.CodOperador,\n"
                + "       Pessoa.Nome            Operador,\n"
                + "       PstInspecao.Latitude,\n"
                + "       PstInspecao.Longitude,\n"
                + "       Inspecoes.Descricao + (select TOP 1 CASE WHEN Inspecoes.Descricao = 'Entrada e Saída de Viaturas' THEN ' - ' + X.Resposta ELSE '' END from PstInspecao AS X where X.data = pstinspecao.data order by sequencia) AS titulo\n"
                + "FROM PstInspecao (NOLOCK)\n"
                + "         LEFT JOIN PstServ (NOLOCK) ON PstServ.Secao = PstInspecao.Secao\n"
                + "    AND PstServ.CodFil = PstInspecao.CodFil\n"
                + "         LEFT JOIN Pessoa (NOLOCK) ON Pessoa.Codigo = PstInspecao.CodOperador\n"
                + "         LEFT JOIN Inspecoes (NOLOCK) ON Inspecoes.Codigo = PstInspecao.CodInspecao\n"
                + "WHERE PstInspecao.CodFil = ? \n"
                + "  AND PstInspecao.Secao = ? \n"
                + "  AND data BETWEEN ? AND ? \n"
                + "GROUP BY PstInspecao.Codigo, PstInspecao.CodFil, PstServ.Secao, PstServ.Local, PstInspecao.Data,\n"
                + "         PstInspecao.CodOperador, Pessoa.Nome,\n"
                + "         PstInspecao.Latitude, PstInspecao.Longitude, inspecoes.Descricao\n"
                + "ORDER BY PstInspecao.Data DESC";
        Consulta consulta = null;
        List<LogsSatMobEW> list = new ArrayList();

        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codFil);
            consulta.setString(secao);
            consulta.setString(dataIni);
            consulta.setString(dataFim);
            consulta.select();
            while (consulta.Proximo()) {
                LogsSatMobEW logsSatMobEW = new LogsSatMobEW();
                logsSatMobEW.setCodfil(consulta.getString("CodFil"));
//                logsSatMobEW.setTipo(consulta.getString("Tipo"));
                logsSatMobEW.setChave(consulta.getString("Codigo"));
                logsSatMobEW.setPosto(consulta.getString("Local"));
                logsSatMobEW.setSecao(consulta.getString("Secao"));
                logsSatMobEW.setFuncionario(consulta.getString("Operador"));
                logsSatMobEW.setData(consulta.getString("Data"));
                logsSatMobEW.setLatitude(consulta.getString("Latitude"));
                logsSatMobEW.setLongitude(consulta.getString("Longitude"));
                logsSatMobEW.setTitulo(consulta.getString("Titulo"));

                list.add(logsSatMobEW);
            }

            return list;
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    /**
     * Busca inspeção a partir de codFil e seção
     *
     * @param codFil código da filial
     * @param dataIni data inicial
     * @param persistencia
     * @return lista de PstInspecao e Cliente
     * @throws Exception
     */
    public List<PstInspecaoRelatorio> listaInspecaoRelatorio(String codFil, String dataIni, Persistencia persistencia) throws Exception {
        String sql = "SELECT Pessoa.Nome,\n"
                + "       PstServ.Local,\n"
                + "       PstInspecao.Dt_Alter Data,\n"
                + "       PstInspecao.Hr_Alter Hora,\n"
                + "       Clientes.Nred,\n"
                + "       Clientes.Ende,\n"
                + "       Clientes.Bairro,\n"
                + "       Clientes.Cidade,\n"
                + "       Clientes.Estado,\n"
                + "       Clientes.Latitude,\n"
                + "       Clientes.Longitude,\n"
                + "       PstInspecao.Pergunta,\n"
                + "       PstInspecao.Resposta\n"
                + "FROM PstInspecao (NOLOCK)\n"
                + "         LEFT JOIN PstServ (NOLOCK) ON PstServ.Secao = PstInspecao.Secao\n"
                + "    AND PstServ.CodFil = PstInspecao.CodFil\n"
                + "         LEFT JOIN Clientes (NOLOCK) ON Clientes.Codigo = PstServ.CodCli\n"
                + "    AND Clientes.CodFil = PstServ.CodFil\n"
                + "         LEFT JOIN Pessoa ON Pessoa.Codigo = PstInspecao.CodOperador\n"
                + "WHERE PstInspecao.Data = ? \n"
                + "  AND PstInspecao.CodFil = ? \n"
                + "ORDER BY Data, Hora, Local, Pergunta";
        Consulta consulta = null;
        List<PstInspecaoRelatorio> list = new ArrayList();

        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(dataIni);
            consulta.setBigDecimal(codFil);
            consulta.select();
            while (consulta.Proximo()) {
                PstInspecaoRelatorio relatorio = new PstInspecaoRelatorio();
                PstInspecao inspecao = new PstInspecao();
                Clientes cliente = new Clientes();

                cliente.setNome(consulta.getString("Nome"));
                inspecao.setLocal(consulta.getString("Local"));
                inspecao.setData(consulta.getString("Data"));
                inspecao.setHr_Alter(consulta.getString("Hora"));
                cliente.setNRed(consulta.getString("Nred"));
                cliente.setEnde(consulta.getString("Ende"));
                cliente.setBairro(consulta.getString("Bairro"));
                cliente.setCidade(consulta.getString("Cidade"));
                cliente.setEstado(consulta.getString("Estado"));
                inspecao.setLatitude(consulta.getString("Latitude"));
                inspecao.setLongitude(consulta.getString("Longitude"));
                inspecao.setPergunta(consulta.getString("Pergunta"));
                inspecao.setResposta(consulta.getString("Resposta"));

                relatorio.setCliente(cliente);
                relatorio.setPstInspecao(inspecao);
                list.add(relatorio);
            }

            return list;
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    public List<ColumnModel> listaInspecaoRelatorioColunadoCabecalho(String codFil, String dataIni, Persistencia persistencia) throws Exception {
        String sql = queryGrideDinamico(codFil, dataIni);

        Consulta consulta = null;
        List<ColumnModel> columnsGride = new ArrayList<>();

        try {
            consulta = new Consulta(sql, persistencia);

            consulta.select();
            int contador = 0;

            for (String item : consulta.getMetadataRotulo()) {
                columnsGride.add(new ColumnModel(item, item, contador));
                contador++;
            }

            return columnsGride;
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    public List<LinhaModel> listaInspecaoRelatorioColunadoDados(String codFil, String dataIni, Persistencia persistencia) throws Exception {
        String sql = queryGrideDinamico(codFil, dataIni);

        Consulta consulta = null;
        List<ColumnModel> columnsGride = new ArrayList<>();
        List<ColumnModel> dadosGrideLinha = new ArrayList<>();
        List<LinhaModel> dadosGride = new ArrayList<>();

        try {
            // Cabeçalhos
            consulta = new Consulta(sql, persistencia);
            consulta.select();
            int contador = 0;

            for (String item : consulta.getMetadataRotulo()) {
                columnsGride.add(new ColumnModel(item, item, contador));
                contador++;
            }

            // Dados
            while (consulta.Proximo()) {
                dadosGrideLinha = new ArrayList<>();

                for (int I = 0; I < columnsGride.size(); I++) {
                    dadosGrideLinha.add(new ColumnModel(consulta.getString(columnsGride.get(I).getProperty()),
                            columnsGride.get(I).getProperty(),
                            I));
                }

                dadosGride.add(new LinhaModel(dadosGrideLinha));
            }

            return dadosGride;
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    private String queryGrideDinamico(String codFil, String dataIni) {
        String sql = "-- Parte 1 - Identificar Todas as Perguntas (que serão colunas) na Inspeção\n"
                + "DECLARE @PerguntasSQL   VARCHAR(5000)\n"
                + "DECLARE @SQL            VARCHAR(5000)\n"
                + "\n"
                + "SET @PerguntasSQL   = ''\n"
                + "SET @SQL            = ''\n"
                + "\n"
                + "SELECT \n"
                + "@PerguntasSQL=@PerguntasSQL+'['+[A].[Column]+'], '\n"
                + "FROM (SELECT DISTINCT \n"
                + "	  Pergunta as[Column]\n"
                + "	  FROM PstInspecao\n"
                + "	  WHERE PstInspecao.Data = '" + dataIni + "' AND PstInspecao.CodFil = '" + codFil + "') AS A\n"
                + "\n"
                + "SET @PerguntasSQL=LEFT(@PerguntasSQL,len(@PerguntasSQL)-1)\n"
                + "\n"
                + "-- Parte 2 - Montar Pivot\n"
                + "SET @SQL='SELECT Descricao Nome,Local Local,Data,Hr_Alter Hora,Nred,Ende Endereco,Bairro,Cidade,Estado,Latitude,Longitude,'\n"
                + "		+ @PerguntasSQL\n"
                + "		+' FROM (SELECT \n"
                + "				Inspecoes.Descricao,\n"
                + "				PstServ.Local,\n"
                + "				PstInspecao.Data,\n"
                + "				PstInspecao.Hr_Alter,\n"
                + "				Clientes.Nred,\n"
                + "				Clientes.Ende,\n"
                + "				Clientes.Bairro,\n"
                + "				Clientes.Cidade,\n"
                + "				Clientes.Estado,\n"
                + "				PstInspecao.Latitude,\n"
                + "				PstInspecao.Longitude,\n"
                + "			        PstInspecao.Pergunta,\n"
                + "				PstInspecao.Resposta,\n"
                + "				Pessoa.Nome\n"
                + "				FROM PstInspecao \n"
                + "				JOIN PstServ\n"
                + "				  ON PstInspecao.Secao  = PstServ.Secao\n"
                + "				 AND PstInspecao.CodFil = PstServ.CodFil\n"
                + "				JOIN Inspecoes\n"
                + "				  ON PstInspecao.CodInspecao = Inspecoes.Codigo\n"
                + "				LEFT JOIN Pessoa\n"
                + "				  ON PstInspecao.Matr = Pessoa.Matr\n"
                + "				LEFT JOIN Clientes\n"
                + "				  ON PstServ.CodCli = Clientes.Codigo\n"
                + "				WHERE PstInspecao.Data = ''" + dataIni + "'' AND PstInspecao.CodFil = ''" + codFil + "'') AS TabelaRef \n"
                + "		  PIVOT (MAX(Resposta) FOR Pergunta IN(' + @PerguntasSQL + ')) AS Dados'\n"
                + "\n"
                + "-- Parte 3 - Executar Consulta\n"
                + "EXEC(@SQL)";

        return sql;
    }

}
