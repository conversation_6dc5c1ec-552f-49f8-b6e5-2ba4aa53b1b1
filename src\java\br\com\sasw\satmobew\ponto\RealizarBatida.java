/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satmobew.ponto;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import Dados.Consulta;
import SasBeans.Filiais;
import SasBeans.Pessoa;
import SasBeans.RHPonto;
import SasBeans.RHPontoDet;
import SasBeans.RHPontoGEO;
import SasBeans.RHPontoImagem;
import SasBeans.RHPontoProcessa;
import SasBeansCompostas.FuncionPstServ;
import SasDaos.FiliaisDao;
import SasDaos.FuncionDao;
import SasDaos.LoginDao;
import SasDaos.OperacoesServDao;
import SasDaos.PessoaDao;
import SasDaos.RHPontoDao;
import SasDaos.RHPontoDetDao;
import SasDaos.RHPontoGeoDao;
import static SasLibrary.ValidarUsuario.ValidaUsuarioPonto;
import br.com.sasw.pacotesuteis.azure.AzureBlobStorageConstante;
import br.com.sasw.pacotesuteis.azure.AzureBlobStorageServico;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import br.com.sasw.pacotesuteis.utilidades.Horaminuto;
import static br.com.sasw.pacotesuteis.utilidades.Logos.getLogo;
import static br.com.sasw.pacotesuteis.utilidades.Logos.getLogoAnexo;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import br.com.sasw.satmobew.ValidarUsuario;
import br.com.sasw.satmobew.mensagem.Messages;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.font.TextAttribute;
import java.awt.font.TextLayout;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.net.URL;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import static java.time.temporal.ChronoUnit.MINUTES;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import javax.imageio.ImageIO;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.w3c.dom.Document;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "RealizarBatida", urlPatterns = {"/ponto/RealizarBatida"})
public class RealizarBatida extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;

    @Override
    public void init() {
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    private Document obterDocumentDeByte(InputStream documentoXml) throws Exception {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        factory.setNamespaceAware(true);
        DocumentBuilder builder = factory.newDocumentBuilder();
        return builder.parse(documentoXml);
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        logerro = new ArquivoLog();

        String codPessoa = request.getParameter("codpessoa");
        String senha = request.getParameter("senha");
        String param = request.getParameter("param");
        String empresa = param;
        String matricula = request.getParameter("matricula");
        String data = request.getParameter("data");
        String hora = request.getParameter("hora");
        String codfil = request.getParameter("codfil");
        String operador = request.getParameter("operador");
        String imagem = request.getParameter("imagem");
        String latitude = request.getParameter("lat");
        String longitude = request.getParameter("long");
        String secao = request.getParameter("secao");
        String dataAtual = request.getParameter("dataAtual");
        String horaAtual = request.getParameter("horaAtual");
        String idioma = request.getParameter("idioma");
        Messages messages = new Messages(idioma);
        String TracerText = "";

        if (null == dataAtual || dataAtual.equals("")) {
            dataAtual = DataAtual.getDataAtual("SQL");
        }
        if (null == horaAtual || horaAtual.equals("")) {
            horaAtual = DataAtual.getDataAtual("HORA");
        }

        String retorno = "<?xml version=\"1.0\"?>";

        try {
            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codPessoa, param, logerro);
            //TracerText += "\n\n" + request.getParameterMap();

            tStart = System.currentTimeMillis();

            Persistencia persistencia = pool.getConexao(param);
            empresa = persistencia.getEmpresa().equals("SATSASEX") ? "SASW" : persistencia.getEmpresa().replace("SAT", "");

            boolean ValidacaoUsuarioPonto = ValidaUsuarioPonto(codPessoa, senha, persistencia);

            if (!ValidacaoUsuarioPonto && (persistencia.getEmpresa().equals("SATINTERFORT") || persistencia.getEmpresa().equals("SATSERVITE"))) {
                if (persistencia.getEmpresa().equals("SATSERVITE")) {
                    persistencia = pool.getConexao("SATINTERFORT");
                    ValidacaoUsuarioPonto = ValidaUsuarioPonto(codPessoa, senha, persistencia);
                } else if (persistencia.getEmpresa().equals("SATINTERFORT")) {
                    persistencia = pool.getConexao("SATSERVITE");
                    ValidacaoUsuarioPonto = ValidaUsuarioPonto(codPessoa, senha, persistencia);
                } else {
                    persistencia = pool.getConexao("SATSERVITE");
                    ValidacaoUsuarioPonto = ValidaUsuarioPonto(codPessoa, senha, persistencia);
                }
            }

            if (ValidacaoUsuarioPonto) {
                PessoaDao pessoaDao = new PessoaDao();
                FuncionDao funcionDao = new FuncionDao();
                RHPontoDao rHPontoDao = new RHPontoDao();
                RHPontoGeoDao rHPontoGeoDao = new RHPontoGeoDao();
                RHPontoDetDao rHPontoDetDao = new RHPontoDetDao();
                String dtCompet = data;
                int quantidade;
                // Trata Secao - Pegar Secao do Funcionario - Carlos 30/08/2023
                secao = funcionDao.getSecao(matricula, persistencia);
                Trace.gerarTrace(getServletContext(), this.getServletName(), "SECAO:" + secao + " MATR:" + matricula, codPessoa, param, logerro);
                /**
                 * Se a batida for antes do meio dia e não houverem batidas
                 * anteriores, verificar se no dia da semana anterior há
                 * previsão de trabalho diuturno (se for quarta verificar
                 * parâmetro de terça). Nesse caso o sistema deve considerar que
                 * é saída do dia anterior. Essa batida receberá DtCompet do dia
                 * anterior.
                 */
                LocalTime h = LocalTime.parse(hora, DateTimeFormatter.ofPattern("HH:mm"));
                if (h.isBefore(LocalTime.parse("12:00", DateTimeFormatter.ofPattern("HH:mm")))) {
                    DateTimeFormatter dd = DateTimeFormatter.ofPattern("yyyyMMdd");

                    /**
                     * 0 - Domingo 1 1 - Segunda 1 2 - Terça 1 3 - Quarta 1 4 -
                     * Quinta 1 5 - Sexta 1 6 - Sábado 1 7 - Feriado 1 =
                     * 11111111 retorno.put("DiuTurno", "00000000");
                     * retorno.put("Batidas", "0");
                     */
                    Map<String, String> diuturno = funcionDao.getDiuTurnoFuncion(matricula, LocalDate.parse(data, dd).minusDays(1).format(dd), persistencia);

                    /**
                     * 1 - segunda, 2 - terça, 3 - quarta, 4 - quinta, 5 -
                     * sexta, 6 - sábado, 7 - domingo
                     */
                    int ontem;
                    if (LocalDate.parse(dataAtual, dd).getDayOfWeek().getValue() == 7) {
                        ontem = 0;
                    } else {
                        ontem = LocalDate.parse(dataAtual, dd).getDayOfWeek().getValue() - 1;
                    }

                    /**
                     * Verifica se a escala do dia anterior é notura. se o cara
                     * tá batendo hoje e não entrou ontem, o ponto é de hoje.
                     */
                    if (diuturno.get("DiuTurno") != null
                            && diuturno.get("DiuTurno").length() == "00000000".length()
                            && diuturno.get("DiuTurno").charAt(ontem) == '1'
                            && !diuturno.get("Batidas").equals("0")) {
                        dtCompet = LocalDate.parse(data, dd).minusDays(1).format(dd);
                        /**
                         * Busca a quantidade de batidas do dia anterior
                         */
                        quantidade = rHPontoDao.obterQuantidadeBatida(new BigDecimal(matricula), dtCompet, persistencia);
                        /**
                         * Se já foram feitas as quatro batidas do dia, dtCompet
                         * = dataAtual Senão, dtCompet permanece como o dia
                         * anterior.
                         */
                        if (persistencia.getEmpresa().contains("CONFE")) {
                            if (quantidade == 4) { // Estava 4 Alterado para 6 DeltaCorp wm 26/09/2023
                                dtCompet = data;
                            }
                        } else {
                            if (quantidade == 6) { // Estava 4 Alterado para 6 DeltaCorp wm 21/08/2023
                                dtCompet = data;
                            }
                        }
                    }
                }
                quantidade = rHPontoDao.obterQuantidadeBatida(new BigDecimal(matricula), dtCompet, persistencia);

                //Verifica se ja tem 15 minutos
                boolean ultimoRegistros15 = false;
                try {
                    String hora1 = rHPontoDao.obterHoraUltimaBatida(new BigDecimal(matricula), dtCompet, persistencia);
                    if (param.equals("SATPROSECUR")
                            || param.equals("SATGLOVAL")
                            || param.equals("SATPISCINAFACIL")) {
                        ultimoRegistros15 = true;
                    } else if (!"".equals(hora1)) {
                        DateTimeFormatter df = DateTimeFormatter.ofPattern("HH:mm");
                        LocalTime horaSistema = LocalTime.parse(hora, df);
                        LocalTime horaAnterior = LocalTime.parse(hora1, df);

                        Horaminuto horaminuto = new Horaminuto();
                        horaminuto.setHora1(hora1);
                        horaminuto.setHora2(horaSistema.getHour() + "" + horaSistema.getMinute());

//                        int resultado = horaminuto.iDifHora1Hora2min() < 0 ? horaminuto.iDifHora1Hora2min() * (-1) : horaminuto.iDifHora1Hora2min();
                        long resultado = Math.abs(MINUTES.between(horaAnterior, horaSistema));
                        if (resultado > 15) {
                            ultimoRegistros15 = true;
                        }
                    } else {
                        ultimoRegistros15 = true;
                    }
                } catch (Exception e) {
                    System.out.println(e.getMessage());
                    Trace.gerarTrace(getServletContext(), this.getServletName(), e.getMessage(), codPessoa, param, logerro);
                }

                if (ultimoRegistros15) {
                    //Adcionando um à batida de ponto
//                    int quantidade = rHPontoDao.obterQuantidadeBatida(new BigDecimal(matricula), data, persistencia);
                    quantidade = quantidade + 1;

                    if (quantidade <= 6 // Alterado para permitir Batida Acima de 4 em 21/08/2023 Carlos
                            || param.equals("SATPISCINAFACIL")) {
                        BigDecimal qtde = new BigDecimal(String.valueOf(quantidade));
//                        boolean enviado = enviandoImagem(imagem, param, matricula, quantidade, dtCompet);

                        String d;
                        try {
                            d = messages.getData(dtCompet, "yyyyMMdd");
                        } catch (Exception xxx) {
                            d = data;
                        }
                        Trace.gerarTrace(getServletContext(), this.getServletName(), "EnviandoIamagem:" + dtCompet + " MATR:" + matricula, codPessoa, param, logerro);
                        boolean enviado = enviandoImagem(imagem, param, matricula, quantidade, dtCompet, d, hora, operador, latitude, longitude);
                        Trace.gerarTrace(getServletContext(), this.getServletName(), "Enviado:" + dtCompet + " MATR:" + matricula, codPessoa, param, logerro);
                        //Salvando informações da latitude
                        RHPontoGEO rhpgeo = new RHPontoGEO();
                        rhpgeo.setMatr(new BigDecimal(matricula));
                        rhpgeo.setDtCompet(dtCompet);
                        rhpgeo.setLatitude(latitude);
                        rhpgeo.setLongitude(longitude);
                        rhpgeo.setBatida(quantidade);
                        rHPontoGeoDao.salvarBatida(rhpgeo, persistencia);

                        //Salvando detalhes da batida
                        RHPontoDet rhPontoDet = new RHPontoDet();
                        rhPontoDet.setMatr(matricula);
                        rhPontoDet.setDtCompet(dtCompet);
                        rhPontoDet.setBatida(String.valueOf(quantidade));
                        rhPontoDet.setSecao(secao);
                        rHPontoDetDao.inserirRHPontoDet(rhPontoDet, persistencia);

                        //Inserindo informações
                        RHPonto rHPonto = new RHPonto();
                        rHPonto.setMatr(new BigDecimal(matricula));
                        rHPonto.setDtCompet(dtCompet);
                        rHPonto.setBatida(qtde);
                        rHPonto.setTipo(qtde);
                        rHPonto.setHora(hora);
                        rHPonto.setDtBatida(data);
                        rHPonto.setDt_Alter(data);
                        rHPonto.setHr_Alter(hora);
                        rHPonto.setOperador(RecortaAteEspaço("MOB_" + operador, 0, 10));

                        if (enviado) {
                            rHPonto.setNSerie("SATMOB_FOTO_ASS");
                        }
                        
                        //https://saswsatellite.blob.core.windows.net/imagem/SATINVLMT/ponto/20250630/00000282_1.jpg
                        String URL = AzureBlobStorageConstante.URL_BASE + 
                                gerarCaminhoPontoNoAzure(param, data, matricula, 
                                        quantidade);
                        RHPontoImagem novoRHPontoImagem = new RHPontoImagem();
                        novoRHPontoImagem.setMatr(new BigDecimal(matricula));
                        novoRHPontoImagem.setDtCompet(dtCompet);
                        novoRHPontoImagem.setBatida(qtde);
                        novoRHPontoImagem.setURL(URL);

                        rHPontoDao.salvarRegistro(rHPonto, persistencia);
                        rHPontoDao.salvarRegistroImagem(novoRHPontoImagem, 
                                persistencia);

                        retorno += "<resp>1</resp>";

                        OperacoesServDao osd = new OperacoesServDao();
                        if (LoginDao.isSupervisorRondas(codPessoa, persistencia)) {
                            String funcionSecao = funcionDao.getSecao(matricula, persistencia);
                            if (null != funcionSecao && !funcionSecao.equals(secao)) {
                                osd.atualizaQtdePresenca(dtCompet, matricula, persistencia);
                            }
                        } else if (quantidade == 1) {
                            osd.atualizaQtdePresenca(dtCompet, matricula, persistencia);
                        }

                        // Contando quantidade real de batidas na seção.
                        try {
                            retorno += "<batidas>" + rHPontoDao.obterQuantidadeBatidaSecao(new BigDecimal(matricula), dtCompet, secao, persistencia) + "</batidas>";
                        } catch (Exception e) {
                            retorno += "<batidas>0</batidas>";
                        }

                        try {
                            File file = new File(getServletContext().getRealPath("/relatorio.html").replace("%20", " "));
                            FileInputStream fis = new FileInputStream(file);
                            byte[] dados = new byte[(int) file.length()];
                            fis.read(dados);
                            fis.close();
                            String htmlEmail = new String(dados, "UTF-8");
                            String htmlAlt;
                            String htmlAnexo;

                            FuncionPstServ funcion = funcionDao.buscarFuncionSecao(codfil, matricula, secao, persistencia);
                            FiliaisDao filiaisDao = new FiliaisDao();
                            Filiais filial = filiaisDao.getFilial(codfil, persistencia);
                            Trace.gerarTrace(getServletContext(), this.getServletName(), "IniciaEmail:" + funcion.getFuncion().getSecao() + " MATR:" + matricula, codPessoa, param, logerro);
                            String dataHtml;
                            try {
                                dataHtml = messages.getData(data, "yyyyMMdd");
                            } catch (Exception xxx) {
                                dataHtml = data;
                            }

                            String check = quantidade % 2 == 0 ? "ClockOutReport" : "ClockInReport";

                            htmlEmail = htmlEmail.replace("@TituloPagina", messages.getMessage(check) + " - " + matricula.replace(".0", ""));
                            htmlEmail = htmlEmail.replace("@TituloRelatorio", messages.getMessage(check));
                            htmlEmail = htmlEmail.replace("@SubTituloRelatorio", funcion.getPstserv().getLocal());
                            htmlEmail = htmlEmail.replace("@TituloInfo", funcion.getPstserv().getDescContrato());
                            htmlEmail = htmlEmail.replace("@TituloEndereco", filial.getRazaoSocial());
                            htmlEmail = htmlEmail.replace("@TituloTelefone", messages.getTelefone(filial.getFone()));
                            htmlEmail = htmlEmail.replace("@Detalhes", messages.getMessage("Detalhes"));

                            String padrao1 = "						<tr>\n"
                                    + "							<td style=\"width:30.0%;padding:0in 0in 0in 0in\" width=\"30%\">\n"
                                    + "								<p class=\"MsoNormal\">\n"
                                    + "									<b>\n"
                                    + "										<span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif\">\n"
                                    + "											@Padrao:\n"
                                    + "										</span>\n"
                                    + "									</b>\n"
                                    + "									<span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif\">\n"
                                    + "										<u>\n"
                                    + "										</u>\n"
                                    + "										<u>\n"
                                    + "										</u>\n"
                                    + "									</span>\n"
                                    + "								</p>\n"
                                    + "							</td>\n"
                                    + "							<td style=\"padding:0in 0in 0in 0in\">\n"
                                    + "								<p class=\"MsoNormal\">\n"
                                    + "									<span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif\">\n"
                                    + "										<strong>@TextoPadrao</strong>\n"
                                    + "										<u>\n"
                                    + "										</u>\n"
                                    + "										<u>\n"
                                    + "										</u>\n"
                                    + "									</span>\n"
                                    + "								</p>\n"
                                    + "							</td>\n"
                                    + "						</tr>";
                            String padraoFoto
                                    = "                <tr>\n"
                                    + "                    <td colspan=\"2\" style=\"padding:.75pt .75pt .75pt .75pt; text-align: center;\">\n"
                                    + "                        <img id=\"imggirar\" alt=\"Image not found\" src=\"@ImagemRelatorio\" height=\"480\" style=\"height: 480px\" />\n"
                                    + "                    </td>\n"
                                    + "                </tr>\n";
                            String padraoFotoAlt
                                    = "                <tr>\n"
                                    + "                    <td colspan=\"2\" style=\"padding:.75pt .75pt .75pt .75pt; text-align: center;\">\n"
                                    + "                        <img id=\"imggirar\" alt=\"Image not found\" src=\"@ImagemRelatorio\" height=\"480\" style=\"height: 480px\" />\n"
                                    + "                    </td>\n"
                                    + "                </tr>\n"
                                    + "             <tr>\n"
                                    + "                    <td colspan=\"2\" style=\"text-align: center;\">\n"
                                    + "                       <button id=\"back\">@GirarEsquerda</button> \n"
                                    + "                       <button id=\"next\">@GirarDireita</button>\n"
                                    + "                    </td>\n"
                                    + "                </tr>";
                            String padraoMapa = "                <tr>\n"
                                    + "                    <td colspan=\"2\" style=\"padding:.75pt .75pt .75pt .75pt\">\n"
                                    + "                           <p class=\"MsoNormal\">\n"
                                    + "									<b>\n"
                                    + "										<span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif\">\n"
                                    + "                        @Padrao:\n"
                                    + "										</span>\n"
                                    + "									</b>\n"
                                    + "									<span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif\">\n"
                                    + "										<u>\n"
                                    + "										</u>\n"
                                    + "										<u>\n"
                                    + "										</u>\n"
                                    + "									</span>\n"
                                    + "								</p>\n"
                                    + "                    </td>\n"
                                    + "                </tr>\n"
                                    + "                <tr>\n"
                                    + "                    <td colspan=\"2\" style=\"padding:.75pt .75pt .75pt .75pt\">\n"
                                    + "                        <div id=\"mapa\" style=\"height: 360px; width: 100%; text-align: center; vertical-align: middle; line-height: 369px;\">\n"
                                    + "                             @MensagemErroMapa\n"
                                    + "                         </div>\n"
                                    + "                    </td>\n"
                                    + "                </tr>";
                            String padraoMapaEstatico = "                <tr>\n"
                                    + "                    <td colspan=\"2\" style=\"padding:.75pt .75pt .75pt .75pt\">\n"
                                    + "                           <p class=\"MsoNormal\">\n"
                                    + "									<b>\n"
                                    + "										<span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif\">\n"
                                    + "                        @Padrao:\n"
                                    + "										</span>\n"
                                    + "									</b>\n"
                                    + "									<span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif\">\n"
                                    + "										<u>\n"
                                    + "										</u>\n"
                                    + "										<u>\n"
                                    + "										</u>\n"
                                    + "									</span>\n"
                                    + "								</p>\n"
                                    + "                    </td>\n"
                                    + "                </tr>\n"
                                    + "                <tr>\n"
                                    + "                    <td colspan=\"2\" style=\"padding:.75pt .75pt .75pt .75pt; text-align: center;\">\n"
                                    + "                        <img alt=\"Map not found\" src=\"@MapaRelatorio\" height=\"480\" style=\"height: 480px\" />\n"
                                    + "                    </td>\n"
                                    + "                </tr>";

                            StringBuilder relatorioEmail = new StringBuilder();
                            StringBuilder relatorioAlt = new StringBuilder();
                            StringBuilder relatorioAnexo = new StringBuilder();

                            relatorioEmail.append(padrao1.replace("@Padrao", messages.getMessage("Funcionario"))
                                    .replace("@TextoPadrao", funcion.getFuncion().getNome()));
                            relatorioEmail.append(padrao1.replace("@Padrao", messages.getMessage("Matricula"))
                                    .replace("@TextoPadrao", funcion.getFuncion().getMatr().toBigInteger().toString()));
                            relatorioEmail.append(padrao1.replace("@Padrao", messages.getMessage("Data") + " - " + messages.getMessage("Hora"))
                                    .replace("@TextoPadrao", dataHtml + " - " + hora));

                            relatorioAnexo.append(relatorioEmail);
                            relatorioAlt.append(relatorioEmail);

                            // Servidor CWEB 01  
                            String urlEmail = "https://mobile.sasw.com.br:9091/satellite/fotos/" + param + "/ponto/";
                            String urlAlt = "https://mobile.sasw.com.br:9091/satellite/fotos/" + param + "/ponto/";
                            String urlAnexo = "file:\\C:\\xampp\\htdocs\\satellite\\fotos\\" + param + "\\ponto\\";

                            String foto = FuncoesString.RecortaString(dtCompet, 0, 10).replaceAll("-", "") + "/"
                                    + trataMatricula(matricula.replace(".0", "")) + "_" + quantidade + ".jpg";
                            String mapa = FuncoesString.RecortaString(dtCompet, 0, 10).replaceAll("-", "") + "/"
                                    + trataMatricula(matricula.replace(".0", "")) + "_mapa_" + quantidade + ".png";

                            //enviandoImagemMapa(mapaEstatico(latitude, longitude, funcion.getPstserv().getLocal()), param, matricula, quantidade, dtCompet); Desativado em 21/03/2023
                            relatorioEmail.append(padraoFoto.replace("@ImagemRelatorio", urlEmail + foto)
                                    .replace("@ImagemFoto", messages.getMessage("Foto")));
                            relatorioAlt.append(padraoFotoAlt.replace("@ImagemRelatorio", urlAlt + foto)
                                    .replace("@ImagemFoto", messages.getMessage("Foto"))
                                    .replace("@GirarEsquerda", messages.getMessage("GirarEsquerda"))
                                    .replace("@GirarDireita", messages.getMessage("GirarDireita")));
                            relatorioAnexo.append(padraoFoto.replace("@ImagemRelatorio", urlAnexo + foto)
                                    .replace("@ImagemFoto", messages.getMessage("Foto")));

                            //relatorioEmail.append(padraoMapaEstatico.replace("@Padrao", idioma.equals("en") ? "Map" : "Mapa")
                            //        .replace("@MapaRelatorio", mapaEstatico(latitude, longitude, funcion.getPstserv().getLocal()))); Desativado em 21/03/2023
                            relatorioAlt.append(padraoMapa.replace("@Padrao", messages.getMessage("Mapa"))
                                    .replace("@MensagemErroMapa", messages.getMessage("ErroMapa")));
                            //relatorioAnexo.append(padraoMapaEstatico.replace("@Padrao", messages.getMessage("Mapa"))
                            //        .replace("@MapaRelatorio", urlAnexo + mapa)); Desativado em 21/03/2023
                            htmlAlt = htmlEmail.replace("@Relatorio", relatorioAlt.toString());
                            htmlAnexo = htmlEmail.replace("@Relatorio", relatorioAnexo.toString());
                            htmlEmail = htmlEmail.replace("@Relatorio", relatorioEmail.toString());

                            htmlEmail = htmlEmail.replace("@Script", "");
                            //htmlAlt = htmlAlt.replace("@Script", mapa(latitude, longitude, funcion.getPstserv().getLocal()));
                            htmlAlt = htmlAlt.replace("@Script", "");
                            htmlAnexo = htmlAnexo.replace("@Script", "");

                            htmlEmail = htmlEmail.replace("@ImagemLogo", getLogo(empresa, "0"));
                            htmlAlt = htmlAlt.replace("@ImagemLogo", getLogo(empresa, "0"));
                            htmlAnexo = htmlAnexo.replace("@ImagemLogo", getLogoAnexo(empresa, "0"));

                            String anexoHTML = empresa + "_report_" + matricula + "_" + dtCompet + "_" + quantidade + ".html";
                            String anexoPdf = empresa + "_report_" + matricula + "_" + dtCompet + "_" + quantidade + ".pdf";

                            htmlEmail = htmlEmail.replace("@URL", "https://mobile.sasw.com.br:9091/Satmobile/documentos/anexo-email/" + anexoHTML);
                            htmlAlt = htmlAlt.replace("@URL", "");
                            htmlAnexo = htmlAnexo.replace("@URL", "");

                            htmlEmail = htmlEmail.replace("@MensagemUrl", messages.getMessage("MensagemUrl"));
                            htmlAlt = htmlAlt.replace("@MensagemUrl", "");
                            htmlAnexo = htmlAnexo.replace("@MensagemUrl", "");

                            // Aqui verifica se colaborador recebe por email ou whatsapp
                            boolean envPontoEmail = false, envPontoWhp = false;

                            try {
                                // Consultar configurações do colaborador
                                Pessoa pessoaConfig = pessoaDao.getEnvPontoConfiguracoes(codPessoa, persistencia);

                                if (null != pessoaConfig && null != pessoaConfig.getEnvPontoEmail() && pessoaConfig.getEnvPontoEmail().equals("1")) {
                                    envPontoEmail = true;
                                }

                                if (null != pessoaConfig && null != pessoaConfig.getEnvPontoWhp() && pessoaConfig.getEnvPontoWhp().equals("1")) {
                                    envPontoWhp = true;
                                }
                            } catch (Exception eCodigo) {
                                //TracerText += "\n\n" + eCodigo.getMessage();                                
                            }

                            // Aqui grava os dados de processamento
                            RHPontoProcessa rHPontoProcessa = new RHPontoProcessa();
                            rHPontoProcessa.setBatida(qtde.toPlainString());
                            rHPontoProcessa.setDtCompet(dtCompet);
                            rHPontoProcessa.setDt_Alter(getDataAtual("SQL"));
                            rHPontoProcessa.setCodFil(codfil);
                            rHPontoProcessa.setEnderecoHTML("C:/xampp/htdocs/satmobile/documentos/anexo-email/" + empresa + "_report_" + matricula + "_" + dtCompet + "_" + quantidade + ".html");
                            rHPontoProcessa.setEnderecoPDF("C:/xampp/htdocs/satmobile/documentos/anexo-email/" + empresa + "_report_" + matricula + "_" + dtCompet + "_" + quantidade + ".pdf");
                            rHPontoProcessa.setEnviaEmail(envPontoEmail ? "S" : "N");
                            rHPontoProcessa.setEnviaWpp(envPontoWhp ? "S" : "N");
                            rHPontoProcessa.setGerado("N");
                            rHPontoProcessa.setHr_Alter(getDataAtual("HORA"));
                            rHPontoProcessa.setHtmlEmail(htmlEmail);
                            rHPontoProcessa.setHtmlPagina(htmlAlt);
                            rHPontoProcessa.setHtmlPDF(htmlAnexo);
                            rHPontoProcessa.setMatr(matricula);
                            rHPontoProcessa.setOperador("SATMOB");
                            rHPontoProcessa.setParametro(persistencia.getEmpresa());

                            Persistencia satellite = pool.getConexao("SATELLITE");
                            rHPontoDao.salvarRegistroProcessamento(rHPontoProcessa, satellite);

                            satellite.FechaConexao();

                        } catch (Exception e) {
                            Trace.gerarTrace(getServletContext(), this.getServletName(), e.getMessage(), codPessoa, param, logerro);
                            //TracerText += "\n\n" + logerro;
                        }
                    } else {
                        retorno += "<resp>3</resp>";
                    }
                } else {
                    retorno += "<resp>4</resp>";
                }
            } else {
                retorno += "<resp>2</resp>";
            }
            Trace.gerarTrace(getServletContext(), this.getServletName(), retorno, codPessoa, param, logerro);
            //TracerText += "\n\n" + logerro;
            persistencia.FechaConexao();
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");

            //Trace.gerarTrace(getServletContext(), this.getServletName(), TracerText, codPessoa, param, logerro);
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0) + " segundos.\r\n", codPessoa, param, logerro);
        } catch (Exception e) {
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Falha BatidaPonto - " + e.getMessage(), codPessoa, param, logerro);
            out.print("<?xml version=\"1.0\"?><resp>0</resp>");
        }

        out.print(retorno);
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

    private String mapa(String lat, String lon, String localidade) {
        String script = "<script type=\"text/javascript\">\n"
                + "	function initialize() {\n"
                + "\n"
                + "	  var myLatlng = new google.maps.LatLng(@Latitude, @Longitude);\n"
                + "	  var mapOptions = {\n"
                + "		zoom: 17,\n"
                + "		center: myLatlng,\n"
                + "		panControl: false,\n"
                + "		mapTypeControlOptions: {\n"
                + "		  mapTypeIds: [google.maps.MapTypeId.ROADMAP, 'map_style']\n"
                + "		}\n"
                + "	  }\n"
                + "\n"
                + "	  var map = new google.maps.Map(document.getElementById(\"mapa\"), mapOptions);\n"
                + "\n"
                + "	  var image = 'https://cdn1.iconfinder.com/data/icons/gpsmapicons/blue/gpsmapicons01.png';\n"
                + "	  var marcadorPersonalizado = new google.maps.Marker({\n"
                + "		  position: myLatlng,\n"
                + "		  map: map,\n"
                + "		  title: '@Localidade',\n"
                + "		  animation: google.maps.Animation.DROP\n"
                + "	  });\n"
                + "	}"
                + "\n"
                + "	function loadScript() {\n"
                + "	  var script = document.createElement(\"script\");\n"
                + "	  script.type = \"text/javascript\";\n"
                + "	  script.src = \"https://maps.googleapis.com/maps/api/js?key=AIzaSyD6DdGi_hMzj_pw9yAtveEY7GUNlu1oRGI&sensor=true&callback=initialize\";\n"
                + "	  document.body.appendChild(script);\n"
                + "	}\n"
                + "\n"
                + "	window.onload = loadScript;\n"
                + " rotate = 0; \n"
                + "             \n"
                + "            document.addEventListener(\"DOMContentLoaded\", function(){ \n"
                + "                document.getElementById(\"next\").addEventListener(\"click\", function(){ \n"
                + "                 \n"
                + "                    if(rotate== 360){rotate = 0} \n"
                + "                 \n"
                + "                    rotate = rotate + 90; \n"
                + "                     \n"
                + "                    document.getElementById(\"imggirar\").style.transform = \"rotate(\"+rotate+\"deg)\"; \n"
                + "                    document.getElementById(\"trfoto\").style.height   = document.getElementById(\"imggirar\").width+30; \n"
                + "                });\n"
                + "                 \n"
                + "                document.getElementById(\"back\").addEventListener(\"click\", function(){ \n"
                + "                 \n"
                + "                    if(rotate== -360){rotate = 0} \n"
                + "                 \n"
                + "                    rotate = rotate + -90 ;\n"
                + "                     \n"
                + "                    document.getElementById(\"imggirar\").style.transform = \"rotate(\"+rotate+\"deg)\"; \n"
                + "                    document.getElementById(\"trfoto\").style.height   = document.getElementById(\"imggirar\").width+30; \n"
                + "                });     \n"
                + "            });"
                + "</script>";
        return script.replace("@Latitude", lat).replace("@Longitude", lon).replace("@Localidade", localidade);
    }

    private String mapaEstatico(String lat, String lon, String localidade) {
        /* Carlos 21/03/2023
        String url = "https://api.mapbox.com/styles/v1/mapbox/streets-v10/static/pin-m(@Longitude,@Latitude),pin-m(@Longitude,@Latitude)/@Longitude,@Latitude,14/640x480?access_token=pk.eyJ1Ijoic2FzdyIsImEiOiJja3M1YzNoNWQwMTk4MnVwbDhrb3ZhemR3In0.VTTrPrhjrQniM8obQtd3tw";
         */
 /*String url = "https://maps.googleapis.com/maps/api/staticmap?size=640x480&format=PNG"
                + "&markers=color:red|label:@Localidade|@Latitude,@Longitude&key=AIzaSyD6DdGi_hMzj_pw9yAtveEY7GUNlu1oRGI";*/
 /* Carlos 21/03/2023
        return url.replace("@Latitude", lat).replace("@Longitude", lon).replace("@Localidade", localidade).replace(" ", "%20");
         */
        return null;
        /* Carlos 21/03/2023
        String url = "https://api.mapbox.com/styles/v1/mapbox/streets-v10/static/pin-m(@Longitude,@Latitude),pin-m(@Longitude,@Latitude)/@Longitude,@Latitude,14/640x480?access_token=pk.eyJ1Ijoic2FzdyIsImEiOiJja3M1YzNoNWQwMTk4MnVwbDhrb3ZhemR3In0.VTTrPrhjrQniM8obQtd3tw";
         */
 /*String url = "https://maps.googleapis.com/maps/api/staticmap?size=640x480&format=PNG"
                + "&markers=color:red|label:@Localidade|@Latitude,@Longitude&key=AIzaSyD6DdGi_hMzj_pw9yAtveEY7GUNlu1oRGI";*/
 /* Carlos 21/03/2023
        return url.replace("@Latitude", lat).replace("@Longitude", lon).replace("@Localidade", localidade).replace(" ", "%20");
         */
    }

    private boolean enviandoImagem(String imagem, String param, String matricula, int numero, String data) {
        boolean enviado = false;
        String vBase64;
        try {
            //Criando imagem
            byte[] montar = new sun.misc.BASE64Decoder().decodeBuffer(imagem);

            String caminho = "C:/xampp/htdocs/satellite/fotos/" + param + "/ponto/" + data;
            String caminhoweb1 = "S:/fotos/" + param + "/ponto/" + data;
            File diretorio = new File(caminho);
            if (!diretorio.exists()) {
                diretorio.mkdirs();  // cria diretórios caso não estejam criados
            }

            File diretorioweb1 = new File(caminhoweb1);
            if (!diretorioweb1.exists()) {
                diretorioweb1.mkdirs();  // cria diretórios caso não estejam criados
            }

            String nome = caminho + "/" + trataMatricula(matricula.replace(".0", "")) + "_" + numero + ".jpg";
            String nomeweb1 = caminhoweb1 + "/" + trataMatricula(matricula.replace(".0", "")) + "_" + numero + ".jpg";

            FileOutputStream fos = new FileOutputStream(nome);
            FileOutputStream fosweb1 = new FileOutputStream(nomeweb1);
            fos.write(montar);
            fosweb1.write(montar);
            FileDescriptor fd = fos.getFD();
            FileDescriptor fdweb1 = fosweb1.getFD();
            fos.flush();
            fosweb1.flush();
            fd.sync();
            fdweb1.sync();
            fos.close();
            fosweb1.close();

            vBase64 = Base64.getEncoder().encodeToString(montar);
            //Gravar no banco:
            Consulta SQLPdrLog;
            String vSQLLog;
            vSQLLog = "Insert into queueimg(Sequencia, Parametro, Sistema, Endereco, Gravado, Imagem,Operador, Dt_Alter, Hr_Alter)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from QueueImg), \n"
                    + "'" + param + "', \n"
                    + "'SATMOBEW', \n"
                    + "'" + nome + "', \n"
                    + "'S', \n"
                    + "'" + vBase64 + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";
            Trace.gerarTrace(getServletContext(), this.getServletName(), vSQLLog, matricula, param, logerro);
            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATELLITE"));
            SQLPdrLog.insert();
            enviado = true;

        } catch (Exception e) {
            System.out.println(e.getMessage());
            Trace.gerarTrace(getServletContext(), this.getServletName(), e.getMessage(), matricula, param, logerro);
        }
        return enviado;
    }

    private boolean enviandoImagem(String imagem, String param, String matricula, int numero, String data,
            String dtCompet, String hora,
            String usuario,
            String latitude, String longitude) {
        boolean enviado = false;
        String vBase64 = "";
        try {
            //Criando imagem
            byte[] montar = new sun.misc.BASE64Decoder().decodeBuffer(imagem);
            vBase64 = imagem;

            String caminho = "C:/xampp/htdocs/satellite/fotos/" + param + "/ponto/" + data;
            String caminhoweb1 = "S:/fotos/" + param + "/ponto/" + data;
            File diretorio = new File(caminho);
            File diretorioweb1 = new File(caminhoweb1);
            if (!diretorio.exists()) {
                diretorio.mkdirs();  // cria diretórios caso não estejam criados
            }

            if (!diretorioweb1.exists()) {
                diretorioweb1.mkdirs();  // cria diretórios caso não estejam criados
            }

            String nome = caminho + "/" + trataMatricula(matricula.replace(".0", "")) + "_" + numero + ".jpg";
            String nomeweb1 = caminhoweb1 + "/" + trataMatricula(matricula.replace(".0", "")) + "_" + numero + ".jpg";

            FileOutputStream fos = new FileOutputStream(nome);
            FileOutputStream fosweb1 = new FileOutputStream(nomeweb1);
            fos.write(montar);
            fosweb1.write(montar);
            FileDescriptor fd = fos.getFD();
            FileDescriptor fdweb1 = fosweb1.getFD();
            fos.flush();
            fosweb1.flush();
            fd.sync();
            fdweb1.sync();
            fos.close();
            fosweb1.close();

            final BufferedImage image = ImageIO.read(new ByteArrayInputStream(montar));

            Graphics g = image.getGraphics();

            Font font = Font.decode("Times New Roman");
            Graphics2D g1 = (Graphics2D) g;
            g1.setRenderingHint(
                    RenderingHints.KEY_FRACTIONALMETRICS,
                    RenderingHints.VALUE_FRACTIONALMETRICS_ON);
            g1.setRenderingHint(
                    RenderingHints.KEY_TEXT_ANTIALIASING,
                    RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

            String maior = dtCompet + " " + hora;
            if (usuario.length() > maior.length()) {
                maior = usuario;
            }
            if ((latitude + ", " + longitude).length() > maior.length()) {
                maior = latitude + ", " + longitude;
            }

            Rectangle2D r2d = g.getFontMetrics(font).getStringBounds(maior, g);
            font = font.deriveFont((float) (font.getSize2D() * 0.03 * image.getHeight() / r2d.getHeight()));

            float x = font.getSize2D() * 0.1f;
            float y = font.getSize2D();

            Map<TextAttribute, Object> atts = new HashMap<TextAttribute, Object>();
            atts.put(TextAttribute.KERNING, TextAttribute.KERNING_ON);
            font = font.deriveFont(atts);

            TextLayout textLayout = new TextLayout(dtCompet + " " + hora, font, g1.getFontRenderContext());
            g1.setPaint(Color.BLACK);
            textLayout.draw(g1, x + 1.5f, y + 1.5f);

            g1.setPaint(Color.WHITE);
            textLayout.draw(g1, x, y);

            y = y + font.getSize2D();
            textLayout = new TextLayout(usuario, font, g1.getFontRenderContext());
            g1.setPaint(Color.BLACK);
            textLayout.draw(g1, x + 1.5f, y + 1.5f);

            g1.setPaint(Color.WHITE);
            textLayout.draw(g1, x, y);

            y = y + font.getSize2D();
            textLayout = new TextLayout(latitude + ", " + longitude, font, g1.getFontRenderContext());
            g1.setPaint(Color.BLACK);
            textLayout.draw(g1, x + 1.5f, y + 1.5f);

            g1.setPaint(Color.WHITE);
            textLayout.draw(g1, x, y);

            g.dispose();

            ImageIO.write(image, "jpg", new File(nome));
            ImageIO.write(image, "jpg", new File(nomeweb1));

            vBase64 = Base64.getEncoder().encodeToString(montar);
            //Gravar no banco:
            Consulta SQLPdrLog;
            String vSQLLog;
            vSQLLog = "Insert into queueimg(Sequencia, Parametro, Sistema, Endereco, Gravado, Imagem,Operador, Dt_Alter, Hr_Alter)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from QueueImg), \n"
                    + "'" + param + "', \n"
                    + "'SATMOBEW', \n"
                    + "'" + nome + "', \n"
                    + "'S', \n"
                    + "'" + vBase64 + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";

            Trace.gerarTrace(getServletContext(), this.getServletName(), vSQLLog, matricula, param, logerro);

            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATELLITE"));
            SQLPdrLog.insert();
            
            String caminhoAzure = gerarCaminhoPontoNoAzure(param, data, matricula, 
                    numero);
            AzureBlobStorageServico azureBlobStorageServico = new 
                AzureBlobStorageServico(
                        AzureBlobStorageConstante.AZURE_CONEXAO, 
                        AzureBlobStorageConstante.NOME_CONTAINER);            
            String imagemURL = azureBlobStorageServico.salvarImagem(montar, caminhoAzure);
            
            Trace.gerarTrace(getServletContext(), this.getServletName(), 
                    "Imagem gravada: " + imagemURL, matricula, param, logerro);

            enviado = true;
        } catch (Exception e) {
            Trace.gerarTrace(getServletContext(), this.getServletName(), e.getMessage(), matricula, param, logerro);
            System.out.println("erro foto " + e.getMessage());
        }
        return enviado;
    }


    private boolean enviandoImagemMapa(String url, String param, String matricula, int numero, String data) {
        boolean enviado = false;
        try {
            //Criando imagem
            URL u = new URL(url);
            InputStream in = new BufferedInputStream(u.openStream());
            ByteArrayOutputStream o = new ByteArrayOutputStream();
            byte[] buf = new byte[1024];
            int n = 0;
            while (-1 != (n = in.read(buf))) {
                o.write(buf, 0, n);
            }
            o.close();
            in.close();
            byte[] r = o.toByteArray();

            String caminho = "C:/xampp/htdocs/satellite/fotos/" + param + "/ponto/" + data;
            File diretorio = new File(caminho);
            if (!diretorio.exists()) {
                diretorio.mkdirs();  // cria diretórios caso não estejam criados
            }

            String nome = caminho + "/" + trataMatricula(matricula.replace(".0", "")) + "_mapa_" + numero + ".png";

            FileOutputStream fos = new FileOutputStream(nome);
            fos.write(r);
            fos.close();

            enviado = true;
        } catch (Exception e) {
            System.out.println("erro foto " + e.getMessage());
        }
        return enviado;
    }

    //com 8 Digitos
    private String trataMatricula(String matricula) {
        if (matricula.length() == 1) {
            matricula = "0000000" + matricula;
        } else if (matricula.length() == 2) {
            matricula = "000000" + matricula;
        } else if (matricula.length() == 3) {
            matricula = "00000" + matricula;
        } else if (matricula.length() == 4) {
            matricula = "0000" + matricula;
        } else if (matricula.length() == 5) {
            matricula = "000" + matricula;
        } else if (matricula.length() == 6) {
            matricula = "00" + matricula;
        } else if (matricula.length() == 7) {
            matricula = "0" + matricula;
        }
        return matricula;
    }
    
    private String gerarCaminhoPontoNoAzure(String param, String data, 
            String matricula, int numero) {
        String caminhoAzure = param + "/ponto/" + data + "/" + trataMatricula(
                matricula.replace(".0", "")) + "_" + numero + ".jpg";
        return caminhoAzure;
    }
    
}
