/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PreOrderVol;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PreOrderVolDao {

    /**
     * Altera o valor do Lacre em PreOrderVol
     *
     * @param sequencia
     * @param codFil
     * @param ordem
     * @param lacre
     * @param obs
     * @param persistencia
     * @throws Exception
     */
    public void editarLacre(String sequencia, String codFil, String ordem, String lacre, String obs, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE PreOrderVol SET Lacre = ?, Obs = ? WHERE Sequencia = ? AND CodFil = ? AND Ordem = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(lacre);
            consulta.setString(obs);
            consulta.setString(sequencia);
            consulta.setString(codFil);
            consulta.setString(ordem);
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("PreOrderVolDao.editarLacre - " + e.getMessage() + "\r\n"
                    + " UPDATE PreOrderVol SET Lacre = " + lacre + " WHERE Sequencia = " + sequencia + " AND CodFil = " + codFil + " AND Ordem = " + ordem);
        }
    }

    /**
     * Exclui as entradas de PreOrderVol com base em PreOrder
     *
     * @param dtColeta
     * @param codCli
     * @param lotes
     * @param persistencia
     * @throws Exception
     */
    public void excluirPreOrderVol(String dtColeta, String codCli, List<Integer> lotes, Persistencia persistencia) throws Exception {
        try {
            String sql = " DELETE FROM PreOrderVol WHERE sequencia IN (SELECT sequencia FROM PreOrder \n "
                    + " WHERE DtColeta = ? AND CodCli1 = ? AND Lote in (";

            for (Integer lote : lotes) {
                sql += " ?, ";
            }
            sql = sql.substring(0, sql.length() - 2) + " )) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtColeta);
            consulta.setString(codCli);
            for (Integer lote : lotes) {
                consulta.setInt(lote);
            }
            consulta.delete();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("PreOrderVolDao.excluirPreOrderVol - " + e.getMessage() + "\r\n"
                    + " DELETE FROM PreOrderVol WHERE sequencia IN (SELECT sequencia FROM PreOrder WHERE DtColeta = " + dtColeta + " AND CodCli1 = " + codCli + ") ");
        }
    }

    /**
     * Insere uma nova entrada na tabela PreOrderVol
     *
     * @param preOrderVol
     * @param persistencia
     * @throws Exception
     */
    public void inserirPreOrderVol(PreOrderVol preOrderVol, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO PreOrderVol (Sequencia, CodFil, Ordem, Qtde, Lacre, Tipo, Valor) "
                    + " VALUES (?, ?, (SELECT ISNULL(MAX(Ordem),0) + 1 FROM PreOrderVol p WHERE p.Sequencia = Sequencia), ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(preOrderVol.getSequencia());
            consulta.setString(preOrderVol.getCodFil());
            //consulta.setString(preOrderVol.getOrdem());
            consulta.setString(preOrderVol.getQtde());
            consulta.setString(preOrderVol.getLacre());
            consulta.setString(preOrderVol.getTipo());
            consulta.setString(preOrderVol.getValor());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PreOrderVolDao.inserirPreOrderVol - " + e.getMessage());
        }
    }
}
