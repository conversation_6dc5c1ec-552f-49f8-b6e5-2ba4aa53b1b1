/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satmobew;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeansCompostas.Login;
import SasDaos.PstServDao;
import Xml.Xmls;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "BuscarPostoAuto", urlPatterns = {"/BuscarPostoAuto"})
public class BuscarPostoAuto extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;

    @Override
    public void init() {
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/plain");
        PrintWriter out = response.getWriter();

        String param = request.getParameter("param");
        String matr = request.getParameter("matricula");

        try {
            long tStart = 0, tEnd;
            tStart = System.currentTimeMillis();

            String xml = "<?xml version=\"1.0\"?>";
            Persistencia persistencia = pool.getConexao(param);

            if (null != persistencia) {
                PstServDao pstServDao = new PstServDao();
                Login login = pstServDao.listarConfiguracaoMatr(matr, persistencia);

                if (null != login && null != login.getFiliais() && null != login.getFiliais().getDescricao() && !login.getFiliais().getDescricao().equals("")) {
                    xml += "<resp>mobileconfig_1</resp>";

                    StringBuilder aux = new StringBuilder();
                    aux.append(Xmls.tag("filialcodigo", login.getFiliais().getCodFil().toBigInteger()))
                            .append(Xmls.tag("filialdescricao", login.getFiliais().getDescricao()))
                            .append(Xmls.tag("filialendereco", login.getFiliais().getEndereco()))
                            .append(Xmls.tag("filialnome", login.getFiliais().getRazaoSocial()))
                            .append(Xmls.tag("postolocal", login.getPstServ().getLocal()))
                            .append(Xmls.tag("postosecao", login.getPstServ().getSecao()))
                            .append(Xmls.tag("postocontrato", login.getPstServ().getContrato()))
                            .append(Xmls.tag("posto", login.getPstServ().getPosto()))
                            .append(Xmls.tag("postotipo", login.getPstServ().getTipoPosto()))
                            .append(Xmls.tag("clientecodigo", login.getCliente().getCodigo()))
                            .append(Xmls.tag("clientenred", login.getCliente().getNRed()))
                            .append(Xmls.tag("clientenome", login.getCliente().getNome()))
                            .append(Xmls.tag("clienteende", login.getCliente().getEnde()))
                            .append(Xmls.tag("clientebairro", login.getCliente().getBairro()))
                            .append(Xmls.tag("clientecidade", login.getCliente().getCidade()))
                            .append(Xmls.tag("clienteestado", login.getCliente().getEstado()))
                            .append(Xmls.tag("clientecep", login.getCliente().getCEP()))
                            .append(Xmls.tag("clientelat", login.getCliente().getLatitude()))
                            .append(Xmls.tag("clientelon", login.getCliente().getLongitude()));

                    xml += Xmls.tag("data", aux.toString());

                    out.print(xml);
                }
                else{
                    out.print("<?xml version=\"1.0\"?><resp>mobileconfig_2</resp>");    
                }
                persistencia.FechaConexao();
            } else {
                out.print("<?xml version=\"1.0\"?><resp>mobileconfig_3</resp>");
            }
        } catch (Exception e) {
            out.print("<?xml version=\"1.0\"?><resp>0</resp><erro>" + e.getMessage() + "</erro>");
        } finally {
            Trace.Erros(getServletContext(), request, logerro);
            out.close();
        }

    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
