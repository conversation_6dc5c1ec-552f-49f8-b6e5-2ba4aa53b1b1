package br.com.sasw.satmobew;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Inspecoes;
import SasBeans.ParametMobile;
import SasBeans.PessoaTrajeto;
import SasBeans.PstDepen;
import SasBeans.PstServ;
import SasBeans.RHPonto;
import SasBeans.Rh_Horas;
import SasBeans.Rondas;
import SasBeans.TipoPosto;
import SasBeansCompostas.Login;
import SasDaos.CtrOpervDao;
import SasDaos.FuncionDao;
import SasDaos.InspecoesDao;
import SasDaos.LoginDao;
import SasDaos.ParametMobileDao;
import SasDaos.PessoaTrajetoDao;
import SasDaos.PstDepenDao;
import SasDaos.PstDepenRondaDao;
import SasDaos.PstServDao;
import SasDaos.RHPontoDao;
import SasDaos.Rh_HorasDao;
import SasDaos.RondasDao;
import SasDaos.TiposPostosDao;
import Xml.Xmls;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.GPS;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "ValidarUsuario", urlPatterns = {"/ValidarUsuario"})
public class ValidarUsuario extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;

    @Override
    public void init() {
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();

        String param = request.getParameter("param");
        String codpessoa = request.getParameter("codpessoa");
        String nomepessoa = request.getParameter("nomepessoa");
        String codfil = request.getParameter("codfil");
        String latitudeMobile = request.getParameter("latitude");
        String longitudeMobile = request.getParameter("longitude");
        try {

            logerro = new ArquivoLog();

            String xml, plataforma, versao;

            String senha = request.getParameter("senha");
            String dataAtual = request.getParameter("dataAtual");
            String horaAtual = request.getParameter("horaAtual");
            String aplicativo = request.getParameter("aplicativo");
            String secao = request.getParameter("secao");

            if (null == dataAtual || dataAtual.equals("")) {
                dataAtual = DataAtual.getDataAtual("SQL");
            }
            if (null == horaAtual || horaAtual.equals("")) {
                horaAtual = DataAtual.getDataAtual("HORA");
            }

            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codpessoa, param, logerro);
            tStart = System.currentTimeMillis();

            Persistencia persistenceLocal = pool.getConexao(param);
            Persistencia persistenceGeral = pool.getConexao("SATELLITE");

            if (null != persistenceLocal) {

                ParametMobileDao pmdao = new ParametMobileDao();

                //****************************************************************************************************
                //identificação de versão válida
                plataforma = request.getParameter("plataforma");
                ParametMobile lpm = pmdao.listaVersao(plataforma, aplicativo, persistenceGeral);

                if (!param.contains("CONFEDERAL")) {
                    // Se NÃO for CONFEDERAL pode utilizar de acordo com versão permitida
                    versao = Xmls.tag("versao", lpm.getVersao()) + Xmls.tag("versaoaceita", lpm.getVersaoAceita());
                } else {
                    // Se for CONFEDERAL e versão = a 2.2.11, obriga a atualizar
                    if (lpm.getVersao().equals("2.2.11")) {
                        versao = Xmls.tag("versao", lpm.getVersao()) + Xmls.tag("versaoaceita", lpm.getVersao());
                    } else {
                        versao = Xmls.tag("versao", lpm.getVersao()) + Xmls.tag("versaoaceita", lpm.getVersaoAceita());
                    }
                }

                // Verificando se o login contém seis dígitos e se o primeiro é 8 para verificar se é funcionário ou prestador de serviços.
                boolean prestador = codpessoa.length() == 6 && codpessoa.charAt(0) == '8';

                List<Login> login = LoginDao.LoginMobilePonto(codpessoa, nomepessoa, prestador, codfil, secao, dataAtual, persistenceLocal);

                if (login.isEmpty() || (!senha.equals(login.get(0).getPessoa().getPWWeb()) && !senha.equals(login.get(0).getPessoa().getPWPortal())) && (persistenceLocal.getEmpresa().equals("SATINTERFORT") || persistenceLocal.getEmpresa().equals("SATSERVITE"))) {
                    if (persistenceLocal.getEmpresa().equals("SATSERVITE")) {
                        persistenceLocal = pool.getConexao("SATINTERFORT");
                    } else if (persistenceLocal.getEmpresa().equals("SATINTERFORT")) {
                        persistenceLocal = pool.getConexao("SATSERVITE");
                    } else {
                        persistenceLocal = pool.getConexao("SATSERVITE");
                    }
                    
                    login = LoginDao.LoginMobilePonto(codpessoa, nomepessoa, prestador, codfil, secao, dataAtual, persistenceLocal);
                }

                if (login.isEmpty()) {
                    Trace.gerarTrace(getServletContext(), this.getServletName(), "Codigo incorreto", codpessoa, param, logerro);
                    xml = "<?xml version=\"1.0\"?><resp>mobilelogin_1</resp>"; //codigo incorreto
                } else if (!senha.equals(login.get(0).getPessoa().getPWWeb()) && !senha.equals(login.get(0).getPessoa().getPWPortal())) {
                    Trace.gerarTrace(getServletContext(), this.getServletName(), "Senha incorreta - " + codpessoa, codpessoa, param, logerro);
                    xml = "<?xml version=\"1.0\"?><resp>mobilelogin_3</resp>"; //senha incorreta
                } else {
                    Trace.gerarTrace(getServletContext(), this.getServletName(), "Carregado com sucesso - " + codpessoa, codpessoa, param, logerro);
                    xml = "<?xml version=\"1.0\"?><resp>1</resp>";
                    xml += versao;
                    xml += "<matricula>" + login.get(0).getFuncion().getMatr() + "</matricula>";
                    xml += "<codfil>" + login.get(0).getFuncion().getCodFil().toString() + "</codfil>";
                    xml += "<nome_guer>" + login.get(0).getFuncion().getNome_Guer() + "</nome_guer>";
                    xml += "<codigo>" + login.get(0).getPessoa().getCodigo() + "</codigo>";
                    xml += "<cercaelet>" + login.get(0).getCliente().getCercaElet()+ "</cercaelet>";
                    xml += "<secaoref>" + login.get(0).getFuncion().getSecaoRef() + "</secaoref>";
                    xml += "<contratoref>" + login.get(0).getFuncion().getContratoRef() + "</contratoref>";
                    xml += "<localref>" + login.get(0).getFuncion().getLocalRef() + "</localref>";
                    xml += "<sistemas>";
                    for (Login login1 : login) {
                        xml += "<sistema>" + login1.getSaspwac().getSistema().toBigInteger().toString() + "</sistema>";
                        xml += "<inclusao>" + login1.getSaspwac().getInclusao() + "</inclusao>";
                        xml += "<alteracao>" + login1.getSaspwac().getAlteracao() + "</alteracao>";
                        xml += "<exclusao>" + login1.getSaspwac().getExclusao() + "</exclusao>";
                    }
                    xml += "</sistemas>";
                    xml += "<pwweb>" + login.get(0).getPessoa().getPWWeb() + "</pwweb>";

                    StringBuilder permissoes;
                    xml += Xmls.tag("qtdpermissoes", login.size() + "");
                    for (Login login1 : login) {
                        permissoes = new StringBuilder();
                        permissoes.append(Xmls.tag("sistema", login1.getSaspwac().getSistema().toBigInteger().toString()));
                        xml += Xmls.tag("permissoes", permissoes.toString());
                    }

                    Login ultimaMovimentacaoEW = LoginDao.ultimaMovimentacaoEW(login.get(0).getPessoa().getCodigo().toPlainString(),
                            login.get(0).getFuncion().getMatr().toPlainString(), persistenceLocal);
                    xml += "<data>" + ultimaMovimentacaoEW.getData() + "</data>";
                    xml += "<hora>" + ultimaMovimentacaoEW.getHora() + "</hora>";

                    if (prestador) {
                        xml += "<prestador>1</prestador>";
                        xml += "<cargo>" + login.get(0).getPessoa().getFuncao() + "</cargo>";
                        xml += "<escala></escala>";
                        xml += "<sexo>M</sexo>";
                        RHPontoDao rhPontoDao = new RHPontoDao();
                        RHPonto rhPonto = rhPontoDao.obterProximoCheckInPrestador(login.get(0).getFuncion().getMatr(), dataAtual, persistenceLocal);
                        xml += "<batidas>" + (rhPonto.getBatida() == null ? "0" : rhPonto.getBatida().toBigInteger().toString()) + "</batidas>";

                        PessoaTrajetoDao pessoaTrajetoDao = new PessoaTrajetoDao();

                        List<PessoaTrajeto> pessoaTrajetos = pessoaTrajetoDao.obterRotaPosicoes(login.get(0).getPessoa().getCodigo().toPlainString(), dataAtual, persistenceLocal);
//                        List<PessoaTrajeto> pessoaTrajetos = pessoaTrajetoDao.obterRota(login.get(0).getPessoa().getCodigo().toPlainString(), dataAtual, persistenceLocal);

                        xml += "<qtdPessoaTrajeto>" + pessoaTrajetos.size() + "</qtdPessoaTrajeto>";
                        for (PessoaTrajeto pessoaTrajeto : pessoaTrajetos) {
                            xml += "<PessoaTrajeto>";
                            xml += Xmls.tag("CodPessoa", pessoaTrajeto.getCodPessoa());
                            xml += Xmls.tag("DtCompet", pessoaTrajeto.getDtCompet());
                            xml += Xmls.tag("Descricao", pessoaTrajeto.getDescricao());
                            xml += Xmls.tag("Endereco", pessoaTrajeto.getEndereco());
                            xml += Xmls.tag("Ordem", pessoaTrajeto.getOrdem());
                            xml += Xmls.tag("Tipo", pessoaTrajeto.getTipo());
                            xml += Xmls.tag("HrCheg", pessoaTrajeto.getHrCheg());
                            xml += Xmls.tag("HrSaida", pessoaTrajeto.getHrSaida());
                            xml += Xmls.tag("Tempo", pessoaTrajeto.getTempo());
                            xml += Xmls.tag("CodContato", pessoaTrajeto.getCodContato());
                            xml += Xmls.tag("CodCli", pessoaTrajeto.getCodCli());
                            xml += Xmls.tag("Secao", pessoaTrajeto.getSecao());
                            xml += Xmls.tag("CodFil", pessoaTrajeto.getCodFil());
                            xml += Xmls.tag("Operador", pessoaTrajeto.getOperador());
                            xml += Xmls.tag("Dt_Alter", pessoaTrajeto.getDt_Alter());
                            xml += Xmls.tag("Hr_Alter", pessoaTrajeto.getHr_Alter());
                            xml += Xmls.tag("Latitude", pessoaTrajeto.getLatitude());
                            xml += Xmls.tag("Longitude", pessoaTrajeto.getLongitude());
                            xml += "</PessoaTrajeto>";
                        }

                    //} else if (login.get(0).getFuncion().getCodFil().compareTo(new BigDecimal(codfil)) > 0) {
                    //    Trace.gerarTrace(getServletContext(), this.getServletName(), "Filial cliente diferente de filial funcion", codpessoa, param, logerro);
                    //    xml = "<?xml version=\"1.0\"?><resp>mobilelogin_4</resp>";
                    } else {
                        xml += "<prestador>0</prestador>";
                        xml += "<cargo>" + login.get(0).getFuncion().getCargo() + "</cargo>";
                        String dtCompet = dataAtual;
                        FuncionDao funcionDao = new FuncionDao();
                        RHPontoDao rHPontoDAO = new RHPontoDao();
                        DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");
                        DateTimeFormatter HHmm = DateTimeFormatter.ofPattern("HH:mm");
                        try {
                            int quantidade;

                            /**
                             * Se a batida for antes do meio dia e não houverem
                             * batidas anteriores, verificar se no dia da semana
                             * anterior há previsão de trabalho diuturno (se for
                             * quarta verificar parâmetro de terça). Nesse caso
                             * o sistema deve considerar que é saída do dia
                             * anterior. Essa batida receberá DtCompet do dia
                             * anterior.
                             */
                            LocalTime h = LocalTime.parse(horaAtual, HHmm);
                            if (h.isBefore(LocalTime.parse("12:00", HHmm))) {
                                /**
                                 * 0 - Domingo 1 1 - Segunda 1 2 - Terça 1 3 -
                                 * Quarta 1 4 - Quinta 1 5 - Sexta 1 6 - Sábado
                                 * 1 7 - Feriado 1 = 11111111
                                 */
                                String diuturno = funcionDao.getDiuTurnoFuncion(login.get(0).getFuncion().getMatr().toPlainString(), persistenceLocal);

                                /**
                                 * 1 - segunda, 2 - terça, 3 - quarta, 4 -
                                 * quinta, 5 - sexta, 6 - sábado, 7 - domingo
                                 */
                                int ontem;
                                if (LocalDate.parse(dataAtual, yyyyMMdd).getDayOfWeek().getValue() == 7) {
                                    ontem = 0;
                                } else {
                                    ontem = LocalDate.parse(dataAtual, yyyyMMdd).getDayOfWeek().getValue() - 1;
                                }

                                /**
                                 * Verifica se a escala do dia anterior é
                                 * notura.
                                 */
                                if (diuturno.charAt(ontem) == '1') {
                                    dtCompet = LocalDate.parse(dataAtual, yyyyMMdd).minusDays(1).format(yyyyMMdd);
                                    /**
                                     * Busca a quantidade de batidas do dia
                                     * anterior
                                     */
                                    quantidade = rHPontoDAO.obterQuantidadeBatida(login.get(0).getFuncion().getMatr(), dtCompet, persistenceLocal);
                                    /**
                                     * Se já foram feitas as quatro batidas do
                                     * dia, dtCompet = dataAtual Senão, dtCompet
                                     * permanece como o dia anterior.
                                     */
                                    if (quantidade == 4) {
                                        dtCompet = dataAtual;
                                    }
                                }
                            }
                        } catch (Exception e) {
                        }

                        List<Login> loginEW;
                        if (dtCompet.equals(dataAtual)) {
                            loginEW = login;
                        } else {
                            loginEW = LoginDao.LoginMobilePonto(codpessoa, nomepessoa, prestador, codfil, secao, dtCompet, persistenceLocal);
                        }

                        PstServDao pstServDao = new PstServDao();
                        if (!loginEW.get(0).getFuncion().getSecao().equals(secao)) {
                            try {
                                CtrOpervDao ctrOperVDao = new CtrOpervDao();
                                TiposPostosDao tipoPostoDao = new TiposPostosDao();
                                if (ctrOperVDao.existeCtrOperV(dtCompet, loginEW.get(0).getFuncion().getCodFil().toString(), secao,
                                        loginEW.get(0).getFuncion().getMatr().toPlainString(), persistenceLocal)) {
                                    ctrOperVDao.deleteCtrOperV(loginEW.get(0).getFuncion().getNome_Guer(),
                                            dataAtual, horaAtual, dtCompet, loginEW.get(0).getFuncion().getCodFil().toString(),
                                            loginEW.get(0).getFuncion().getMatr().toPlainString(), persistenceLocal);
                                }

                                LocalTime h = LocalTime.parse(horaAtual, HHmm);
                                String periodo = (h.isAfter(LocalTime.parse("07:00", HHmm))
                                        && h.isBefore(LocalTime.parse("20:00", HHmm))) ? "D" : "N";

                                String[] horaMinDiu = null, horaMinNot = null;
                                BigDecimal heHoraDiu = BigDecimal.ZERO, heHoraNot = BigDecimal.ZERO;
                                LocalTime horaFim = LocalTime.parse(horaAtual, HHmm);
                                TipoPosto tipoPosto = tipoPostoDao.getTipoPosto(secao, loginEW.get(0).getFuncion().getCodFil().toString(),
                                        persistenceLocal);
                                /**
                                 * 1 - segunda, 2 - terça, 3 - quarta, 4 -
                                 * quinta, 5 - sexta, 6 - sábado, 7 - domingo
                                 */
                                switch (LocalDate.parse(dataAtual, yyyyMMdd).getDayOfWeek().getValue()) {
                                    case 1:
                                        heHoraDiu = new BigDecimal(tipoPosto.getChdiu2());
                                        heHoraNot = new BigDecimal(tipoPosto.getChnot2());
                                        horaMinDiu = tipoPosto.getChdiu2().split("\\.");
                                        horaMinNot = tipoPosto.getChnot2().split("\\.");
                                        break;
                                    case 2:
                                        heHoraDiu = new BigDecimal(tipoPosto.getChdiu3());
                                        heHoraNot = new BigDecimal(tipoPosto.getChnot3());
                                        horaMinDiu = tipoPosto.getChdiu3().split("\\.");
                                        horaMinNot = tipoPosto.getChnot3().split("\\.");
                                        break;
                                    case 3:
                                        heHoraDiu = new BigDecimal(tipoPosto.getChdiu4());
                                        heHoraNot = new BigDecimal(tipoPosto.getChnot4());
                                        horaMinDiu = tipoPosto.getChdiu4().split("\\.");
                                        horaMinNot = tipoPosto.getChnot4().split("\\.");
                                        break;
                                    case 4:
                                        heHoraDiu = new BigDecimal(tipoPosto.getChdiu5());
                                        heHoraNot = new BigDecimal(tipoPosto.getChnot5());
                                        horaMinDiu = tipoPosto.getChdiu5().split("\\.");
                                        horaMinNot = tipoPosto.getChnot5().split("\\.");
                                        break;
                                    case 5:
                                        heHoraDiu = new BigDecimal(tipoPosto.getChdiu6());
                                        heHoraNot = new BigDecimal(tipoPosto.getChnot6());
                                        horaMinDiu = tipoPosto.getChdiu6().split("\\.");
                                        horaMinNot = tipoPosto.getChnot6().split("\\.");
                                        break;
                                    case 6:
                                        heHoraDiu = new BigDecimal(tipoPosto.getChdiu7());
                                        heHoraNot = new BigDecimal(tipoPosto.getChnot7());
                                        horaMinDiu = tipoPosto.getChdiu7().split("\\.");
                                        horaMinNot = tipoPosto.getChnot7().split("\\.");
                                        break;
                                    case 7:
                                        heHoraDiu = new BigDecimal(tipoPosto.getChdiu1());
                                        heHoraNot = new BigDecimal(tipoPosto.getChnot1());
                                        horaMinDiu = tipoPosto.getChdiu1().split("\\.");
                                        horaMinNot = tipoPosto.getChnot1().split("\\.");
                                        break;
                                }
                                horaFim = horaFim.plusHours(Long.valueOf(horaMinDiu[0]));
                                horaFim = horaFim.plusMinutes(Long.valueOf(horaMinDiu[1]));

                                horaFim = horaFim.plusHours(Long.valueOf(horaMinNot[0]));
                                horaFim = horaFim.plusMinutes(Long.valueOf(horaMinNot[1]));

                                ctrOperVDao.insereCtrOperV(loginEW.get(0).getFuncion().getNome_Guer(), dataAtual, horaAtual, dtCompet,
                                        loginEW.get(0).getFuncion().getCodFil().toString(), loginEW.get(0).getFuncion().getMatr().toPlainString(),
                                        periodo, ctrOperVDao.obterNumeroIncremento(loginEW.get(0).getFuncion().getCodFil(), persistenceLocal).toPlainString(),
                                        secao, horaFim.format(HHmm), heHoraDiu, heHoraNot, persistenceLocal);

                                Rh_HorasDao rh_HorasDao = new Rh_HorasDao();
                                Rh_Horas rh_Horas = new Rh_Horas();
                                rh_Horas.setMatr(loginEW.get(0).getFuncion().getMatr().toPlainString());
                                rh_Horas.setCodFil(loginEW.get(0).getFuncion().getCodFil().toString());
                                rh_Horas.setData(dtCompet);
                                rh_Horas.setHsDiurnas(heHoraDiu.toPlainString());
                                rh_Horas.setHsNoturnas(heHoraNot.toPlainString());
                                rh_Horas.setSecao(secao);
                                rh_Horas.setOperador(FuncoesString.RecortaAteEspaço(loginEW.get(0).getFuncion().getNome_Guer(), 0, 10));
                                rh_Horas.setHr_Alter(horaAtual);
                                rh_Horas.setDt_Alter(dataAtual);
                                if (rh_HorasDao.existeRh_Horas(rh_Horas, persistenceLocal)) {
                                    rh_HorasDao.atualizarRh_Horas(secao, dtCompet, loginEW.get(0).getFuncion().getCodFil().toString(),
                                            loginEW.get(0).getFuncion().getMatr().toPlainString(), dataAtual, horaAtual,
                                            loginEW.get(0).getFuncion().getNome_Guer(), persistenceLocal);
                                } else {
                                    rh_HorasDao.incluir(rh_Horas, persistenceLocal);
                                }
                            } catch (Exception e) {
                                Trace.gerarTrace(getServletContext(), this.getServletName(), "Falha - " + e.getMessage(), codpessoa, param, logerro);
                            }
                        }

                        try {
                            if (LoginDao.isSupervisorRondas(loginEW.get(0).getPessoa().getCodigo().toPlainString(), persistenceLocal)) {
                                xml += "<supervisor>" + funcionDao.getSecao(loginEW.get(0).getFuncion().getMatr().toPlainString(), persistenceLocal) + "</supervisor>";
                                List<PstServ> postos;
                                if (persistenceLocal.getEmpresa().contains("CONFE")){
                                   postos = pstServDao
                                                                                .listarPostosSatMobEWMatr(loginEW.get(0).getFuncion().getMatr().toString(), persistenceLocal);
                                        //.listarPostosSatMobEW(persistenceLocal);
                                   xml += Xmls.tag("qtdpostos", postos.size() + "");
                                }else{
                                   postos = pstServDao
                                                      //                          .listarPostosSatMobEW(loginEW.get(0).getFuncion().getCodFil().toString(), persistenceLocal);
                                        .listarPostosSatMobEW(persistenceLocal);
                                   xml += Xmls.tag("qtdpostos", postos.size() + "");
                                    
                                }
                                StringBuilder p;
                                for (PstServ posto : postos) {
                                    /**
                                     * Calcula distância entre o aparelho e os
                                     * clientes
                                     */
                                    BigDecimal dist = GPS.distanciaCoordenadas(posto.getLatitude(), posto.getLongitude(), latitudeMobile, longitudeMobile);
                                    posto.setDist(dist);
                                }
                                orderPostos(postos);
                                for (PstServ posto : postos) {
                                    p = new StringBuilder();
                                    p.append(Xmls.tag("secao", posto.getSecao()))
                                            .append(Xmls.tag("codfil", posto.getCodFil().toBigInteger().toString()))
                                            .append(Xmls.tag("endereco", posto.getEndereco()))
                                            .append(Xmls.tag("descricao", posto.getDescContrato()))
                                            .append(Xmls.tag("local", posto.getLocal()))
                                            .append(Xmls.tag("distancia", posto.getDist()))
                                            .append(Xmls.tag("latitude", posto.getLatitude()))
                                            .append(Xmls.tag("longitude", posto.getLongitude()));
                                    xml += Xmls.tag("postos", p.toString());
                                }

                                try {
                                    PstServ postosCheckIn = pstServDao.validarCheckIns(loginEW.get(0).getFuncion().getMatr().toPlainString(),
                                            dataAtual, horaAtual, persistenceLocal);
                                    if (postosCheckIn != null) {
                                        p = new StringBuilder();
                                        p.append(Xmls.tag("secao", postosCheckIn.getSecao()))
                                                .append(Xmls.tag("codfil", postosCheckIn.getCodFil().toBigInteger().toString()))
                                                .append(Xmls.tag("endereco", postosCheckIn.getEndereco()))
                                                .append(Xmls.tag("descricao", postosCheckIn.getDescContrato()))
                                                .append(Xmls.tag("local", postosCheckIn.getLocal()))
                                                .append(Xmls.tag("latitude", postosCheckIn.getLatitude()))
                                                .append(Xmls.tag("longitude", postosCheckIn.getLongitude()));
                                        xml += Xmls.tag("postoSelecionado", p.toString());
                                        // Contando quantidade de batidas pela seção pendente de check out .
                                        try {
                                            xml += "<batidas>" + rHPontoDAO.obterQuantidadeBatidaSecao(login.get(0).getFuncion().getMatr(), dtCompet, postosCheckIn.getSecao(), persistenceLocal) + "</batidas>";
                                        } catch (Exception e) {
                                            xml += "<batidas>0</batidas>";
                                        }
                                    } else {
                                        // Contando quantidade de batidas pela seção do aparelho caso não tenha seção pendente de check out.
                                        try {
                                            xml += "<batidas>" + rHPontoDAO.obterQuantidadeBatidaSecao(login.get(0).getFuncion().getMatr(), dtCompet, secao, persistenceLocal) + "</batidas>";
                                        } catch (Exception e) {
                                            xml += "<batidas>0</batidas>";
                                        }
                                    }
                                } catch (Exception e2) {

                                }

                                InspecoesDao inspecoesDao = new InspecoesDao();
                                List<Inspecoes> inspecoes;
                                
                                inspecoes = inspecoesDao.getAllInspecoes(persistenceLocal);
                                
                                xml += Xmls.tag("qtdInspecoes", inspecoes.size());
                                for (Inspecoes inspecao : inspecoes) {
                                    p = new StringBuilder();
                                    p.append(Xmls.tag("codigo", inspecao.getCodigo())).append(Xmls.tag("descricao", inspecao.getDescricao()));
                                    xml += Xmls.tag("inspecoes", p.toString());
                                }

                            } else {
                                xml += "<supervisor>0</supervisor>";
                                // Contando quantidade de batidas pela seção do aparelho.
                                try {
                                    xml += "<batidas>" + rHPontoDAO.obterQuantidadeBatidaSecao(login.get(0).getFuncion().getMatr(), dtCompet, secao, persistenceLocal) + "</batidas>";
                                } catch (Exception e) {
                                    xml += "<batidas>0</batidas>";
                                }
                            }
                        } catch (Exception e) {
                        }

                        PstDepenDao pstDepenDao = new PstDepenDao();
                        PstDepenRondaDao pstDepenRondaDao = new PstDepenRondaDao();
                        List<PstDepen> ds = pstDepenDao.getPstDepen(secao, loginEW.get(0).getFuncion().getCodFil().toString(), persistenceLocal);
                        StringBuilder depen, depens = new StringBuilder();
                        for (PstDepen pstDepen : ds) {
                            depen = new StringBuilder();
                            depen.append(Xmls.tag("codigo", pstDepen.getCodigo()));
                            depen.append(Xmls.tag("secao", pstDepen.getSecao()));
                            depen.append(Xmls.tag("codfil", pstDepen.getCodFil()));
                            depen.append(Xmls.tag("descricao", pstDepen.getDescricao()));
                            depen.append(Xmls.tag("latitude", pstDepen.getLatitude()));
                            depen.append(Xmls.tag("longitude", pstDepen.getLongitude()));
                            /**
                             * Formata os horários em um inteiro que representa
                             * a string binária da lista de horários
                             */
                            depen.append(Xmls.tag("horarios", pstDepenRondaDao.getHorarios(pstDepen.getCodigo(), pstDepen.getSecao(), pstDepen.getCodFil(), persistenceLocal)));
                            depens.append(Xmls.tag("pstdepens", depen.toString()));
                        }
                        xml += depens.toString();

                        /**
                         * Obtem rondas pendentes
                         */
                        Calendar c = Calendar.getInstance();
                        c.setTime(new SimpleDateFormat("yyyyMMdd").parse(dtCompet));
                        String tipo = "";
                        switch (c.get(Calendar.DAY_OF_WEEK)) {
                            case 1:
                                tipo = "Dom";
                                break;
                            case 2:
                            case 3:
                            case 4:
                            case 5:
                            case 6:
                                tipo = "SegSex";
                                break;
                            case 7:
                                tipo = "Sab";
                                break;
                        }

                        RondasDao rondasDao = new RondasDao();
                        /**
                         * Busca rondas pendentes (rondas da hora atual que
                         * ainda não foram feitas)
                         */
                        List<Rondas> pendentes = rondasDao.getRondasPendentes(dtCompet, secao,
                                tipo, loginEW.get(0).getFuncion().getCodFil().toString(), horaAtual.split(":")[0], persistenceLocal);
                        StringBuilder sbPendente, sbPendentes = new StringBuilder();
                        if (pendentes.isEmpty()) {
                            xml += "<rondas></rondas>";
                        } else {
                            for (Rondas ronda : pendentes) {
                                sbPendente = new StringBuilder();
                                sbPendente.append(Xmls.tag("sequencia", (null == ronda.getSequencia() ? "" : ronda.getSequencia())));
                                sbPendente.append(Xmls.tag("codDepen", ronda.getCodDepen()));
                                sbPendente.append(Xmls.tag("horaPrevista", ronda.getHoraPrevista()));
                                sbPendentes.append(Xmls.tag("rondas", sbPendente.toString()));
                            }
                            xml += sbPendentes.toString();
                        }
                        xml += "<sexo>" + loginEW.get(0).getFuncion().getSexo() + "</sexo>";
                        xml += "<secao>" + secao + "</secao>";
                        xml += "<escala>" + loginEW.get(0).getFuncion().getEscala() + "</escala>";
                        xml += "<descricao>" + loginEW.get(0).getSaspw().getDescricao() + "</descricao>";
                        xml += "<local>" + loginEW.get(0).getCliente().getNRed() + "</local>";
                        xml += "<endereco>" + loginEW.get(0).getCliente().getEnde() + "</endereco>";
                        xml += "<latitude>" + loginEW.get(0).getCliente().getLatitude() + "</latitude>";
                        xml += "<longitude>" + loginEW.get(0).getCliente().getLongitude() + "</longitude>";
//                        xml += "<data>" + loginEW.get(0).getRhPonto().getDtBatida()+ "</data>"; xxx
//                        xml += "<hora>" + loginEW.get(0).getRhPonto().getHora()+ "</hora>";
                    }
                }

                //define saida por texto
                response.setContentType("text/plain");
                PrintWriter resp = response.getWriter();
                //escreve a resposta no buffer de saida
                resp.print(xml);

                Trace.gerarTrace(getServletContext(), this.getServletName(), xml, codpessoa, param, logerro);

                persistenceLocal.FechaConexao();
                persistenceGeral.FechaConexao();

            } else {
                PrintWriter resp = response.getWriter();
                resp.print("<?xml version=\"1.0\"?><resp>mobilelogin_6</resp>");
            }

            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0) + " segundos.\r\n", codpessoa, param, logerro);
        } catch (Exception e) {
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Falha - " + e.getMessage(), codpessoa, param, logerro);
            //define saida por texto
            response.setContentType("text/plain");
            PrintWriter resp = response.getWriter();
            //escreve a resposta no buffer de saida
            resp.print("<?xml version=\"1.0\"?><resp>0</resp><erro>" + e.getMessage() + "</erro>");
        } finally {
            Trace.Erros(getServletContext(), request, logerro);
            out.close();
        }
    }

    private static void orderPostos(List<PstServ> postos) {
        Collections.sort(postos, (Object o1, Object o2) -> {
            int sComp;
            try {
                BigDecimal dist1 = ((PstServ) o1).getDist();
                BigDecimal dist2 = ((PstServ) o2).getDist();
                sComp = dist1.compareTo(dist2);
            } catch (Exception e) {
                sComp = -1;
            }
            return sComp;
        });
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

}
