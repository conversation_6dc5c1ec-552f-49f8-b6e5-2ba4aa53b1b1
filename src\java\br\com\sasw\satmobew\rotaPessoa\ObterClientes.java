/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satmobew.rotaPessoa;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeansCompostas.ClientesContatos;
import SasBeansCompostas.ClientesContatos.Postos;
import SasDaos.ClientesDao;
import static SasLibrary.ValidarUsuario.ValidaUsuario;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import Xml.Xmls;
import br.com.sasw.satmobew.*;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.List;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "ObterClientes", urlPatterns = {"/rotaPessoa/ObterClientes"})
public class ObterClientes extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;
    
    @Override
    public void init(){
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

     /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
    
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/plain");
        PrintWriter resp = response.getWriter();
        logerro = new ArquivoLog();
        
        String senha = request.getParameter("senha");
        String param = request.getParameter("param");
        String codPessoa = request.getParameter("codpessoa");
        
        String retorno = "<?xml version=\"1.0\"?>";

        try {
            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codPessoa,  param, logerro);
            tStart = System.currentTimeMillis();
            Persistencia persistencia = pool.getConexao(param);
            if (ValidaUsuario(codPessoa, senha, persistencia)) {
                String latitudeMobile = request.getParameter("latitude");
                String longitudeMobile = request.getParameter("longitude");
    //            String busca = request.getParameter("busca");

                ClientesDao clientesDao = new ClientesDao();
                List<ClientesContatos> clientes = clientesDao.listaClientesPessoaTrajeto(latitudeMobile, longitudeMobile, "8", persistencia);

                StringBuilder clis = new StringBuilder(), aux1, aux2;
                clis.append(Xmls.tag("qtdClientesContatos", clientes.size()));
                for(ClientesContatos cli : clientes){
                    aux1 = new StringBuilder();
                    aux1.append(Xmls.tag("CodFil", cli.getCodFil()))
                        .append(Xmls.tag("Descricao", cli.getDescricao()))
                        .append(Xmls.tag("CodCont", cli.getCodCont().replace(".0", "")))
                        .append(Xmls.tag("CodCli", cli.getCodCli()))
                        .append(Xmls.tag("NRed", cli.getNRed()))
                        .append(Xmls.tag("Cidade", cli.getCidade()))
                        .append(Xmls.tag("UF", cli.getUF()))
                        .append(Xmls.tag("Endereco", cli.getEndereco()))
                        .append(Xmls.tag("Distancia", cli.getDistancia()));
                    // Sempre vai existir pelo menos um elemento na lista de postos.
                    // Se a seção do primeiro elemento for diferente de string vazia, então existe ao menos um posto válido.
                    if(!cli.getPostos().get(0).getSecao().equals("")){
                        aux2 = new StringBuilder();
                        for(Postos posto : cli.getPostos()){
                            aux2.append(Xmls.tag("Posto", Xmls.tag("Secao", posto.getSecao())+Xmls.tag("Local", posto.getLocal())));
                        }
                        aux1.append(Xmls.tag("qtdPostos", cli.getPostos().size()));
                        aux1.append(Xmls.tag("Postos", aux2.toString()));
                    } else {
                        aux1.append(Xmls.tag("qtdPostos", "0"));
                    }
                    clis.append(Xmls.tag("ClientesContatos", aux1.toString()));
                }
                retorno += "<resp>1</resp>";
                retorno += Xmls.tag("data", clis.toString());
            } else {
                retorno += "<resp>2</resp>";
            }
            resp.print(retorno);
            persistencia.FechaConexao();
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0)+" segundos.\r\n", codPessoa, param, logerro); 
        }catch(Exception e){
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Falha ObterClientes - " + e.getMessage(), codPessoa, param, logerro);
            resp.print("<?xml version=\"1.0\"?><resp>0</resp>");
        }
    }
    
    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
