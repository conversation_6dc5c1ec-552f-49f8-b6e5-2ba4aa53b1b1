/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satmobew.inspecaov2;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Clientes;
import SasBeans.EmailsEnviar;
import SasBeans.EmailsEnviarAnexo;
import SasBeans.Filiais;
import SasBeans.Inspecoes;
import SasBeans.PstInspecao;
import SasDaos.ClientesDao;
import SasDaos.EmailsEnviarAnexoDao;
import SasDaos.EmailsEnviarDao;
import SasDaos.FiliaisDao;
import SasDaos.InspecoesDao;
import SasDaos.PstInspecaoDao;
import static SasLibrary.ValidarUsuario.ValidaUsuarioPonto;
import Xml.Xmls;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.LerArquivo;
import static br.com.sasw.pacotesuteis.utilidades.Logos.getLogo;
import static br.com.sasw.pacotesuteis.utilidades.Logos.getLogoAnexo;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import br.com.sasw.satmobew.MultipartMap;
import br.com.sasw.satmobew.ValidarUsuario;
import br.com.sasw.satmobew.mensagem.Messages;
import br.com.sasw.satmobew.relatorio.RealizarRelatorios;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfSignatureAppearance;
import com.itextpdf.text.pdf.PdfStamper;
import com.itextpdf.text.pdf.security.BouncyCastleDigest;
import com.itextpdf.text.pdf.security.DigestAlgorithms;
import com.itextpdf.text.pdf.security.ExternalDigest;
import com.itextpdf.text.pdf.security.ExternalSignature;
import com.itextpdf.text.pdf.security.MakeSignature;
import com.itextpdf.text.pdf.security.PrivateKeySignature;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.font.TextAttribute;
import java.awt.font.TextLayout;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileFilter;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.Security;
import java.security.cert.Certificate;
import java.security.cert.X509Certificate;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.imageio.ImageIO;
import javax.servlet.ServletException;
import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.io.FileUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.w3c.dom.Document;
import org.w3c.tidy.Tidy;
import org.xhtmlrenderer.pdf.ITextRenderer;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "RealizarInspecaoV2", urlPatterns = {"/inspecaov2/RealizarInspecao"})
@MultipartConfig(location = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobEWTemp", maxFileSize = 50485760L) // 50MB.
public class RealizarInspecao extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;

    @Override
    public void init() {
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws java.io.UnsupportedEncodingException
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws UnsupportedEncodingException, IOException {
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        logerro = new ArquivoLog();
        String codPessoa = "SATMOBEW";
        String param = "SATMOBEW";
        try {
            long tStart = 0, tEnd;
            MultipartMap map = new MultipartMap(request, this);

            codPessoa = map.getParameter("codpessoa");
            String senha = map.getParameter("senha");
            String operador = map.getParameter("operador");
            param = map.getParameter("param");
            String empresa = param;

            String arquivos = map.getParameter("arquivos");
            String perguntas = map.getParameter("perguntas");
            String respostas = map.getParameter("respostas");
            String sequencias = map.getParameter("sequencias");
            String matriculas = map.getParameter("matriculas");
            String codInspecao = map.getParameter("inspecao");
            String veiculos = map.getParameter("veiculos");
            String secao = map.getParameter("secao");
            String codfil = map.getParameter("codfil");
            String latitude = map.getParameter("latitude");
            String longitude = map.getParameter("longitude");
            String SEPARADOR = map.getParameter("separador");

            if (null == SEPARADOR || SEPARADOR.equals("")) {
                SEPARADOR = ";";
            }

            String dataAtual = map.getParameter("dataAtual");
            String horaAtual = map.getParameter("horaAtual");
            String idioma = map.getParameter("idioma");
            Messages messages = new Messages();
            messages.setIdioma(idioma);

            if (null == dataAtual || dataAtual.equals("")) {
                dataAtual = DataAtual.getDataAtual("SQL");
            }
            if (null == horaAtual || horaAtual.equals("")) {
                horaAtual = DataAtual.getDataAtual("HORA");
            }

            String retorno = "<?xml version=\"1.0\"?>";

            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codPessoa, param, logerro);
            tStart = System.currentTimeMillis();

            Persistencia persistencia = pool.getConexao(param);
            empresa = persistencia.getEmpresa().equals("SATSASEX") ? "SASW" : persistencia.getEmpresa().replace("SAT", "");

            Trace.gerarTrace(getServletContext(), this.getServletName(), "Validando usuário", codPessoa, param, logerro);
            if (ValidaUsuarioPonto(codPessoa, senha, persistencia)) {
                Trace.gerarTrace(getServletContext(), this.getServletName(), "Usuário validado.", codPessoa, param, logerro);

                List<PstInspecao> pstInspecoes = new ArrayList<>();

                String[] perguntasArray = perguntas.split(SEPARADOR);
                String[] respostasArray = respostas.split(SEPARADOR);
                String[] sequenciasArray = sequencias.split(SEPARADOR);
                String[] matriculasArray = matriculas.split(SEPARADOR);
                String[] veiculosArray = veiculos.split(SEPARADOR);
                String[] arquivosArray = arquivos != null ? arquivos.split(";") : new String[]{};

                if (perguntasArray.length != respostasArray.length
                        || sequenciasArray.length != matriculasArray.length
                        || perguntasArray.length != veiculosArray.length) {

                    Trace.gerarTrace(getServletContext(), this.getServletName(), "Falha envio.", codPessoa, param, logerro);
                    retorno += "<resp>0</resp>" + Xmls.tag("erro", "Falha envio");
                } else {
                    PstInspecaoDao pstInspecaoDao = new PstInspecaoDao();

                    Trace.gerarTrace(getServletContext(), this.getServletName(), "Montando as inspeções vindas do SatMobEW", codPessoa, param, logerro);
                    // Montando as inspeções vindas do SatMobEW
                    PstInspecao inspecao;
                    for (int i = 0; i < perguntasArray.length; i++) {
                        inspecao = new PstInspecao();
                        inspecao.setSecao(secao);
                        inspecao.setCodfil(codfil);
                        inspecao.setData(dataAtual);
                        inspecao.setCodInspecao(codInspecao);
                        inspecao.setSequencia(sequenciasArray[i]);
                        inspecao.setPergunta(perguntasArray[i].equals("null") ? "" : perguntasArray[i].replace("\n", "\\N"));
                        inspecao.setResposta(respostasArray[i].equals("null") ? "" : respostasArray[i].replace("\n", "\\N"));
                        inspecao.setMatr(matriculasArray[i].equals("null") ? "" : matriculasArray[i]);
                        inspecao.setVeiculo(veiculosArray[i].equals("null") ? "" : veiculosArray[i]);
                        inspecao.setCaminhoImagem("");
                        inspecao.setCaminhoVideo("");
                        inspecao.setCaminhoAudio("");
                        inspecao.setLatitude(latitude);
                        inspecao.setLongitude(longitude);

                        inspecao.setDt_Alter(dataAtual);
                        inspecao.setHr_Alter(horaAtual);
                        inspecao.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
                        inspecao.setCodOperador(codPessoa);

//                        inspecao = (PstInspecao) Fun  coesString.removeAcentoObjeto(inspecao);
                        if (inspecao.getResposta().equals("")) {
                            for (String arquivo : arquivosArray) {
                                if (arquivo.split("_")[0].equals(inspecao.getSequencia())) {
                                    pstInspecoes.add(inspecao);
                                    break;
                                }
                            }
                        } else {
                            pstInspecoes.add(inspecao);
                        }
                    }

                    // Inserindo as inspeções no banco
                    Trace.gerarTrace(getServletContext(), this.getServletName(), "Inserindo as inspeções no banco", codPessoa, param, logerro);
                    PstInspecao pstInspecao;
                    String codigo = null;
                    for (int j = 0; j < pstInspecoes.size(); j++) {
                        pstInspecao = pstInspecoes.get(j);
                        // Tenta inserir uma inspeção 20 em caso de falhas
                        boolean falha = true;
                        for (int i = 0; i < 20 && falha; i++) {
                            try {

                                String novaSequencia = pstInspecaoDao.obterSequencia(pstInspecao, persistencia);
                                // Caso não consiga retornar uma nova sequencia, gera exceção pra tentar novamente
                                if (null == novaSequencia) {
                                    throw new Exception();
                                }

                                if (j == 0) {
                                    codigo = pstInspecaoDao.inserirInspecaoCodigo(novaSequencia, pstInspecao, persistencia);
                                    if (codigo == null) {
                                        throw new Exception();
                                    }
                                } else {
                                    if (codigo == null) {
                                        throw new Exception();
                                    }
                                    pstInspecao.setCodigo(codigo);
                                    // Caso o insert não seja bem sucedido, gera exceção pra tentar novamente
                                    if (!pstInspecaoDao.inserirInspecao(novaSequencia, pstInspecao, persistencia)) {
                                        throw new Exception();
                                    }
                                    if (j == pstInspecoes.size() - 1) {
                                        if (pstInspecao.getCodInspecao().replace(".0", "").equals("4") && param.equals("SPM")) {
                                            pstInspecaoDao.inserirResumo(codfil, dataAtual, codPessoa, latitude, longitude, persistencia);
                                        }
                                    }
                                }

                                // salva a nova sequencia para poder tratar o nome dos arquivos depois
                                pstInspecao.setSequencia(pstInspecao.getSequencia() + SEPARADOR + novaSequencia.replace(".0", ""));
                                falha = false;
                            } catch (Exception e) {
                                Trace.gerarTrace(getServletContext(), this.getServletName(), e.getMessage(), codPessoa, param, logerro);
                                falha = true;
                            }
                        }
                    }

                    // Salva os arquivos e os camminhos
                    Trace.gerarTrace(getServletContext(), this.getServletName(), "Salvando os arquivos e os camminhos", codPessoa, param, logerro);
                    String url;
                    File file;
                    byte[] bFile;
                    FileInputStream fileInputStream;
                    FileOutputStream fileOuputStream;
                    String[] seqs;
                    for (String arquivo : arquivosArray) {
                        // se arquivo conter a palavra thumbnail, não salva, apenas 
                        if (arquivo.contains("thumbnail_")) {
                            arquivo = arquivo.replace("thumbnail_", "");
                        } else {
                            for (PstInspecao p : pstInspecoes) {
                                seqs = p.getSequencia().split(SEPARADOR);
                                // seqs.length == 2: p.sequencia contém a sequência temporária do EW e a sequência gerada no servidor
                                // arquivo.split("_")[0]: nome do arquivo sem a ordem e extensão, que é justamente a sequência temporária
                                // seqs[0]: sequência temporária
                                if (seqs.length == 2 && arquivo.split("_")[0].equals(seqs[0])) {
                                    url = "https://mobile.sasw.com.br:9091/satellite/fotos/" + param + "/inspecao/" + p.getData().replace("-", "")
                                            + "/" + p.getSecao() + "/" + seqs[1].replace(".0", "") + "/"
                                            + arquivo.replace(arquivo.split("_")[0], seqs[1].replace(".0", ""));
                                    file = map.getFile(arquivo);
                                    bFile = new byte[(int) file.length()];

                                    //read file into bytes[]
                                    fileInputStream = new FileInputStream(file);
                                    fileInputStream.read(bFile);

                                    File diretorio = new File("C:/xampp/htdocs/satellite/fotos/" + param + "/inspecao/" + p.getData().replace("-", "")
                                            + "/" + p.getSecao() + "/" + seqs[1].replace(".0", "") + "/");
                                    if (!diretorio.exists()) {
                                        diretorio.mkdirs();  // cria diretórios caso não estejam criados
                                    }

                                    fileOuputStream = new FileOutputStream("C:/xampp/htdocs/satellite/fotos/" + param + "/inspecao/"
                                            + p.getData().replace("-", "") + "/" + p.getSecao() + "/" + seqs[1].replace(".0", "")
                                            + "/" + arquivo.replace(arquivo.split("_")[0], seqs[1].replace(".0", "")));

                                    fileOuputStream.write(bFile);
                                    fileInputStream.close();
                                    fileOuputStream.close();
                                    file.delete();

                                    if (arquivo.contains(".3gp")) {
                                        p.setCaminhoAudio(p.getCaminhoAudio() + url + ";");
                                    } else if (arquivo.contains(".mp4")) {
                                        p.setCaminhoVideo(p.getCaminhoVideo() + url + ";");
                                    } else {
                                        p.setCaminhoImagem(p.getCaminhoImagem() + url + ";");

                                        try {
                                            final BufferedImage image = ImageIO.read(new ByteArrayInputStream(bFile));

                                            Graphics g = image.getGraphics();

                                            Font font = Font.decode("Times New Roman");
                                            Graphics2D g1 = (Graphics2D) g;
                                            g1.setRenderingHint(
                                                    RenderingHints.KEY_FRACTIONALMETRICS,
                                                    RenderingHints.VALUE_FRACTIONALMETRICS_ON);
                                            g1.setRenderingHint(
                                                    RenderingHints.KEY_TEXT_ANTIALIASING,
                                                    RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

                                            String maior = dataAtual + " " + horaAtual;
                                            if (operador.length() > maior.length()) {
                                                maior = operador;
                                            }
                                            if ((latitude + ", " + longitude).length() > maior.length()) {
                                                maior = latitude + ", " + longitude;
                                            }

                                            Rectangle2D r2d = g.getFontMetrics(font).getStringBounds(maior, g);
                                            font = font.deriveFont((float) (font.getSize2D() * 0.03 * image.getHeight() / r2d.getHeight()));

                                            float x = font.getSize2D() * 0.1f;
                                            float y = font.getSize2D();

                                            Map<TextAttribute, Object> atts = new HashMap<TextAttribute, Object>();
                                            atts.put(TextAttribute.KERNING, TextAttribute.KERNING_ON);
                                            font = font.deriveFont(atts);

                                            TextLayout textLayout = new TextLayout(dataAtual + " " + horaAtual, font, g1.getFontRenderContext());
                                            g1.setPaint(Color.BLACK);
                                            textLayout.draw(g1, x + 1.5f, y + 1.5f);

                                            g1.setPaint(Color.WHITE);
                                            textLayout.draw(g1, x, y);

                                            y = y + font.getSize2D();
                                            textLayout = new TextLayout(operador, font, g1.getFontRenderContext());
                                            g1.setPaint(Color.BLACK);
                                            textLayout.draw(g1, x + 1.5f, y + 1.5f);

                                            g1.setPaint(Color.WHITE);
                                            textLayout.draw(g1, x, y);

                                            y = y + font.getSize2D();
                                            textLayout = new TextLayout(latitude + ", " + longitude, font, g1.getFontRenderContext());
                                            g1.setPaint(Color.BLACK);
                                            textLayout.draw(g1, x + 1.5f, y + 1.5f);

                                            g1.setPaint(Color.WHITE);
                                            textLayout.draw(g1, x, y);

                                            g.dispose();

                                            ImageIO.write(image, "jpg", new File("C:/xampp/htdocs/satellite/fotos/" + param + "/inspecao/"
                                                    + p.getData().replace("-", "") + "/" + p.getSecao() + "/" + seqs[1].replace(".0", "")
                                                    + "/" + arquivo.replace(arquivo.split("_")[0], seqs[1].replace(".0", ""))));
                                        } catch (Exception eFoto) {

                                        }
                                    }
                                    break;
                                }
                            }
                        }
                    }

                    /**
                     * Atualiza os caminhos de video e imagem e já monta a
                     * resposta de retorno
                     */
                    Trace.gerarTrace(getServletContext(), this.getServletName(), "Atualizando os caminhos de video e imagem e já montando a\n"
                            + "resposta de retorno", codPessoa, param, logerro);
                    StringBuilder resposta = new StringBuilder();
                    for (PstInspecao p : pstInspecoes) {
                        seqs = p.getSequencia().split(SEPARADOR);
                        if (seqs.length == 2
                                && ((null != p.getCaminhoImagem() && !p.getCaminhoImagem().equals(""))
                                || (null != p.getCaminhoVideo() && !p.getCaminhoVideo().equals("")))) {
                            pstInspecaoDao.atualizarCaminhos(seqs[1], p, persistencia);
                        }
                        if (seqs.length == 2
                                && null != p.getCaminhoAudio()
                                && !p.getCaminhoAudio().equals("")) {
                            pstInspecaoDao.atualizarCaminhoAudio(seqs[1], p, persistencia);
                        }

                        resposta.append(Xmls.tag("inspecao", p.getSequencia()));
                    }

                    retorno += "<resp>1</resp>" + Xmls.tag("inspecoes", resposta.toString());

                    if (!pstInspecoes.isEmpty()) {
                        try {
                            InspecoesDao inspecoesDao = new InspecoesDao();
                            Inspecoes inspecoes = inspecoesDao.getInspecoes(codInspecao, persistencia);

                            ClientesDao clientesDao = new ClientesDao();
                            Clientes cliente = clientesDao.clientePosto(secao, codfil, persistencia);

                            FiliaisDao filiaisDao = new FiliaisDao();
                            Filiais filial = filiaisDao.getFilial(codfil, persistencia);

                            String htmlEmail = LerArquivo.obterConteudo(RealizarInspecao.class.getResourceAsStream("/relatorios/relatorioEW.html"));
                            String htmlAlt;
                            String htmlAnexo;

                            htmlEmail = htmlEmail.replace("@TituloPagina", messages.getMessage("RelatorioInspecao"));

                            htmlEmail = htmlEmail.replace("@TituloRelatorio", messages.getMessage("RelatorioInspecao").toUpperCase()
                                    + " - " + inspecoes.getDescricao());
                            htmlEmail = htmlEmail.replace("@SubTituloRelatorio", cliente.getContato());
                            htmlEmail = htmlEmail.replace("@TituloInfo", "");
                            htmlEmail = htmlEmail.replace("@TituloEndereco", cliente.getEnde() + ", " + cliente.getBairro() + ". "
                                    + cliente.getCidade() + "/" + cliente.getEstado());
                            htmlEmail = htmlEmail.replace("@TituloTelefone", messages.getTelefone(filial.getFone()));
                            htmlEmail = htmlEmail.replace("@Detalhes", messages.getMessage("Detalhes"));

                            String padrao1coluna = LerArquivo.obterConteudo(RealizarInspecao.class.getResourceAsStream("/relatorios/EW_uma_coluna_style.html"));
                            String padrao2colunas = LerArquivo.obterConteudo(RealizarInspecao.class.getResourceAsStream("/relatorios/EW_duas_colunas.html"));
                            String padraoImagem = LerArquivo.obterConteudo(RealizarInspecao.class.getResourceAsStream("/relatorios/EW_imagem.html"));
                            String padraoVideo = LerArquivo.obterConteudo(RealizarRelatorios.class.getResourceAsStream("/relatorios/EW_video.html"));
                            String padraoImagemBotoes = LerArquivo.obterConteudo(RealizarInspecao.class.getResourceAsStream("/relatorios/EW_imagem_botoes.html"));

                            StringBuilder relatorioEmail = new StringBuilder();
                            StringBuilder relatorioAlt = new StringBuilder();
                            StringBuilder relatorioAnexo = new StringBuilder();

                            relatorioEmail.append(padrao2colunas.replace("@Padrao", messages.getMessage("Posto"))
                                    .replace("@TextoPadrao", cliente.getContato()));
                            relatorioEmail.append(padrao2colunas.replace("@Padrao:", "")
                                    .replace("@TextoPadrao", cliente.getNRed() + " - " + cliente.getNome()));
                            relatorioEmail.append(padrao2colunas.replace("@Padrao:", "")
                                    .replace("@TextoPadrao", cliente.getEnde() + " - " + cliente.getBairro()));
                            relatorioEmail.append(padrao2colunas.replace("@Padrao:", "")
                                    .replace("@TextoPadrao", cliente.getCidade()
                                            + "/" + cliente.getEstado() + " - " + messages.getCEP(cliente.getCEP())));

                            String dataHtml;
                            try {
                                LocalDate datetime = LocalDate.parse(dataAtual, DateTimeFormatter.ofPattern("yyyyMMdd"));
                                dataHtml = datetime.format(idioma.equals("en") ? DateTimeFormatter.ofPattern("MM/dd/yyyy") : DateTimeFormatter.ofPattern("dd/MM/yyyy"));
                            } catch (Exception xxx) {
                                dataHtml = dataAtual;
                            }

                            relatorioEmail.append(padrao2colunas.replace("@Padrao", messages.getMessage("Data")).replace("@TextoPadrao", dataHtml));
                            relatorioEmail.append(padrao1coluna.replace("@Padrao:", "<br><br>"));

                            relatorioAlt.append(relatorioEmail);
                            relatorioAnexo.append(relatorioEmail);

                            String urlEmail = "https://mobile.sasw.com.br:9091/satellite/fotos/" + param + "/";
//                            String urlAnexo = "http://localhost:9080/satellite/fotos/" + param + "/";
                            String urlAnexo = "file:\\C:\\xampp\\htdocs\\satellite\\fotos\\" + param + "\\";

                            String[] fotosArray;
                            StringBuilder fotos;
                            List<String> idsFotos = new ArrayList<>();

                            relatorioEmail.append(padrao1coluna.replace("@Padrao:", messages.getMessage("Inspecao")).replace("@Style", "text-align: center; font-weight: bold; background: #aaaaaa; font-size:10.0pt; padding:.75pt .75pt .75pt .75pt"));
                            relatorioAlt.append(padrao1coluna.replace("@Padrao:", messages.getMessage("Inspecao")).replace("@Style", "text-align: center; font-weight: bold; background: #aaaaaa; font-size:10.0pt; padding:.75pt .75pt .75pt .75pt"));
                            relatorioAnexo.append(padrao1coluna.replace("@Padrao:", messages.getMessage("Inspecao")).replace("@Style", "text-align: center; font-weight: bold; background: #aaaaaa; font-size:10.0pt; padding:.75pt .75pt .75pt .75pt"));

                            for (PstInspecao p : pstInspecoes) {
                                relatorioEmail.append(padrao1coluna.replace("@Padrao:", p.getPergunta()).replace("@Style", "text-align: center; background: #0808081a; font-weight: bold; font-size:10.0pt; padding:.75pt .75pt .75pt .75pt"));
                                relatorioEmail.append(padrao1coluna.replace("@Padrao:", p.getResposta().replace("\\N", "<br>")).replace("@Style", "text-align: center; padding:.75pt .75pt .75pt .75pt"));

                                relatorioAlt.append(padrao1coluna.replace("@Padrao:", p.getPergunta()).replace("@Style", "text-align: center; background: #0808081a; font-weight: bold; font-size:10.0pt; padding:.75pt .75pt .75pt .75pt"));
                                relatorioAlt.append(padrao1coluna.replace("@Padrao:", p.getResposta().replace("\\N", "<br>")).replace("@Style", "text-align: center; padding:.75pt .75pt .75pt .75pt"));

                                relatorioAnexo.append(padrao1coluna.replace("@Padrao:", p.getPergunta()).replace("@Style", "text-align: center; background: #0808081a; font-weight: bold; font-size:10.0pt; padding:.75pt .75pt .75pt .75pt"));
                                relatorioAnexo.append(padrao1coluna.replace("@Padrao:", p.getResposta().replace("\\N", "<br>")).replace("@Style", "text-align: center; padding:.75pt .75pt .75pt .75pt"));

                                fotos = new StringBuilder().append(p.getCaminhoImagem()).append(p.getCaminhoVideo());
                                fotosArray = fotos.toString().contains(";") ? fotos.toString().split(";") : null;

                                if (null != fotosArray) {
                                    relatorioEmail.append(padrao1coluna.replace("@Padrao",
                                            fotosArray.length == 1 ? messages.getMessage("Foto") : messages.getMessage("Fotos")).replace("@Style", "font-weight: bold; padding:.75pt .75pt .75pt .75pt"));
                                    relatorioAlt.append(padrao1coluna.replace("@Padrao",
                                            fotosArray.length == 1 ? messages.getMessage("Foto") : messages.getMessage("Fotos")).replace("@Style", "font-weight: bold; padding:.75pt .75pt .75pt .75pt"));
                                    relatorioAnexo.append(padrao1coluna.replace("@Padrao",
                                            fotosArray.length == 1 ? messages.getMessage("Foto") : messages.getMessage("Fotos")).replace("@Style", "font-weight: bold; padding:.75pt .75pt .75pt .75pt"));

                                    for (int i = 1; i <= fotosArray.length; i++) {
                                        if (fotosArray[i - 1].contains(".mp4")) {
                                            relatorioEmail.append(padraoVideo.replace("@VideoId", "imggirar_" + i)
                                                    .replace("@VideoMensagem", messages.getMessage("MensagemVideo"))
                                                    .replace("@VideoTipo", "video/mp4")
                                                    .replace("@VideoRelatorio", fotosArray[i - 1]));
                                            relatorioAlt.append(padraoVideo.replace("@VideoId", "imggirar_" + i)
                                                    .replace("@VideoMensagem", messages.getMessage("MensagemVideo"))
                                                    .replace("@VideoTipo", "video/mp4")
                                                    .replace("@VideoRelatorio", fotosArray[i - 1]));
                                            relatorioAnexo.append(padraoVideo.replace("@VideoId", "imggirar_" + i)
                                                    .replace("@VideoMensagem", messages.getMessage("MensagemVideo"))
                                                    .replace("@VideoTipo", "video/mp4")
                                                    .replace("@VideoRelatorio", fotosArray[i - 1].replace(urlEmail, urlAnexo)));
                                        } else {
                                            idsFotos.add("imggirar_" + p.getSequencia().split(SEPARADOR)[1] + "_" + i);
                                            relatorioEmail.append(padraoImagem.replace("@ImagemId", "imggirar_" + p.getSequencia().split(SEPARADOR)[1] + "_" + i)
                                                    .replace("@ImagemMensagem", messages.getMessage("ImagemNaoEncontrada"))
                                                    .replace("@ImagemRelatorio", fotosArray[i - 1]));
                                            relatorioAnexo.append(padraoImagem.replace("@ImagemId", "imggirar_" + p.getSequencia().split(SEPARADOR)[1] + "_" + i)
                                                    .replace("@ImagemMensagem", messages.getMessage("ImagemNaoEncontrada"))
                                                    .replace("@ImagemRelatorio", fotosArray[i - 1].replace(urlEmail, urlAnexo)));
                                            relatorioAlt.append(padraoImagemBotoes.replace("@ImagemId", "imggirar_" + p.getSequencia().split(SEPARADOR)[1] + "_" + i)
                                                    .replace("@ImagemRelatorio", fotosArray[i - 1])
                                                    .replace("@ImagemMensagem", messages.getMessage("ImagemNaoEncontrada"))
                                                    .replace("@ImagemBack", "back_imggirar_" + p.getSequencia().split(SEPARADOR)[1] + "_" + i)
                                                    .replace("@ImagemNext", "next_imggirar_" + p.getSequencia().split(SEPARADOR)[1] + "_" + i)
                                                    .replace("@GirarEsquerda", messages.getMessage("Girar Esquerda"))
                                                    .replace("@GirarDireita", messages.getMessage("Girar Direita")));
                                        }
                                    }
                                }

                                relatorioEmail.append(padrao1coluna.replace("@Padrao:", "<br>"));
                                relatorioAlt.append(padrao1coluna.replace("@Padrao:", "<br>"));
                                relatorioAnexo.append(padrao1coluna.replace("@Padrao:", "<br>"));
                            }

                            htmlAlt = htmlEmail.replace("@Relatorio", relatorioAlt.toString());
                            htmlAnexo = htmlEmail.replace("@Relatorio", relatorioAnexo.toString());
                            htmlEmail = htmlEmail.replace("@Relatorio", relatorioEmail.toString());

                            htmlEmail = htmlEmail.replace("@Script", "");
                            htmlAnexo = htmlAnexo.replace("@Script", "");
                            htmlAlt = htmlAlt.replace("@Script", scriptFotos(idsFotos));

                            htmlEmail = htmlEmail.replace("@ImagemLogo", getLogo(empresa, "0"));
                            htmlAnexo = htmlAnexo.replace("@ImagemLogo", getLogoAnexo(empresa, "0"));
                            htmlAlt = htmlAlt.replace("@ImagemLogo", getLogo(empresa, "0"));

                            String sequencia = pstInspecoes.get(0).getData() + pstInspecoes.get(0).getSecao() + pstInspecoes.get(0).getSequencia().split(SEPARADOR)[1];
                            String anexoHTML = empresa + "_inspection_" + sequencia.replace(".0", "") + ".html";
                            String anexoPDF = empresa + "_inspection_" + sequencia.replace(".0", "") + ".pdf";

                            String assunto = empresa + " " + inspecoes.getDescricao() + " " + sequencia;

                            htmlEmail = htmlEmail.replace("@URL", "https://mobile.sasw.com.br:9091/Satmobile/documentos/anexo-email/" + anexoHTML);
                            htmlAlt = htmlAlt.replace("@URL", "");
                            htmlAnexo = htmlAnexo.replace("@URL", "");

                            htmlEmail = htmlEmail.replace("@MensagemUrl", messages.getMessage("MensagemUrl"));
                            htmlAlt = htmlAlt.replace("@MensagemUrl", "");
                            htmlAnexo = htmlAnexo.replace("@MensagemUrl", "");

                            File filePDFanexo = new File("C:/xampp/htdocs/satmobile/documentos/anexo-email/");
                            filePDFanexo.mkdirs();
                            OutputStream osPdf = new FileOutputStream("C:/xampp/htdocs/satmobile/documentos/anexo-email/" + anexoPDF);

                            Tidy tidy = new Tidy();
                            tidy.setShowWarnings(false);
                            InputStream input = new ByteArrayInputStream(htmlAnexo.getBytes());
                            Document doc = tidy.parseDOM(input, null);
                            ITextRenderer renderer = new ITextRenderer();
                            renderer.setDocument(doc, null);
                            renderer.layout();
                            renderer.createPDF(osPdf);
                            input.close();
                            osPdf.close();

                            if (param.contains("SATPISCINAFACIL")) {
                                try {
                                    char[] pass = "06956831fatima".toCharArray();
                                    BouncyCastleProvider provider = new BouncyCastleProvider();
                                    Security.addProvider(provider);
                                    KeyStore ks = KeyStore.getInstance(KeyStore.getDefaultType());
                                    ks.load(new FileInputStream(new File(getClass().getResource("/br/com/sasw/satmobew/piscina").toURI())), pass);
                                    String alias = (String) ks.aliases().nextElement();
                                    PrivateKey pk = (PrivateKey) ks.getKey(alias, pass);
                                    Certificate[] chain = ks.getCertificateChain(alias);
                                    X509Certificate signerCertificate = (X509Certificate) chain[0];

                                    PdfReader reader = new PdfReader("C:/xampp/htdocs/satmobile/documentos/anexo-email/" + anexoPDF);
                                    FileOutputStream os = new FileOutputStream("C:/xampp/htdocs/satmobile/documentos/anexo-email/"
                                            + empresa + "_sinspection_" + sequencia.replace(".0", "") + ".pdf");
                                    PdfStamper stamper = PdfStamper.createSignature(reader, os, '\0');
                                    // Creating the appearance
                                    PdfSignatureAppearance appearance = stamper.getSignatureAppearance();
                                    appearance.setReason("AUTENTICAÇÃO");
                                    appearance.setLocation("BRASÍLIA");
                                    appearance.setCertificate(signerCertificate);
                                    Rectangle cropBox = reader.getCropBox(1);
                                    float width = 200;
                                    float height = 60;
                                    appearance.setVisibleSignature(new Rectangle(cropBox.getRight(width), cropBox.getBottom(),
                                            cropBox.getRight(), cropBox.getBottom(height)), 1, "sig");
                                    // Creating the signature
                                    ExternalDigest digest = new BouncyCastleDigest();
                                    ExternalSignature signature = new PrivateKeySignature(pk, DigestAlgorithms.SHA256, provider.getName());
                                    MakeSignature.signDetached(appearance, digest, signature, chain, null, null, null, 0, MakeSignature.CryptoStandard.CMS);

                                    anexoPDF = empresa + "_sinspection_" + sequencia.replace(".0", "") + ".pdf";
                                } catch (Exception eC) {
                                    Trace.gerarTrace(getServletContext(), this.getServletName(), eC.getMessage(), codPessoa, param, logerro);
                                }
                            }

                            File newHtmlFile = new File("C:/xampp/htdocs/satmobile/documentos/anexo-email/" + anexoHTML);
                            FileUtils.writeStringToFile(newHtmlFile, htmlAlt, StandardCharsets.UTF_8);

                            if (param.contains("GSI") || param.contains("PROSECUR") || param.contains("GLOVAL")) {
                                EmailsEnviarDao emailDao = new EmailsEnviarDao();
                                EmailsEnviarAnexoDao emailanexodao = new EmailsEnviarAnexoDao();

                                EmailsEnviar email1 = new EmailsEnviar();
                                EmailsEnviar email2 = new EmailsEnviar();
                                EmailsEnviar email3 = new EmailsEnviar();

                                email1.setRemet_email("<EMAIL>");
                                email2.setRemet_email("<EMAIL>");
                                email3.setRemet_email("<EMAIL>");

                                email1.setRemet_nome(operador);
                                email2.setRemet_nome(operador);
                                email3.setRemet_nome(operador);

                                email1.setDest_nome(filial.getRazaoSocial());
                                email2.setDest_nome(filial.getRazaoSocial());
                                email3.setDest_nome(cliente.getContato());

                                email1.setDest_email("<EMAIL>;<EMAIL>");
                                email2.setDest_email("<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>");
                                email3.setDest_email(cliente.getEmail());
                                if (cliente.getEmail().equals("7350001")) {
                                    email3.setDest_email("<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>");
                                } else {
                                    email3.setDest_email(cliente.getEmail());
                                }

                                email1.setParametro(param);
                                email2.setParametro(param);
                                email3.setParametro(param);
                                email1.setCodFil(codfil);
                                email2.setCodFil(codfil);
                                email3.setCodFil(codfil);
                                email1.setCodCli(cliente.getCodigo());
                                email2.setCodCli(cliente.getCodigo());
                                email3.setCodCli(cliente.getCodigo());

                                email1.setAssunto(assunto);
                                email1.setSmtp("smtplw.com.br");
                                email1.setMensagem(htmlEmail.replace("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">\n", ""));
                                email1.setAut_login("sasw");
                                email1.setAut_senha("xNiadJEj9607");
                                email1.setPorta(587);

                                email2.setAssunto(assunto);
                                email2.setSmtp("smtplw.com.br");
                                email2.setMensagem(htmlEmail.replace("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">\n", ""));
                                email2.setAut_login("sasw");
                                email2.setAut_senha("xNiadJEj9607");
                                email2.setPorta(587);

                                email3.setAssunto(assunto);
                                email3.setSmtp("smtplw.com.br");
                                email3.setMensagem(htmlEmail.replace("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">\n", ""));
                                email3.setAut_login("sasw");
                                email3.setAut_senha("xNiadJEj9607");
                                email3.setPorta(587);

                                Persistencia satellite = pool.getConexao("SATELLITE");
                                String seq1 = emailDao.inserirEmail(email1, satellite);
                                String seq2 = emailDao.inserirEmail(email2, satellite);

                                EmailsEnviarAnexo emailanexo1 = new EmailsEnviarAnexo();
                                emailanexo1.setSequencia(new BigDecimal(seq1));
                                emailanexo1.setOrdem(1);
                                emailanexo1.setEndAnexo(anexoPDF);
                                emailanexo1.setNomeAnexo(anexoPDF);
                                emailanexo1.setDescAnexo(anexoPDF);

                                EmailsEnviarAnexo emailanexo2 = new EmailsEnviarAnexo();
                                emailanexo2.setSequencia(new BigDecimal(seq2));
                                emailanexo2.setOrdem(1);
                                emailanexo2.setEndAnexo(anexoPDF);
                                emailanexo2.setNomeAnexo(anexoPDF);
                                emailanexo2.setDescAnexo(anexoPDF);

                                emailanexodao.inserirAnexos(emailanexo1, satellite);
                                emailanexodao.inserirAnexos(emailanexo2, satellite);

                                //                    if(!email3.getDest_email().equals("")){
                                //                        String seq3 = emailDao.inserirEmail(email3, satellite);
                                //                        EmailsEnviarAnexo emailanexo3 = new EmailsEnviarAnexo();
                                //                        emailanexo3.setSequencia(new BigDecimal(seq3));
                                //                        emailanexo3.setOrdem(1);
                                //                        emailanexo3.setEndAnexo(anexoPDF);
                                //                        emailanexo3.setNomeAnexo(anexoPDF);
                                //                        emailanexo3.setDescAnexo(anexoPDF);
                                //                        emailanexodao.inserirAnexos(emailanexo3, satellite);
                                //                    }
                                satellite.FechaConexao();

                            } else if (param.contains("SASW") || param.contains("SASEX")) {
                                EmailsEnviar email1 = new EmailsEnviar();

                                email1.setRemet_email("<EMAIL>");
                                email1.setRemet_nome(operador);
                                email1.setDest_nome(filial.getRazaoSocial());

                                email1.setDest_email("<EMAIL>;<EMAIL>;<EMAIL>");

                                email1.setParametro(param);
                                email1.setCodFil(codfil);
                                email1.setCodCli(cliente.getCodigo());

                                email1.setAssunto(assunto);
                                email1.setSmtp("smtplw.com.br");
                                email1.setMensagem(htmlEmail);
                                email1.setAut_login("sasw");
                                email1.setAut_senha("xNiadJEj9607");
                                email1.setPorta(587);

                                EmailsEnviarDao emailDao = new EmailsEnviarDao();

                                Persistencia satellite = pool.getConexao("SATELLITE");
                                String seq1 = emailDao.inserirEmail(email1, satellite);

                                EmailsEnviarAnexo emailanexo1 = new EmailsEnviarAnexo();
                                emailanexo1.setSequencia(new BigDecimal(seq1));
                                emailanexo1.setOrdem(1);
                                emailanexo1.setEndAnexo(anexoPDF);
                                emailanexo1.setNomeAnexo(anexoPDF);
                                emailanexo1.setDescAnexo(anexoPDF);

                                EmailsEnviarAnexoDao emailanexodao = new EmailsEnviarAnexoDao();
                                emailanexodao.inserirAnexos(emailanexo1, satellite);
                                File fHTML = new File(anexoHTML);
                                fHTML.delete();
                                File fPDF = new File(anexoPDF);
                                fPDF.delete();

                                satellite.FechaConexao();
                            } else if (!param.contains("CONFEDERAL")){
                                EmailsEnviar email = new EmailsEnviar();
                                email.setRemet_email("<EMAIL>");
                                email.setRemet_nome(operador);
                                email.setDest_nome(filial.getRazaoSocial());
                                email.setDest_email(filial.getEmail());

                                email.setParametro(param);
                                email.setCodFil(codfil);
                                email.setCodCli(cliente.getCodigo());

                                email.setAssunto(assunto);
                                email.setSmtp("smtplw.com.br");
                                email.setMensagem(htmlEmail);
                                email.setAut_login("sasw");
                                email.setAut_senha("xNiadJEj9607");
                                email.setPorta(587);

                                if (null != email.getDest_email() && !email.getDest_email().equals("")) {
                                    EmailsEnviarDao emailDao = new EmailsEnviarDao();

                                    Persistencia satellite = pool.getConexao("SATELLITE");
                                    String seq = emailDao.inserirEmail(email, satellite);

                                    EmailsEnviarAnexo emailanexo = new EmailsEnviarAnexo();
                                    emailanexo.setSequencia(new BigDecimal(seq));
                                    emailanexo.setOrdem(1);
                                    emailanexo.setEndAnexo(anexoPDF);
                                    emailanexo.setNomeAnexo(anexoPDF);
                                    emailanexo.setDescAnexo(anexoPDF);

                                    EmailsEnviarAnexoDao emailanexodao = new EmailsEnviarAnexoDao();
                                    emailanexodao.inserirAnexos(emailanexo, satellite);

                                    File fHTML = new File(anexoHTML);
                                    fHTML.delete();
                                    File fPDF = new File(anexoPDF);
                                    fPDF.delete();

                                    satellite.FechaConexao();
                                }
                            }
                        } catch (Exception e) {

                        }
                    }
                }

//                out.print(htmlAlt);
            } else {
                retorno += "<resp>2</resp>";
            }
            persistencia.FechaConexao();
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0) + " segundos.\r\n", codPessoa, param, logerro);
            //Apagar Arquivos Temp
            apagarWsSASTemp();
            out.print(retorno);
//            map.cleanUp(request);
        } catch (Exception e) {
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Falha RealizarInspecao - " + e.getMessage(), codPessoa, param, logerro);
            out.print("<?xml version=\"1.0\"?><resp>0</resp>");
        }
    }

    private String scriptFotos(List<String> idsFotos) {
        String script = "<script type=\"text/javascript\">\n"
                + "       @ImagemRotates\n"
                + "       document.addEventListener(\"DOMContentLoaded\", function(){ \n"
                + "            @ImagemEventListener"
                + "       });"
                + "</script>";
        String rotates = "rotate_@ImagemNumero = 0; \n";
        String addEventListener = "document.getElementById(\"next_@ImagemNumero\").addEventListener(\"click\", function(){ \n"
                + "                 \n"
                + "                    if(rotate_@ImagemNumero== 360){rotate_@ImagemNumero = 0} \n"
                + "                 \n"
                + "                    rotate_@ImagemNumero = rotate_@ImagemNumero + 90; \n"
                + "                     \n"
                + "                    document.getElementById(\"imggirar_@ImagemNumero\").style.transform = \"rotate(\"+rotate_@ImagemNumero+\"deg)\"; \n"
                + "                    document.getElementById(\"trfoto\").style.height   = document.getElementById(\"imggirar_@ImagemNumero\").width+30; \n"
                + "                });\n"
                + "                 \n"
                + "                document.getElementById(\"back_@ImagemNumero\").addEventListener(\"click\", function(){ \n"
                + "                 \n"
                + "                    if(rotate_@ImagemNumero== -360){rotate_@ImagemNumero = 0} \n"
                + "                 \n"
                + "                    rotate_@ImagemNumero = rotate_@ImagemNumero + -90 ;\n"
                + "                     \n"
                + "                    document.getElementById(\"imggirar_@ImagemNumero\").style.transform = \"rotate(\"+rotate_@ImagemNumero+\"deg)\"; \n"
                + "                    document.getElementById(\"trfoto\").style.height   = document.getElementById(\"imggirar_@ImagemNumero\").width+30; \n"
                + "                });     \n";
        StringBuilder rotatesSB = new StringBuilder();
        StringBuilder eventsSB = new StringBuilder();
        for (String idFoto : idsFotos) {
            rotatesSB.append(rotates.replace("@ImagemNumero", idFoto));
            eventsSB.append((addEventListener.replaceAll("@ImagemNumero", idFoto)));
        }
        return script.replace("@ImagemRotates", rotatesSB.toString()).replace("@ImagemEventListener", eventsSB.toString());
    }

    private String apagarWsSASTemp(){
        File diretorio = new File("C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobEWTemp");         
        FileFilter ff = new FileFilter() { 
            public boolean accept(File arquivo){ 
                return arquivo.getName().endsWith(".tmp") || 
                	   arquivo.getName().endsWith(".png") || 
                	   arquivo.getName().endsWith(".pdf"); 
            } 
        }; 
        
        File[] arquivos = diretorio.listFiles(ff); 
  
        if(arquivos != null){ 
            for(File arquivo : arquivos){ 
               arquivo.delete();  
            } 
        }
        return null;
    }
    
    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
