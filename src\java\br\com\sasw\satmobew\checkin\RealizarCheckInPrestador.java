/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satmobew.checkin;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.EmailsEnviar;
import SasBeans.EmailsEnviarAnexo;
import SasBeans.Filiais;
import SasBeans.Pessoa;
import SasBeans.PstServ;
import SasBeans.RHPonto;
import SasBeans.RHPontoDet;
import SasBeans.RHPontoGEO;
import SasDaos.EmailsEnviarAnexoDao;
import SasDaos.EmailsEnviarDao;
import SasDaos.FiliaisDao;
import SasDaos.PessoaDao;
import SasDaos.PstServDao;
import SasDaos.RHPontoDao;
import SasDaos.RHPontoDetDao;
import SasDaos.RHPontoGeoDao;
import static SasLibrary.ValidarUsuario.ValidaUsuarioPonto;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.Logos.getLogo;
import static br.com.sasw.pacotesuteis.utilidades.Logos.getLogoAnexo;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import br.com.sasw.satmobew.ValidarUsuario;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.io.FileUtils;
import org.w3c.dom.Document;
import org.w3c.tidy.Tidy;
import org.xhtmlrenderer.pdf.ITextRenderer;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "RealizarCheckInPrestador", urlPatterns = {"/checkin/RealizarCheckInPrestador"})
public class RealizarCheckInPrestador extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;

    @Override
    public void init() {
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        logerro = new ArquivoLog();

        String codPessoa = request.getParameter("codpessoa");
        String senha = request.getParameter("senha");
        String param = request.getParameter("param");
        String empresa = param;
        String matricula = request.getParameter("matricula");
        String data = request.getParameter("data");
        String hora = request.getParameter("hora");
        String codfil = request.getParameter("codfil");
        String operador = request.getParameter("operador");
        String imagem = request.getParameter("imagem");
        String latitude = request.getParameter("lat");
        String longitude = request.getParameter("long");
        String secao = request.getParameter("secao");
        String local = request.getParameter("local");
        String dataAtual = request.getParameter("dataAtual");
        String horaAtual = request.getParameter("horaAtual");
        String idioma = request.getParameter("idioma");

        if (null == dataAtual || dataAtual.equals("")) {
            dataAtual = DataAtual.getDataAtual("SQL");
        }
        if (null == horaAtual || horaAtual.equals("")) {
            horaAtual = DataAtual.getDataAtual("HORA");
        }

        String retorno = "<?xml version=\"1.0\"?>";

        try {
            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codPessoa, param, logerro);
            tStart = System.currentTimeMillis();

            Persistencia persistencia = pool.getConexao(param);
            empresa = persistencia.getEmpresa().equals("SATSASEX") ? "SASW" : persistencia.getEmpresa().replace("SAT", "");

            if (ValidaUsuarioPonto(codPessoa, senha, persistencia)) {
                PessoaDao pessoaDao = new PessoaDao();
                PstServDao pstServDao = new PstServDao();
                RHPontoDao rHPontoDao = new RHPontoDao();
                FiliaisDao filiaisDao = new FiliaisDao();
                RHPontoGeoDao rHPontoGeoDao = new RHPontoGeoDao();
                RHPontoDetDao rHPontoDetDao = new RHPontoDetDao();
                String dtCompet = data;
                int quantidade = rHPontoDao.obterProximoCheckInPrestador(new BigDecimal(matricula), dtCompet, persistencia).getBatida().toBigInteger().intValue();

                //Adcionando um à batida de ponto
                quantidade = quantidade + 1;
                BigDecimal qtde = new BigDecimal(String.valueOf(quantidade));
                boolean enviado = enviandoImagem(imagem, param, matricula, quantidade, dtCompet);
//                        boolean enviado = enviandoImagem(imagem, param, matricula, quantidade, data);

                //Salvando informações da latitude
                RHPontoGEO rhpgeo = new RHPontoGEO();
                rhpgeo.setMatr(new BigDecimal(matricula));
                rhpgeo.setDtCompet(dtCompet);
//                        rhpgeo.setDtCompet(data);
                rhpgeo.setLatitude(latitude);
                rhpgeo.setLongitude(longitude);
                rhpgeo.setBatida(quantidade);
                rHPontoGeoDao.salvarBatida(rhpgeo, persistencia);

                //Salvando detalhes da batida
                RHPontoDet rhPontoDet = new RHPontoDet();
                rhPontoDet.setMatr(matricula);
                rhPontoDet.setDtCompet(dtCompet);
                rhPontoDet.setBatida(String.valueOf(quantidade));
                rhPontoDet.setSecao(secao);
                rhPontoDet.setLocal(local);
                rHPontoDetDao.inserirRHPontoDet(rhPontoDet, persistencia);

                //Inserindo informações
                RHPonto rHPonto = new RHPonto();
                rHPonto.setMatr(new BigDecimal(matricula));
                rHPonto.setDtCompet(dtCompet);
//                        rHPonto.setDtCompet(data);
                rHPonto.setBatida(qtde);
                rHPonto.setTipo(qtde);
                rHPonto.setHora(hora);
                rHPonto.setDtBatida(data);
                rHPonto.setDt_Alter(data);
                rHPonto.setHr_Alter(hora);
                rHPonto.setOperador(tratarOperador("MOB_" + operador));

                if (enviado) {
                    rHPonto.setNSerie("SATMOB_FOTO_ASS");
                }

                rHPontoDao.salvarRegistro(rHPonto, persistencia);

                retorno += "<resp>1</resp>";

                retorno += "<data>" + data + "</data>";
                retorno += "<hora>" + hora + "</hora>";
                retorno += "<batidas>" + quantidade + "</batidas>";

                try {
                    File file = new File(getServletContext().getRealPath("/relatorio.html").replace("%20", " "));
                    FileInputStream fis = new FileInputStream(file);
                    byte[] dados = new byte[(int) file.length()];
                    fis.read(dados);
                    fis.close();
                    String htmlEmail = new String(dados, "UTF-8");
                    String htmlAlt;
                    String htmlAnexo;

                    Pessoa funcion = pessoaDao.getPessoaCodigo(new BigDecimal(codPessoa), persistencia);
                    PstServ pstServ = pstServDao.getPstServ(secao, codfil, persistencia);

                    Filiais filial = filiaisDao.getFilial(codfil, persistencia);

                    String dataHtml;
                    try {
                        LocalDate datetime = LocalDate.parse(data, DateTimeFormatter.ofPattern("yyyyMMdd"));
                        dataHtml = datetime.format(idioma.equals("en") ? DateTimeFormatter.ofPattern("MM/dd/yyyy") : DateTimeFormatter.ofPattern("dd/MM/yyyy"));
                    } catch (Exception xxx) {
                        dataHtml = data;
                    }

                    int checkins = rHPontoDao.obterQuantidadeCheckInsPrestador(new BigDecimal(matricula), dtCompet, persistencia);
                    String check = idioma.equals("en") ? ("Supervisor " + (checkins % 2 == 0 ? " Check Out" : " Check In") + " Report")
                            : "Relatório de " + (checkins % 2 == 0 ? "Saída " : "Chegada ") + "Prestador de Serviço";
                    htmlEmail = htmlEmail.replace("@TituloPagina", check);
                    htmlEmail = htmlEmail.replace("@TituloRelatorio", check.toUpperCase());
                    htmlEmail = htmlEmail.replace("@SubTituloRelatorio", pstServ.getLocal());
                    htmlEmail = htmlEmail.replace("@TituloInfo", pstServ.getDescContrato());
                    htmlEmail = htmlEmail.replace("@TituloEndereco", filial.getRazaoSocial());
                    htmlEmail = htmlEmail.replace("@TituloTelefone", idioma.equals("en") ? FuncoesString.formatarString(filial.getFone(), "(###) ###-####")
                            : FuncoesString.formatarString(filial.getFone(), "(##) ########?"));
                    htmlEmail = htmlEmail.replace("@Detalhes", idioma.equals("en") ? "Details" : "Detalhes");

                    String padrao1 = "						<tr>\n"
                            + "							<td style=\"width:30.0%;padding:0in 0in 0in 0in\" width=\"30%\">\n"
                            + "								<p class=\"MsoNormal\">\n"
                            + "									<b>\n"
                            + "										<span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif\">\n"
                            + "											@Padrao:\n"
                            + "										</span>\n"
                            + "									</b>\n"
                            + "									<span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif\">\n"
                            + "										<u>\n"
                            + "										</u>\n"
                            + "										<u>\n"
                            + "										</u>\n"
                            + "									</span>\n"
                            + "								</p>\n"
                            + "							</td>\n"
                            + "							<td style=\"padding:0in 0in 0in 0in\">\n"
                            + "								<p class=\"MsoNormal\">\n"
                            + "									<span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif\">\n"
                            + "										<strong>@TextoPadrao</strong>\n"
                            + "										<u>\n"
                            + "										</u>\n"
                            + "										<u>\n"
                            + "										</u>\n"
                            + "									</span>\n"
                            + "								</p>\n"
                            + "							</td>\n"
                            + "						</tr>";
                    String padraoFoto
                            = "                <tr>\n"
                            + "                    <td colspan=\"2\" style=\"padding:.75pt .75pt .75pt .75pt; text-align: center;\">\n"
                            + "                        <img id=\"imggirar\" alt=\"Image not found\" src=\"@ImagemRelatorio\" height=\"480\" style=\"height: 480px\" />\n"
                            + "                    </td>\n"
                            + "                </tr>\n";
                    String padraoFotoAlt
                            = "                <tr>\n"
                            + "                    <td colspan=\"2\" style=\"padding:.75pt .75pt .75pt .75pt; text-align: center;\">\n"
                            + "                        <img id=\"imggirar\" alt=\"Image not found\" src=\"@ImagemRelatorio\" height=\"480\" style=\"height: 480px\" />\n"
                            + "                    </td>\n"
                            + "                </tr>\n"
                            + "             <tr>\n"
                            + "                    <td colspan=\"2\" style=\"text-align: center;\">\n"
                            + "                       <button id=\"back\">@GirarEsquerda</button> \n"
                            + "                       <button id=\"next\">@GirarDireita</button>\n"
                            + "                    </td>\n"
                            + "                </tr>";
                    String padraoMapa = "                <tr>\n"
                            + "                    <td colspan=\"2\" style=\"padding:.75pt .75pt .75pt .75pt\">\n"
                            + "                           <p class=\"MsoNormal\">\n"
                            + "									<b>\n"
                            + "										<span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif\">\n"
                            + "                        @Padrao:\n"
                            + "										</span>\n"
                            + "									</b>\n"
                            + "									<span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif\">\n"
                            + "										<u>\n"
                            + "										</u>\n"
                            + "										<u>\n"
                            + "										</u>\n"
                            + "									</span>\n"
                            + "								</p>\n"
                            + "                    </td>\n"
                            + "                </tr>\n"
                            + "                <tr>\n"
                            + "                    <td colspan=\"2\" style=\"padding:.75pt .75pt .75pt .75pt\">\n"
                            + "                        <div id=\"mapa\" style=\"height: 360px; width: 100%; text-align: center; vertical-align: middle; line-height: 369px;\">\n"
                            + "                             @MensagemErroMapa\n"
                            + "                         </div>\n"
                            + "                    </td>\n"
                            + "                </tr>";
                    String padraoMapaEstatico = "                <tr>\n"
                            + "                    <td colspan=\"2\" style=\"padding:.75pt .75pt .75pt .75pt\">\n"
                            + "                           <p class=\"MsoNormal\">\n"
                            + "									<b>\n"
                            + "										<span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif\">\n"
                            + "                        @Padrao:\n"
                            + "										</span>\n"
                            + "									</b>\n"
                            + "									<span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif\">\n"
                            + "										<u>\n"
                            + "										</u>\n"
                            + "										<u>\n"
                            + "										</u>\n"
                            + "									</span>\n"
                            + "								</p>\n"
                            + "                    </td>\n"
                            + "                </tr>\n"
                            + "                <tr>\n"
                            + "                    <td colspan=\"2\" style=\"padding:.75pt .75pt .75pt .75pt; text-align: center;\">\n"
                            + "                        <img alt=\"Map not found\" src=\"@MapaRelatorio\" height=\"480\" style=\"height: 480px\" />\n"
                            + "                    </td>\n"
                            + "                </tr>";

                    StringBuilder relatorioEmail = new StringBuilder();
                    StringBuilder relatorioAlt = new StringBuilder();
                    StringBuilder relatorioAnexo = new StringBuilder();

                    relatorioEmail.append(padrao1.replace("@Padrao", idioma.equals("en") ? "Officer" : "Prestador").replace("@TextoPadrao", funcion.getNome()));
                    relatorioEmail.append(padrao1.replace("@Padrao", idioma.equals("en") ? "Location" : "Local").replace("@TextoPadrao", local));
                    relatorioEmail.append(padrao1.replace("@Padrao", idioma.equals("en") ? "Date-Time" : "Data-Hora").replace("@TextoPadrao", dataHtml + " - " + hora));

                    relatorioAnexo.append(relatorioEmail);
                    relatorioAlt.append(relatorioEmail);

                    String urlEmail = "https://mobile.sasw.com.br:9091/satellite/fotos/" + param + "/ponto/";
                    String urlAlt = "https://mobile.sasw.com.br:9091/satellite/fotos/" + param + "/ponto/";
//                    String urlAnexo = "http://localhost:9080/satellite/fotos/" + param + "/ponto/";
                    String urlAnexo = "file:\\C:\\xampp\\htdocs\\satellite\\fotos\\" + param + "\\ponto\\";

                    String foto = FuncoesString.RecortaString(dtCompet, 0, 10).replaceAll("-", "") + "/"
                            + trataMatricula(matricula.replace(".0", "")) + "_" + quantidade + ".jpg";
                    String mapa = FuncoesString.RecortaString(dtCompet, 0, 10).replaceAll("-", "") + "/"
                            + trataMatricula(matricula.replace(".0", "")) + "_mapa_" + quantidade + ".png";

                    enviandoImagemMapa(mapaEstatico(latitude, longitude, pstServ.getLocal()), param, matricula, quantidade, dtCompet);

                    relatorioEmail.append(padraoFoto.replace("@ImagemRelatorio", urlEmail + foto)
                            .replace("@ImagemFoto", idioma.equals("en") ? "Photo" : "Foto"));
                    relatorioAlt.append(padraoFotoAlt.replace("@ImagemRelatorio", urlAlt + foto)
                            .replace("@ImagemFoto", idioma.equals("en") ? "Photo" : "Foto")
                            .replace("@GirarEsquerda", idioma.equals("en") ? "Turn Left" : "Girar Esquerda")
                            .replace("@GirarDireita", idioma.equals("en") ? "Turn Right" : "Girar Direita"));
                    relatorioAnexo.append(padraoFoto.replace("@ImagemRelatorio", urlAnexo + foto)
                            .replace("@ImagemFoto", idioma.equals("en") ? "Photo" : "Foto"));

                    relatorioEmail.append(padraoMapaEstatico.replace("@Padrao", idioma.equals("en") ? "Map" : "Mapa")
                            .replace("@MapaRelatorio", mapaEstatico(latitude, longitude, pstServ.getLocal())));
                    relatorioAlt.append(padraoMapa.replace("@Padrao", idioma.equals("en") ? "Map" : "Mapa")
                            .replace("@MensagemErroMapa", idioma.equals("en") ? "Error showing the map" : "Erro ao mostrar o mapa"));
                    relatorioAnexo.append(padraoMapaEstatico.replace("@Padrao", idioma.equals("en") ? "Map" : "Mapa")
                            .replace("@MapaRelatorio", urlAnexo + mapa));

                    htmlAlt = htmlEmail.replace("@Relatorio", relatorioAlt.toString());
                    htmlAnexo = htmlEmail.replace("@Relatorio", relatorioAnexo.toString());
                    htmlEmail = htmlEmail.replace("@Relatorio", relatorioEmail.toString());

                    htmlEmail = htmlEmail.replace("@Script", "");
                    htmlAlt = htmlAlt.replace("@Script", mapa(latitude, longitude, pstServ.getLocal()));
                    htmlAnexo = htmlAnexo.replace("@Script", "");

                    htmlEmail = htmlEmail.replace("@ImagemLogo", getLogo(empresa, "0"));
                    htmlAlt = htmlAlt.replace("@ImagemLogo", getLogo(empresa, "0"));
                    htmlAnexo = htmlAnexo.replace("@ImagemLogo", getLogoAnexo(empresa, "0"));

                    String anexoHTML = empresa + "_report_" + matricula + "_" + dtCompet + "_" + quantidade + ".html";
                    String anexoPdf = empresa + "_report_" + matricula + "_" + dtCompet + "_" + quantidade + ".pdf";

                    htmlEmail = htmlEmail.replace("@URL", "https://mobile.sasw.com.br:9091/Satmobile/documentos/anexo-email/" + anexoHTML);
                    htmlAlt = htmlAlt.replace("@URL", "");
                    htmlAnexo = htmlAnexo.replace("@URL", "");

                    htmlEmail = htmlEmail.replace("@MensagemUrl", idioma.equals("en") ? "Email not displaying correctly? View it in your browser."
                            : "Email não exibido corretamente? Veja diretamente no seu navegador.");
                    htmlAlt = htmlAlt.replace("@MensagemUrl", "");
                    htmlAnexo = htmlAnexo.replace("@MensagemUrl", "");

                    //out.print(html);
                    File filePDFanexo = new File("C:/xampp/htdocs/satmobile/documentos/anexo-email/");
                    filePDFanexo.mkdirs();
                    OutputStream osPdf = new FileOutputStream("C:/xampp/htdocs/satmobile/documentos/anexo-email/" + anexoPdf);

                    Tidy tidy = new Tidy();
                    tidy.setShowWarnings(false);
                    InputStream input = new ByteArrayInputStream(htmlAnexo.getBytes());
                    Document doc = tidy.parseDOM(input, null);
                    ITextRenderer renderer = new ITextRenderer();
                    renderer.setDocument(doc, null);
                    renderer.layout();
                    renderer.createPDF(osPdf);
                    input.close();
                    osPdf.close();

                    File newHtmlFile = new File("C:/xampp/htdocs/satmobile/documentos/anexo-email/" + anexoHTML);
                    FileUtils.writeStringToFile(newHtmlFile, htmlAlt, StandardCharsets.UTF_8);

                    if (param.contains("GSI") || param.contains("PROSECUR") || param.contains("GLOVAL")) {
                        EmailsEnviar email1 = new EmailsEnviar();
                        EmailsEnviar email2 = new EmailsEnviar();
                        EmailsEnviar email3 = new EmailsEnviar();

                        email1.setRemet_email("<EMAIL>");
                        email2.setRemet_email("<EMAIL>");
                        email3.setRemet_email("<EMAIL>");

                        email1.setRemet_nome(operador);
                        email2.setRemet_nome(operador);
                        email3.setRemet_nome(operador);

                        email1.setDest_nome(filial.getRazaoSocial());
                        email2.setDest_nome(filial.getRazaoSocial());
                        email3.setDest_nome(pstServ.getOperador());

                        email1.setDest_email("<EMAIL>;<EMAIL>");
                        email2.setDest_email("<EMAIL>; <EMAIL>; <EMAIL>;<EMAIL>");
                        if (pstServ.getCodCli().equals("7350001")) {
                            email3.setDest_email("<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>");
                        } else {
                            email3.setDest_email(pstServ.getInterfExt());
                        }

                        email1.setParametro(param);
                        email2.setParametro(param);
                        email3.setParametro(param);
                        email1.setCodFil(codfil);
                        email2.setCodFil(codfil);
                        email3.setCodFil(codfil);
                        email1.setCodCli(pstServ.getCodCli());
                        email2.setCodCli(pstServ.getCodCli());
                        email3.setCodCli(pstServ.getCodCli());

                        email1.setAssunto(check);
                        email1.setSmtp("smtplw.com.br");
                        email1.setMensagem(htmlEmail);
                        email1.setAut_login("sasw");
                        email1.setAut_senha("xNiadJEj9607");
                        email1.setPorta(587);

                        email2.setAssunto(check);
                        email2.setSmtp("smtplw.com.br");
                        email2.setMensagem(htmlEmail);
                        email2.setAut_login("sasw");
                        email2.setAut_senha("xNiadJEj9607");
                        email2.setPorta(587);

                        email3.setAssunto(check);
                        email3.setSmtp("smtplw.com.br");
                        email3.setMensagem(htmlEmail);
                        email3.setAut_login("sasw");
                        email3.setAut_senha("xNiadJEj9607");
                        email3.setPorta(587);

                        EmailsEnviarDao emailDao = new EmailsEnviarDao();

                        Persistencia satellite = pool.getConexao("SATELLITE");
                        String seq1 = emailDao.inserirEmail(email1, satellite);
                        String seq2 = emailDao.inserirEmail(email2, satellite);

                        EmailsEnviarAnexo emailanexo1 = new EmailsEnviarAnexo();
                        emailanexo1.setSequencia(new BigDecimal(seq1));
                        emailanexo1.setOrdem(1);
                        emailanexo1.setEndAnexo(anexoPdf);
                        emailanexo1.setNomeAnexo(anexoPdf);
                        emailanexo1.setDescAnexo(anexoPdf);

                        EmailsEnviarAnexo emailanexo2 = new EmailsEnviarAnexo();
                        emailanexo2.setSequencia(new BigDecimal(seq2));
                        emailanexo2.setOrdem(1);
                        emailanexo2.setEndAnexo(anexoPdf);
                        emailanexo2.setNomeAnexo(anexoPdf);
                        emailanexo2.setDescAnexo(anexoPdf);

                        EmailsEnviarAnexoDao emailanexodao = new EmailsEnviarAnexoDao();

                        emailanexodao.inserirAnexos(emailanexo1, satellite);
                        emailanexodao.inserirAnexos(emailanexo2, satellite);

                        if (!email3.getDest_email().equals("")) {
                            String seq3 = emailDao.inserirEmail(email3, satellite);
                            EmailsEnviarAnexo emailanexo3 = new EmailsEnviarAnexo();
                            emailanexo3.setSequencia(new BigDecimal(seq3));
                            emailanexo3.setOrdem(1);
                            emailanexo3.setEndAnexo(anexoPdf);
                            emailanexo3.setNomeAnexo(anexoPdf);
                            emailanexo3.setDescAnexo(anexoPdf);
                            emailanexodao.inserirAnexos(emailanexo3, satellite);
                        }

                        satellite.FechaConexao();

                    } else if (param.contains("SASW") || param.contains("SASEX")) {
                        EmailsEnviar email1 = new EmailsEnviar();
                        EmailsEnviar email2 = new EmailsEnviar();

                        email1.setRemet_email("<EMAIL>");
                        email2.setRemet_email("<EMAIL>");

                        email1.setRemet_nome(operador);
                        email2.setRemet_nome(operador);

                        email1.setDest_nome(filial.getRazaoSocial());
                        email2.setDest_nome(pstServ.getOperador());

                        email1.setDest_email("<EMAIL>;");

                        email2.setDest_email(pessoaDao.getEmail(new BigDecimal(codPessoa), persistencia));

                        email1.setParametro(param);
                        email2.setParametro(param);
                        email1.setCodFil(codfil);
                        email2.setCodFil(codfil);
                        email1.setCodCli(pstServ.getCodCli());
                        email2.setCodCli(pstServ.getCodCli());

                        email1.setAssunto(check);
                        email1.setSmtp("smtplw.com.br");
                        email1.setMensagem(htmlEmail);
                        email1.setAut_login("sasw");
                        email1.setAut_senha("xNiadJEj9607");
                        email1.setPorta(587);

                        email2.setAssunto(check);
                        email2.setSmtp("smtplw.com.br");
                        email2.setMensagem(htmlEmail);
                        email2.setAut_login("sasw");
                        email2.setAut_senha("xNiadJEj9607");
                        email2.setPorta(587);

                        EmailsEnviarDao emailDao = new EmailsEnviarDao();

                        Persistencia satellite = pool.getConexao("SATELLITE");
                        String seq1 = emailDao.inserirEmail(email1, satellite);

                        EmailsEnviarAnexo emailanexo1 = new EmailsEnviarAnexo();
                        emailanexo1.setSequencia(new BigDecimal(seq1));
                        emailanexo1.setOrdem(1);
                        emailanexo1.setEndAnexo(anexoPdf);
                        emailanexo1.setNomeAnexo(anexoPdf);
                        emailanexo1.setDescAnexo(anexoPdf);

                        EmailsEnviarAnexoDao emailanexodao = new EmailsEnviarAnexoDao();

                        emailanexodao.inserirAnexos(emailanexo1, satellite);
                        if (!email2.getDest_email().equals("")) {
                            String seq2 = emailDao.inserirEmail(email2, satellite);
                            EmailsEnviarAnexo emailanexo2 = new EmailsEnviarAnexo();
                            emailanexo2.setSequencia(new BigDecimal(seq2));
                            emailanexo2.setOrdem(1);
                            emailanexo2.setEndAnexo(anexoPdf);
                            emailanexo2.setNomeAnexo(anexoPdf);
                            emailanexo2.setDescAnexo(anexoPdf);
                            emailanexodao.inserirAnexos(emailanexo2, satellite);
                        }

                        satellite.FechaConexao();
                    } else {
                        EmailsEnviar email = new EmailsEnviar();
                        email.setRemet_email("<EMAIL>");
                        email.setRemet_nome(operador);
                        email.setDest_nome(filial.getRazaoSocial());
                        email.setDest_email(filial.getEmail());

                        email.setParametro(param);
                        email.setCodFil(codfil);
                        email.setCodCli(pstServ.getCodCli());

                        email.setAssunto(check);
                        email.setSmtp("smtplw.com.br");
                        email.setMensagem(htmlEmail);
                        email.setAut_login("sasw");
                        email.setAut_senha("xNiadJEj9607");
                        email.setPorta(587);

                        EmailsEnviarDao emailDao = new EmailsEnviarDao();

                        Persistencia satellite = pool.getConexao("SATELLITE");
                        String seq = emailDao.inserirEmail(email, satellite);

                        EmailsEnviarAnexo emailanexo = new EmailsEnviarAnexo();
                        emailanexo.setSequencia(new BigDecimal(seq));
                        emailanexo.setOrdem(1);
                        emailanexo.setEndAnexo(anexoPdf);
                        emailanexo.setNomeAnexo(anexoPdf);
                        emailanexo.setDescAnexo(anexoPdf);

                        EmailsEnviarAnexoDao emailanexodao = new EmailsEnviarAnexoDao();
                        emailanexodao.inserirAnexos(emailanexo, satellite);
                        satellite.FechaConexao();
                    }
                } catch (Exception e) {
                    Trace.gerarTrace(getServletContext(), this.getServletName(), e.getMessage(), codPessoa, param, logerro);
                }
            } else {
                retorno += "<resp>2</resp>";
            }
            Trace.gerarTrace(getServletContext(), this.getServletName(), retorno, codPessoa, param, logerro);
            persistencia.FechaConexao();
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0) + " segundos.\r\n", codPessoa, param, logerro);
        } catch (Exception e) {
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Falha RealizarCheckIn - " + e.getMessage(), codPessoa, param, logerro);
            out.print("<?xml version=\"1.0\"?><resp>0</resp>");
        }

        out.print(retorno);
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

    private String mapa(String lat, String lon, String localidade) {
        String script = "<script type=\"text/javascript\">\n"
                + "	function initialize() {\n"
                + "\n"
                + "	  var myLatlng = new google.maps.LatLng(@Latitude, @Longitude);\n"
                + "	  var mapOptions = {\n"
                + "		zoom: 17,\n"
                + "		center: myLatlng,\n"
                + "		panControl: false,\n"
                + "		mapTypeControlOptions: {\n"
                + "		  mapTypeIds: [google.maps.MapTypeId.ROADMAP, 'map_style']\n"
                + "		}\n"
                + "	  }\n"
                + "\n"
                + "	  var map = new google.maps.Map(document.getElementById(\"mapa\"), mapOptions);\n"
                + "\n"
                + "	  var image = 'https://cdn1.iconfinder.com/data/icons/gpsmapicons/blue/gpsmapicons01.png';\n"
                + "	  var marcadorPersonalizado = new google.maps.Marker({\n"
                + "		  position: myLatlng,\n"
                + "		  map: map,\n"
                + "		  title: '@Localidade',\n"
                + "		  animation: google.maps.Animation.DROP\n"
                + "	  });\n"
                + "	}"
                + "\n"
                + "	function loadScript() {\n"
                + "	  var script = document.createElement(\"script\");\n"
                + "	  script.type = \"text/javascript\";\n"
                + "	  script.src = \"https://maps.googleapis.com/maps/api/js?key=AIzaSyD6DdGi_hMzj_pw9yAtveEY7GUNlu1oRGI&sensor=true&callback=initialize\";\n"
                + "	  document.body.appendChild(script);\n"
                + "	}\n"
                + "\n"
                + "	window.onload = loadScript;\n"
                + " rotate = 0; \n"
                + "             \n"
                + "            document.addEventListener(\"DOMContentLoaded\", function(){ \n"
                + "                document.getElementById(\"next\").addEventListener(\"click\", function(){ \n"
                + "                 \n"
                + "                    if(rotate== 360){rotate = 0} \n"
                + "                 \n"
                + "                    rotate = rotate + 90; \n"
                + "                     \n"
                + "                    document.getElementById(\"imggirar\").style.transform = \"rotate(\"+rotate+\"deg)\"; \n"
                + "                    document.getElementById(\"trfoto\").style.height   = document.getElementById(\"imggirar\").width+30; \n"
                + "                });\n"
                + "                 \n"
                + "                document.getElementById(\"back\").addEventListener(\"click\", function(){ \n"
                + "                 \n"
                + "                    if(rotate== -360){rotate = 0} \n"
                + "                 \n"
                + "                    rotate = rotate + -90 ;\n"
                + "                     \n"
                + "                    document.getElementById(\"imggirar\").style.transform = \"rotate(\"+rotate+\"deg)\"; \n"
                + "                    document.getElementById(\"trfoto\").style.height   = document.getElementById(\"imggirar\").width+30; \n"
                + "                });     \n"
                + "            });"
                + "</script>";
        return script.replace("@Latitude", lat).replace("@Longitude", lon).replace("@Localidade", localidade);
    }

    private String mapaEstatico(String lat, String lon, String localidade) {
        String url = "https://maps.googleapis.com/maps/api/staticmap?size=640x480&format=PNG"
                + "&markers=color:red|label:@Localidade|@Latitude,@Longitude&key=AIzaSyD6DdGi_hMzj_pw9yAtveEY7GUNlu1oRGI";
        return url.replace("@Latitude", lat).replace("@Longitude", lon).replace("@Localidade", localidade).replace(" ", "%20");
    }

    private boolean enviandoImagem(String imagem, String param, String matricula, int numero, String data) {
        boolean enviado = false;
        try {
            //Criando imagem
            byte[] montar = new sun.misc.BASE64Decoder().decodeBuffer(imagem);

            String caminho = "C:/xampp/htdocs/satellite/fotos/" + param + "/ponto/" + data;
            File diretorio = new File(caminho);
            if (!diretorio.exists()) {
                diretorio.mkdirs();  // cria diretórios caso não estejam criados
            }

            String nome = caminho + "/" + trataMatricula(matricula.replace(".0", "")) + "_" + numero + ".jpg";

            FileOutputStream fos = new FileOutputStream(nome);
            fos.write(montar);
            FileDescriptor fd = fos.getFD();
            fos.flush();
            fd.sync();
            fos.close();

            enviado = true;
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return enviado;
    }

    private boolean enviandoImagemMapa(String url, String param, String matricula, int numero, String data) {
        boolean enviado = false;
        try {
            //Criando imagem
            URL u = new URL(url);
            InputStream in = new BufferedInputStream(u.openStream());
            ByteArrayOutputStream o = new ByteArrayOutputStream();
            byte[] buf = new byte[1024];
            int n = 0;
            while (-1 != (n = in.read(buf))) {
                o.write(buf, 0, n);
            }
            o.close();
            in.close();
            byte[] r = o.toByteArray();

            String caminho = "C:/xampp/htdocs/satellite/fotos/" + param + "/ponto/" + data;
            File diretorio = new File(caminho);
            if (!diretorio.exists()) {
                diretorio.mkdirs();  // cria diretórios caso não estejam criados
            }

            String nome = caminho + "/" + trataMatricula(matricula.replace(".0", "")) + "_mapa_" + numero + ".png";

            FileOutputStream fos = new FileOutputStream(nome);
            fos.write(r);
            fos.close();

            enviado = true;
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return enviado;
    }

    //com 8 Digitos
    private String trataMatricula(String matricula) {
        if (matricula.length() == 1) {
            matricula = "0000000" + matricula;
        } else if (matricula.length() == 2) {
            matricula = "000000" + matricula;
        } else if (matricula.length() == 3) {
            matricula = "00000" + matricula;
        } else if (matricula.length() == 4) {
            matricula = "0000" + matricula;
        } else if (matricula.length() == 5) {
            matricula = "000" + matricula;
        } else if (matricula.length() == 6) {
            matricula = "00" + matricula;
        } else if (matricula.length() == 7) {
            matricula = "0" + matricula;
        }
        return matricula;
    }

    //Realiza o tratamento do operador
    private String tratarOperador(String operador) {
        if (operador.length() > 10) {
            operador = operador.substring(0, 9);
        }
        return operador;
    }

}
