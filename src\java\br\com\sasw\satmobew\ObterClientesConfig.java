/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satmobew;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Clientes;
import SasDaos.ClientesDao;
import br.com.sasw.pacotesuteis.utilidades.GPS;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import Xml.Xmls;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "ObterClientesConfig", urlPatterns = {"/ObterClientesConfig"})
public class ObterClientesConfig extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;
    
    @Override
    public void init(){
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

     /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
    
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/plain");
        PrintWriter resp = response.getWriter();
        logerro = new ArquivoLog();
            
        String param = request.getParameter("param");
        
        String retorno = "<?xml version=\"1.0\"?>";

        try {
            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), "SATMOBEW",  param, logerro);
            tStart = System.currentTimeMillis();
            Persistencia persistencia = pool.getConexao(param);
//            if (ValidarUsuario.ValidaUsuario(codPessoa, senha, persistencia)) {
            List<Clientes> clientesAtuais = new ArrayList<>();
            BigDecimal maiorMenorDistancia = new BigDecimal("999999999");
            String latitudeMobile = request.getParameter("latitude");
            String longitudeMobile = request.getParameter("longitude");
            String codFil = request.getParameter("codfil");
            String busca = request.getParameter("busca");

            ClientesDao clientesDao = new ClientesDao();
            List<Clientes> clientes;
            if(null == busca) clientes = clientesDao.localizacaoClientesW(codFil, persistencia);
            else clientes = clientesDao.localizacaoClientesNred(codFil, busca, persistencia);
                
            if(param.equals("SATGLOVAL") || param.equals("SATPROSECUR")){
                for(Clientes cli : clientes){
                    /**
                     * Calcula distância entre o aparelho e os clientes
                     */
                    BigDecimal dist = GPS.distanciaCoordenadas(cli.getLatitude(), cli.getLongitude(), latitudeMobile, longitudeMobile);
                    cli.setLimite(dist.toString());
                    clientesAtuais.add(cli);
                }
                order(clientesAtuais);
            } else {
                for(Clientes cli : clientes){
                    /**
                     * Calcula distância entre o aparelho e os clientes
                     */
                    BigDecimal dist = GPS.distanciaCoordenadas(cli.getLatitude(), cli.getLongitude(), latitudeMobile, longitudeMobile);

                    /**
                     * Se haver menos que 10 clientes, adicionar na lista de clientes mais próximos de qualquer jeito.
                     */
                    if(clientesAtuais.size() < 10){
                        /**
                         * Salvando a distância em um campo qualquer para poder ordenar
                         */
                        cli.setLimite(dist.toString());
                        clientesAtuais.add(cli);
                        order(clientesAtuais);
                        maiorMenorDistancia = clientesAtuais.get(clientesAtuais.size() - 1).getLimite();
                    } else {
                        if(dist.compareTo(maiorMenorDistancia) == -1){
                            /**
                             * Adiciona o elemento, ordena e remove o último para a lista ter sempre o mesmo tamanho e recalcula a menor distância
                             */
                            cli.setLimite(dist.toString());
                            clientesAtuais.add(cli);
                            order(clientesAtuais);
                            clientesAtuais.remove(clientesAtuais.size()-1);
                            maiorMenorDistancia = clientesAtuais.get(clientesAtuais.size() - 1).getLimite();
                        }
                    }
                }
            }

            StringBuilder clis = new StringBuilder(), aux;
            for(Clientes cli : clientesAtuais){
                aux = new StringBuilder();
                aux.append(Xmls.tag("codfil", cli.getCodFil().toBigInteger()))
                        .append(Xmls.tag("nred", cli.getNRed()))
                        .append(Xmls.tag("ende", cli.getEnde()))
                        .append(Xmls.tag("codigo", cli.getCodigo()))
                        .append(Xmls.tag("lat", cli.getLatitude()))
                        .append(Xmls.tag("lon", cli.getLongitude()))
                        .append(Xmls.tag("dist", cli.getLimite()));
                clis.append(Xmls.tag("cliente", aux.toString()));
            }
            retorno += "<resp>1</resp>";
            retorno += Xmls.tag("data", clis.toString());
//            } else {
//                retorno += "<resp>2</resp>";
//            }
            resp.print(retorno);
            persistencia.FechaConexao();
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0)+" segundos.\r\n", "SATMOBEW", param, logerro); 
        }catch(Exception e){
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Falha ObterPostosLogin - " + e.getMessage(), "SATMOBEW", param, logerro);
            resp.print("<?xml version=\"1.0\"?><resp>0</resp>");
        }
    }
    
    private static void order(List<Clientes> clientes) {
        Collections.sort(clientes, (Object o1, Object o2) -> {
            int sComp;
            try{
                BigDecimal dist1 = ((Clientes) o1).getLimite();
                BigDecimal dist2 = ((Clientes) o2).getLimite();
                sComp = dist1.compareTo(dist2);
            } catch (Exception e){
                sComp = -1;
            }
            return sComp;
        });
    }
    
    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
