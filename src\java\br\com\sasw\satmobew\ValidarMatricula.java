package br.com.sasw.satmobew;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import static SasDaos.LoginDao.ValidarMatricula;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "ValidarMatricula", urlPatterns = {"/ValidarMatricula"})
public class ValidarMatricula extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;

    @Override
    public void init() {
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarMatricula.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");

        //define saida por texto
        response.setContentType("text/plain");
        PrintWriter out = response.getWriter();

        String param = request.getParameter("param");
        String matr = request.getParameter("matr");
        String senha = request.getParameter("senha");
        String codfil = request.getParameter("codfil");
        String secao= request.getParameter("secao");
        String dataAtual = request.getParameter("dataAtual");
        try {

            logerro = new ArquivoLog();

            String xml;

            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), matr, param, logerro);
            tStart = System.currentTimeMillis();

            Persistencia persistencia = pool.getConexao(param);

            if (null != persistencia) {

                if (ValidarMatricula(matr, senha, persistencia)) {
                    xml = "<?xml version=\"1.0\"?><resp>1</resp>"; // matrícula e senha válidos
                } else {
                    xml = "<?xml version=\"1.0\"?><resp>2</resp>"; // matrícula e senha não combinam                
                }

                persistencia.FechaConexao();

                //escreve a resposta no buffer de saida
                out.print(xml);

                Trace.gerarTrace(getServletContext(), this.getServletName(), xml, matr, param, logerro);

            } else {
                PrintWriter resp = response.getWriter();
                resp.print("<?xml version=\"1.0\"?><resp>mobilelogin_6</resp>");
            }

            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0) + " segundos.\r\n", matr, param, logerro);
        } catch (Exception e) {
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Falha - " + e.getMessage(), matr, param, logerro);
            //define saida por texto
            response.setContentType("text/plain");
            PrintWriter resp = response.getWriter();
            //escreve a resposta no buffer de saida
            resp.print("<?xml version=\"1.0\"?><resp>0</resp><erro>" + e.getMessage() + "</erro>");
        } finally {
            Trace.Erros(getServletContext(), request, logerro);
            out.close();
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

}
