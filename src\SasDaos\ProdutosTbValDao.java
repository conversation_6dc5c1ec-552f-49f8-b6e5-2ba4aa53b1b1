package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Produtos;
import SasBeansCompostas.ProdutosTbVal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ProdutosTbValDao {

    /**
     * Listar combustíveis cadastrados
     *
     * @param persistencia - conexão com banco de dados
     * @return - lista de produtos do tipo combustível
     * @throws Exception
     */
    public List<ProdutosTbVal> buscaCombustiveis(Persistencia persistencia) throws Exception {
        List<ProdutosTbVal> lProdutosTbVal;
        try {
            ProdutosTbVal produtosTbVal;
            Produtos produtos;
            // TbVal tbval;
            Consulta consult = new Consulta("Select Produtos.Codigo, Produtos.Descricao,"
                    + " Produtos.PrecoCusto, Produtos.TipoCombust, Produtos.UN from Produtos"
                    + " Left join tbVal TbVal22 on TbVal22.Tabela = 22"
                    + "                         And TbVal22.Codigo = Produtos.Categoria"
                    + " Where TbVal22.Descricao like 'COMBUST%'", persistencia);
            consult.select();
            lProdutosTbVal = new ArrayList();
            while (consult.Proximo()) {
                produtosTbVal = new ProdutosTbVal();
                produtos = new Produtos();
                // tbval = new TbVal();
                produtos.setCodigo(consult.getString("Codigo"));
                produtos.setDescricao(consult.getString("Descricao"));
                produtos.setPrecoCusto(consult.getString("PrecoCusto"));
                produtos.setTipoCombust(consult.getString("TipoCombust"));
                produtos.setUn(consult.getString("UN"));
                produtosTbVal.setProdutos(produtos);
                lProdutosTbVal.add(produtosTbVal);
            }
            consult.Close();
        } catch (Exception e) {
            lProdutosTbVal = null;
            throw new Exception("Falha ao executar buscaCombustiveis" + e.getMessage());
        }
        return lProdutosTbVal;
    }
}
