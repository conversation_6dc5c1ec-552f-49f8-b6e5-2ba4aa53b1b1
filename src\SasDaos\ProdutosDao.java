/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Produtos;
import SasBeans.PropServ;
import SasBeans.PropServItens;
import SasBeansCompostas.ProdutosPropProdServ;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class ProdutosDao {

    /**
     * Conta o número de produtos cadastrados no banco
     *
     * @param filtros
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer totalProdutosMobWeb(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "select count(*) total from produtos"
                    + " WHERE ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + "codigo IS NOT null";
            Consulta consult = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao contar produtos - \r\n" + e.getMessage());
        }
    }

    /**
     * Listagem paginada de produtos para o SatMobWeb
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Produtos> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<Produtos> retorno = new ArrayList();
        try {
            String sql = "SELECT  * "
                    + "FROM    ( SELECT    ROW_NUMBER() OVER ( ORDER BY descricao ) AS RowNum, * "
                    + "          FROM      produtos "
                    + " WHERE ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + "codigo IS NOT null) AS RowConstrainedResult "
                    + "WHERE   RowNum >= ?"
                    + "    AND RowNum < ? "
                    + "ORDER BY RowNum";
            Consulta consult = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.setInt(primeiro + 1);
            consult.setInt(primeiro + 1 + linhas);
            consult.select();
            Produtos produto;
            while (consult.Proximo()) {
                produto = new Produtos();
                produto.setCodigo(consult.getString("codigo"));
                produto.setDescricao(consult.getString("descricao"));
                produto.setAplicacao(consult.getString("aplicacao"));
                produto.setPrecoVenda(consult.getString("PrecoVenda"));
                produto.setObs(consult.getString("obs"));
                produto.setIndServico(consult.getString("IndServico"));
                produto.setMargem("1");
                produto.setOperador(consult.getString("operador"));
                produto.setDt_Alter(consult.getString("dt_alter"));
                produto.setHr_Alter(consult.getString("hr_alter"));
                retorno.add(produto);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar pesquisa de produtos - \r\n" + e.getMessage());
        }
    }

    /**
     * Busca o próximo número de código
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String getMaxCodigo(Persistencia persistencia) throws Exception {
        try {
            String retorno = new String(), sql = "Select isnull(MAX(codigo),0)+1 codigo "
                    + " FROM produtos ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                retorno = consult.getString("codigo");
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar max codigo de produtos - \r\n" + e.getMessage());
        }
    }

    /**
     * Insere um novo produto
     *
     * @param produto
     * @param persistencia
     * @throws Exception
     */
    public void inserirProduto(Produtos produto, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO Produtos (codigo, descricao, aplicacao, precovenda, obs, Operador, Dt_Alter, Hr_Alter) "
                    + "VALUES(?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(produto.getCodigo());
            consulta.setString(produto.getDescricao());
            consulta.setString(produto.getAplicacao());
            consulta.setBigDecimal(produto.getPrecoVenda());
            consulta.setString(produto.getObs());
            consulta.setString(produto.getOperador());
            consulta.setString(produto.getDt_Alter());
            consulta.setString(produto.getHr_Alter());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao inserir produto - \r\n" + e.getMessage());
        }
    }

    /**
     * Edita um produto
     *
     * @param produto
     * @param persistencia
     * @throws Exception
     */
    public void atualizarProduto(Produtos produto, Persistencia persistencia) throws Exception {
        try {
            String sql = "update Produtos  set descricao = ?, aplicacao = ?, precovenda = ?, obs = ?,"
                    + " Operador = ?, Dt_Alter = ?, Hr_Alter = ?"
                    + " where codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(produto.getDescricao());
            consulta.setString(produto.getAplicacao());
            consulta.setBigDecimal(produto.getPrecoVenda());
            consulta.setString(produto.getObs());
            consulta.setString(produto.getOperador());
            consulta.setString(produto.getDt_Alter());
            consulta.setString(produto.getHr_Alter());
            consulta.setBigDecimal(produto.getCodigo());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar um produto - \r\n" + e.getMessage());
        }
    }

    /**
     * Lista os produtos e serviços de uma proposta como 'Produtos'..
     *
     * @param numero
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Produtos> listaProdutosPropostas(BigDecimal numero, BigDecimal codFil, Persistencia persistencia) throws Exception {
        List<Produtos> retorno = new ArrayList<>();
        try {
            String sql = "select produtos.descricao, produtos.aplicacao, produtos.indservico, propprod.* "
                    + " from propprod "
                    + " left join produtos on produtos.codigo = propprod.codpro"
                    + " where propprod.numero = ? and propprod.codfil = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(numero);
            consulta.setBigDecimal(codFil);
            consulta.select();
            Produtos produto;
            while (consulta.Proximo()) {
                produto = new Produtos();
                produto.setDescricao(consulta.getString("descricao"));
                produto.setAplicacao(consulta.getString("aplicacao"));
                produto.setCodigo(consulta.getBigDecimal("codpro"));
                produto.setPrecoVenda(consulta.getBigDecimal("valorun"));
                produto.setDt_Alter(consulta.getString("dt_alter"));
                produto.setHr_Alter(consulta.getString("hr_alter"));
                produto.setOperador(consulta.getString("operador"));
                produto.setQtde(consulta.getBigDecimal("qtde"));
                produto.setIndServico(consulta.getString("IndServico"));
                produto.setPrecoCusto(consulta.getBigDecimal("custoun"));
                try {
                    produto.setMargem(produto.getPrecoVenda().divide(produto.getPrecoCusto(), 5, RoundingMode.HALF_UP));
                } catch (Exception e) {
                    produto.setMargem(BigDecimal.ONE);
                }
                produto.setQtde(consulta.getBigDecimal("qtde"));
                retorno.add(produto);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Failed to list itens de propostas - " + e.getMessage());
        }
    }

    public List<ProdutosPropProdServ> listarItensProposta(BigDecimal numero, BigDecimal codfil, Persistencia persistencia) throws Exception {
        List<ProdutosPropProdServ> retorno = new ArrayList<>();
        try {
            String sql = "select produtos.descricao, produtos.aplicacao, produtos.indservico, propprod.* "
                    + " from propprod "
                    + " left join produtos on produtos.codigo = propprod.codpro"
                    + " where propprod.numero = ? and propprod.codfil = ?";// and produtos.indservico <> 'S'";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(numero);
            consulta.setBigDecimal(codfil);
            consulta.select();
            ProdutosPropProdServ item;
            Produtos produto;
            PropServ servico;
            PropServItens servicoItem;
            List<PropServItens> servicoItens;
            BigDecimal id = BigDecimal.ONE;
            while (consulta.Proximo()) {
                item = new ProdutosPropProdServ();
                produto = new Produtos();
                produto.setDescricao(consulta.getString("descricao"));
                produto.setAplicacao(consulta.getString("aplicacao"));
                produto.setCodigo(consulta.getBigDecimal("codpro"));
                produto.setPrecoVenda(consulta.getBigDecimal("valorun"));
                produto.setDt_Alter(consulta.getString("dt_alter"));
                produto.setHr_Alter(consulta.getString("hr_alter"));
                produto.setOperador(consulta.getString("operador"));
                produto.setQtde(consulta.getBigDecimal("qtde"));
                produto.setIndServico(consulta.getString("IndServico"));
                produto.setPrecoCusto(consulta.getBigDecimal("custoun"));
                try {
                    produto.setMargem(produto.getPrecoVenda().divide(produto.getPrecoCusto(), 5, RoundingMode.HALF_UP));
                } catch (Exception e) {
                    produto.setMargem(BigDecimal.ONE);
                }
                produto.setQtde(consulta.getBigDecimal("qtde").toBigInteger().toString());
                item.setProduto(produto);
                item.setId(id);
                retorno.add(item);
                id = id.add(BigDecimal.ONE);
            }
            consulta.Close();

            sql = "select ordem, descricao, codFreq from propserv where numero = ? and codfil = ?";
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(numero);
            consulta.setBigDecimal(codfil);
            consulta.select();
            while (consulta.Proximo()) {
                item = new ProdutosPropProdServ();
                servico = new PropServ();
                produto = new Produtos();
                produto.setDescricao(consulta.getString("descricao"));
                produto.setIndServico("*");
                produto.setCodigo("0");
                servico.setCodFreq(consulta.getBigDecimal("codFreq"));
                servico.setOrdem(consulta.getBigDecimal("ordem"));

                String sqlItens = "select PropServItens.descricao, propservitens.valor,"
                        + " case propservitens.tipocalc "
                        + "     when 'M' then 'Por mês' "
                        + "     when 'D' then 'Por dia' "
                        + "     when 'H' then 'Por hora' "
                        + "     when 'Q' then 'Por qtde' "
                        + "     when 'I' then 'Índice s/valor' "
                        + " end TipoCalc "
                        + " from PropServItens "
                        + " where PropServItens.numero = ? and PropServItens.codfil = ? and PropServItens.ordem = ?";
                Consulta consultaItens = new Consulta(sqlItens, persistencia);
                consultaItens.setBigDecimal(numero);
                consultaItens.setBigDecimal(codfil);
                consultaItens.setBigDecimal(servico.getOrdem());
                consultaItens.select();
                servicoItens = new ArrayList<>();
                while (consultaItens.Proximo()) {
                    servicoItem = new PropServItens();
                    servicoItem.setTipoCalc(consultaItens.getString("TipoCalc"));
                    servicoItem.setDescricao(consultaItens.getString("descricao"));
                    servicoItem.setValor(consultaItens.getBigDecimal("valor"));
                    servicoItens.add(servicoItem);
                }
                item.setProduto(produto);
                item.setPropServ(servico);
                item.setPropServItens(servicoItens);
                item.setId(id);
                retorno.add(item);
                id = id.add(BigDecimal.ONE);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Failed to list itens de propostas - " + e.getMessage());
        }
    }
}
