/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satmobew;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeansCompostas.PstServTipoPosto;
import SasDaos.PstServDao;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import Xml.Xmls;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.List;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "ObterPostosCliente", urlPatterns = {"/ObterPostosCliente"})
public class ObterPostosCliente extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;
    
    @Override
    public void init(){
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

     /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
    
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/plain");
        PrintWriter out = response.getWriter();
        logerro = new ArquivoLog();
            
        String param    = request.getParameter("param");
//        String senha    = request.getParameter("senha");
        String codfil   = request.getParameter("codfil");
        String cliente  = request.getParameter("cliente");
//        String codPessoa    = request.getParameter("codpessoa");
//        String nomepessoa   = request.getParameter("nomepessoa");
        
        String retorno = "<?xml version=\"1.0\"?>";

        try {
            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), "SATMOBEW",  param, logerro);
            tStart = System.currentTimeMillis();
            Persistencia persistencia = pool.getConexao(param);
//            if (ValidarUsuario.ValidaUsuarioEW(codPessoa, nomepessoa, senha, persistencia)) {                
                PstServDao pstServDao = new PstServDao();
                List<PstServTipoPosto> postos = pstServDao.listarPostosClienteSatMobEW(codfil, cliente, persistencia);
                
                StringBuilder pst = new StringBuilder(), aux;
                for(PstServTipoPosto posto : postos){
                    aux = new StringBuilder();
                    aux.append(Xmls.tag("codfil", posto.getPstServ().getCodFil().toBigInteger()))
                            .append(Xmls.tag("secao", posto.getPstServ().getSecao()))
                            .append(Xmls.tag("local", posto.getPstServ().getLocal()))
                            .append(Xmls.tag("ende", posto.getPstServ().getEndereco()))
                            .append(Xmls.tag("descricao", posto.getPstServ().getDescContrato()));
                    pst.append(Xmls.tag("pstserv", aux.toString()));
                }
                retorno += "<resp>1</resp>";
                retorno += Xmls.tag("data", pst.toString());                
//            } else {
//                retorno += "<resp>2</resp>";
//            }
            out.print(retorno);
            persistencia.FechaConexao();
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0)+" segundos.\r\n", "SATMOBEW", param, logerro); 
        }catch(Exception e){
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Falha ObterPostosLogin - " + e.getMessage(), "SATMOBEW", param, logerro);
            out.print("<?xml version=\"1.0\"?><resp>0</resp>");
        }
    }
    
    
    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
