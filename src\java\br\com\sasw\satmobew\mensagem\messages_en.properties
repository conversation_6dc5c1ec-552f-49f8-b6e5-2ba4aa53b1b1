idioma=ingl\u00eas
RelatorioEvento=Event Report
Detalhes=Details
Posto=Station
Ocorrencia=Event
Data=Date
Hora=Time
Foto=Photo
Fotos=Photos
ImagemNaoEncontrada=Image not found
GirarEsquerda=Turn Left
GirarDireita=Turn Right
Mapa=Map
ErroMapa=Error showing the map
MensagemUrl=Email not displaying correctly? View it in your browser.
Relatorio=Report
MensagemVideo=Your browser does not support the video tag.
Inspecao=Inspection
RelatorioInspecao=Inspection Report
Portugues=Portuguese
Ingles=English
Salve=Save
Nnacionalidade=Nationality
Titulo=JSF
Senha=Password
Saudacao=Welcome to SatMob
Manual=Manual
PrimeiroAcesso=First access
ValidarCC=Validate paycheck
CandidateSe=Apply
EsqueciSenha=I forgot the password
AcessoSistema=System access:
Espanhol=Spanish
Entrar=Login
Voltar=Back
Empresa=Company
SelecioneEmpresa=Select a Company
Usuario=User
FolhaPonto=Point Sheet
ContraCheque=Paycheck
TrocarSenha=Change Password
SelecionePeriodo=Select a Period
SelecioneData=Select a Date
DigiteSenha=Enter a Password
DigiteSenhaNovamente=Enter the Password Again
Inicio=Home
PropostaEmprego=Job Offer
SenhaAtual=Current Password
NovaSenha=New Password
confirmarNovaSenha=Confirm New Password
RecuperarSenha=Recover Password
FalhaCarregarEmpresas=Failed to load Companies
UsuarioPermissaoAcessarFiliais=User not allowed to access branches
UsuarioPermissaoContateResponsavelSistema=User without permission, please contact your system
UsuarioInativo=Inactive User
SenhaIncorreta=Wrong Password
EmailInvalido=Invalid Email
dadosNaoConfereTenteNovamente=Data does not match, try again
emaiInexistenteUsuarioInativo=Email nonexistent, or Invalid user
SenhaAlteradaSucesso=Password successfully changed
SenhaAtualNaoConfere=Current password does not match
camposSenhaConfirmarSenhaPrecisamIguais=Fields: Password and Confirm Password must match
SenhaCadastradaSucesso=Password registered successfully
UsuarioPossuiAcessoFavorRecuperarSenha=User already has access, please recover password
CodigoPreenchidoErro=Wrong code
Filial=Branch
SelecioneFilial=Select a Branch Office
SemSenha=No password, click on First Access
Cagedonline=CAGED Online
Caged=CAGED
RecursosHumanos=Humam Resources
FGTS=FGTS
SeguroDesemprego=Unemployment Insurance
Tesouraria=Treasury
Cofre=Vault
CadastrarPessoa=People Registration
Idioma=Language
UsuarioEmpresa=User/Companies
Fechar=Close
Adicionar=Add
Gerar=Generate
Novo=New
AlterarSenha=Change password
ExcluidoSucesso=Successfully deleted
UsuarioSenha=Users/Passwords
UsuarioFiliais=User/Branches
Filtrar=Filter
RemoveFiltro=Remove
login.falhageral<message>login.usuarioerrado=User Not Found
login.usuariosemacessocadastrado=User Without Access
Operacoes=Operations
Configuracoes=Configuration
Cadastros=Registration
Relatorios=Reports
Sair=Logout
EscalaDia=Day Scale
Supervisao=Supervision
Pessoas=People
Funcionarios=Employees
Clientes=Clients
PostoServicos=Service Sites
Filiais=Branches
Usuarios=Users
Importador=Importer
Exportador=Exporter
CodFil=Branch
Secao=Code
CodCli=CliCode
Local=Location
Situacao=Situation
Dt_Situacao=Dt Situation
InterfExt=InterfExt
Operador=Operator
Dt_Alter=Dt_Alter
Hr_Alter=Hr_Alter
Cadastrar=Register
Editar=Edit
PstServ=Service Sites
Funcion=Funcion\u00e1rios
Matr=Registry
Nome=Name
CPF=SSN
RG=ID
Dt_Nasc=Dt_Birth
Dt_Admis=Dt_Admis
CodPessoaWeb=WebPersonCode
Insc_Munic=County Registration
Excluir=Delete
arquivoCliente=Select the kind of client
Obrigatorio=Missing mandatory field 
ValorAtual=Current value
NovoValor=New value
Nome_Guer=Nickname
Posto=Site
Dt_Situac=Dt_Situac
NRed=ShortName
Ende=Address
Bairro=District
Cidade=City
Estado=State
CEP=ZIPCode
Fone1=Phone1
Fone2=Phone2
CGC=FEIN
IE=IE
Latitude=Latitude
Codigo=Code
Longitude=Longitude
Pesquisar=Search
Cliente=Client
Data=Date
Hora=Time
Supervisoes=Supervisions
Detalhes=Details
Distancia=Distance
Historico=History
TipoPosto=Type
Resp=Answer
Qst=Question
Descricao=Description
NomeCliente=Client's Name
TipoPostoDesc=Type Description
RazaoSocial=Company Name
Cadastro=Registration
UF=ST
QtdEntrevistas=Number of Interviews
Qtd=#INTW
SupervisoesRecentes=Recent Supervisions
Entrevistas=Interviews
Checklist=Checklist
Galeria=Galery
Funcion=Employee
Supervisor=Supervisor
Horario=Time
Agencia=Agency
NivelOP=OPLevel
NomeCompleto=Full Name
Motivo=Reason
Grupo=Group
Confirmacao=Confirmation
Pessoa=Person
Grupos=Groups
Permissoes=Permissions
Nivel=Level
Login=Login
Operacao=Operation
Manutencao=Maintenance
Gerencia=Management
Administrador=Administrator
Ativo=Active
Bloqueado=Blocked
RGOrg=Department
Obs=Observation
Funcao=Function
Sexo=Gender
Sistema=System
Inclusao=Inclusion
Alteracao=Change
Exclusao=Exclusion
Altura=Height (cm)
Candidato=C - Candidate
Prestador=P - Service provider
Autonomo=A - Autonomous
Funcionario=Employee
Diretor=D - Director
Socio=S - Partner
Visitante=V - Visitor
BBloqueado=B - Blocked
Visitanteweb=W - Web visitor
Selecione=Select
Masculino=Male
Peso=Weight
Feminino=Female
OOutros=O-Not Declared
CadastrarCliente=Customer registration
PessoaJuridica=Company
PessoaFisica=Person
EditarFuncion=Edit Staff
EditarFilial=Edit Branch
CadastrarFuncion=Employee Registration
CadastrarCliente=Client Registration
EditarPessoa=Edit People
EditarCliente=Edit Client
EditarUsuario=Edit User
CadastrarUsuario=User Registration
CadastrarFilial=Branch Registration
QtdFotos=Number of Photos
Endereco=Address
RotasSup=Supervision Routes
Rota=Route
Hr_Largada=HrStart
Hr_Chegada=HrArrival
Hr_IntIni=HrIntIni
Hr_IntFim=HrIntEnd
Hr_Total=Total Hs
Viagem=Journey
ATM=ATM
Bacen=Treasury
Aeroporto=Airport
Sequencia=Sequence
Flag_Excl=Flag_Excl
TpVeic=Type Vehicle
Intervalo=Interval
Selecionar=Select
Parada=Stop
Hora1=Hour1
ER=ER
CodCli1=CliCode1
Regiao=Region
CodCli2=CliCode2
DPar=DPar
Hora1d=Hour1D
Valor=Amount
Pedido=Request
OperIncl=OperIncl
Dt_Incl=Dt_Incl
Hr_Incl=Hr_Incl
OperExcl=OperExcl
Dt_Excl=Dt_Excl
Hr_Excl=Hr_Excl
Trajetos=Paths
Calcular=Calculate
CadastrarRota=Route Registration
FotosLocal=Photos of the Location
Tipo=Type
Mapa=Map
Contrato=Contract
#Trajeto
CadastrarTrajeto=Path Registration
AtualizarCEP=Would you like to update the address?
Sim=Yes
Nao=No
QtdPostos=Number of Sites
CadastrarPstServ=Service Site Registration
PesquisarPstServ=Site Search
QtdSupervisoes=Number of Supervisions
SelecionePstServ=Select a Sevice Site
SemSupervisaoRecente=No forward supervisions
SemSupervisaoAntiga=No previous supervisions
SelecioneSupervisao=Select a Supervision
SupervisaoAnterior=Previous Supervision 
SupervisaoPosterior=Next Supervision
QtdUsuarios=Number of Users
QtdPessoas=Number of People
QtdClientes=Number of Clients
QtdFuncion=Number of Employees
QtdRotas=Number of Routes
AdicionarFilial=Add Branch
Email=Email
SenhaFraca=Weak password
SenhaBoa=Good password
SenhaForte=Strong password
SenhaSomenteNumerosMinimoSeis=The password must have only numbers and at least 6 digits
Corporativo=Corporative
PesquisarFuncion=Employee Search
TbVal=Questions
QtdTbVal=Number of Questions
QtdFiliais=Number of Branches
MostrarTodasFiliais=Show all branches
SomenteAtivos=Active Only
PesquisarCliente=Client Search
PesquisarPessoa=Person Search
PesquisarUsuario=User Search
OK=OK
CompletarEndereco=Automatic address, please complete with addtional information.
Aviso=Warning
desejaSeguir=Proceed with this action?
SemEnderecoCadastrado=No Registered Address
EdicaoSucesso=Successful Changes
CadastroSucesso=Successful registration
SelecioneCliente=Select a Client
SelecioneFuncion=Select an Employee
SelecioneSituacao=Select a Situation
SelecionePessoa=Select a Person
SelecionePermissao=Select a Permission
SelecioneUsuario=Select an User
CPFInvalido=Invalid SSN
FuncionarioCadastrado=Employee Already Registered
MatriculaInvalida=Invalid Registry
EnderecoNaoEncontrado=Address Not Found
CNPJInvalido=Invalid FEIN
ClienteNaoEncontrado=Client not found
PessoaInvalida=Invalid Person
tipoServ=Service type
filialRota=Branch of the route
ImpossivelConectarSatellite=Not abble to connect to Satellite
LimparFiltros=Remove Filters
buscaMovimentacao=Movimenta\u00e7\u00f5es
login.falhageral<message>login.senhaerrada=Wrong Password
ImpossivelConectarBanco=Not abble to connect to
AlturaSemVirgula=Comma not allowed
PesoSemVirgula=Comma not allowed
filialRota=Branch of the route
secao=Section
AdicionarPermissoesGrupo=Add all permissions to the group
javax.faces.validator.RegexValidator.NOT_MATCHED=The password must contain only numbers and at least 5 characters
primefaces.password.INVALID_MATCH=Password don't match
QtdPermissoes=Number of Permissions
SemRegistros=No Records Found
ExibirSenha=Show password
Placa=License plate
Hora2=Hour2
Hora3=Hour3
Hora4=Hour4
hs_interv=Hs_Break
QtdEscalas=Number of Scales
CadastrarEscala=Scale Registration
Periodo=Period
PesquisarFilial=Branch Search
FilialInvalida=Invalid Branch
senhaMobile=Mobile password
permissaoRotas=Routes permissions
ProximaFoto=Next Photo
Foto=Photo
De=out of
FotoAnterior=Previous Photo
SemMaisFotosInicio=No More Photos
SemMaisFotosFim=No More Photos
NaoSeAplica=Not applicable
Concluido=Done
Pendente=Pending
EmAndamento=In progress
Demitido=Fired
Inativo=Inactive
PesquisarSupervisao=Supervision Search
DataInvalida=Invalid Date
a=to
numero=Number
SelecioneRota=Select a Route
SelecioneTrajeto=Select a Path
ExclusaoSucesso=Successfully deleted
ExcluirRota=Delete this route?
ExcluirTrajeto=Delete this path?
VerifiqueHorario=Check the time
SemRotas=There are no routes avaliable
numero=Number
Escala=Scale
excluirEscala=Deleting the scale will make you lose all the information about it 
naoListar=It was not possible to find the data
Assinado=Signed 
Guia=Receipt
listaguaistitulo=Receipt list
Volume=Volume
ExcluirEscala=Delete this scale?
SelecioneEscala=Select a Scale
QtdGuias=Number of Receipts
Serie=Series
TempoEspera=Waiting time
EscalaExistente=Scale already exists
RotaInexistente=This route is missing for this date
CadastrarQuestoes=Questions Registration
CadastrarQuestao=Question Registration
Questoes=Questions
SelecioneQuestao=Select a Question
ExcluirQuestao=Delete this question?
ExisteServico=There is a job already at this hour
HoraInvalida=The hour typed is invalid
IntervaloInvalido=The interval typed is invalid
GTV=Eletronic Receipt
DiaSemana=Day of the week
$=US$
Segunda=Monday
Ter\u00e7a=Tuesday
Quarta=Wednesday
Quinta=Thursday
Sexta=Friday
Sabado=Saturday
Domingo=Sunday
DetalhesGuia=Receipt details
Lacre=Seal
Guias=Receipts
SelecioneGuia=Select a Receipt
AdicionarCliente=Add a Client
trajetoExcluido=Path already deleted
exclusaoVisivel=Visible exclusions
escalaExcluida=Scale deleted
VerTodos=See all
DataInicial=Start Date
DataFinal=End Date
Hr_Saida=HrDeparture
EscolherOpcao=Select an option
TrocarCliente=Switch Clients
Cedulas=Bank Note
Cheques=Bank Check
Moedas=Coins
MetaisPreciosos=Precious Metal
MoedaExtrangeira=Foreign Currency
Outros=Others
Exportar=Export
AssistenciaTecnica=Technical Assistance
Entrega=Delivery
Recolhimento=Pick up
ExibirExcluidos=Show deleted
escolhaTipoArquivo=Choose the type of the file
pdf=PDF
xls=XLS
html=HTML
xml=XML
nomeArquivo=File name
erroExportar=Error exporting
pagina=Page
de=of
Origem=Origin
Destino=Destination
QtdVolumes=Number of Volumes
ValorGuias=Total Value of Receipts
SATELLITE=SATELLITE
SASW=SASW
SAS=SAS
SATLOYAL=LOYAL
SATTRANSVIG=TRANSVIG
SATINVLMT=INVIOLAVEL MT
CONFEDERAL=TRANSFEDERAL
CONFEDERALGO=TRANSFEDERAL GO
SATCONFEDERALBSB=TRANSFEDERAL
SATCONFEDERALGO=TRANSFEDERAL GO
SATCORPVSPE=CORPVS PE
SATTRANSVIP=TRANSVIP
SATPRESERVE=PRESERVE
VSG=VSG
SATTSEG=TECNOSEG
SATAGIL=AGIL
AGILSERV=AGIL SERV
SATAGILVIG=AGIL VIG
SATAGILCOND=AGIL COND
SATINVLRS=INVIOLAVEL RS
SASEX=SASEX
SATGSI=GSI
SATTRANSEXCEL=TRANSEXCEL
SATRODOB=RODOBAN
SATRODOBAN=RODOBAN
SATTAMEME=TAMEME
SATCOMETRA=COMETRA
SATSASEX=SASEX
EAGSATI=EAGLE
EAGSAS=EAGLE
CofreInteligente=Smart Vault
TotalCreditos=Day Total Credits
CreditoDia=Day Credit
CreditoProxDU=Next day credit
ChefeEquipe=Messenger
ValorRecolhido=Valor Recolhido
SaldoCustoDia=Saldo Custo Dia
SaldoCofre=Saldo Cofre
TotalVlrRecolhido=Total Valor Recolhido
TotalSalCustDia=Total Saldo Custo Dia
TotalSaldoCofre=Total Saldo Cofre
SelecioneCofre=Select a Vault
Feriado=Holiday
ValorRecD0=Valor Rec D0
HoraRecD0=Hora Rec D0
DepDiaAntAposCorte=Dep Dia Ant Ap\u00f3s Corte
ValorCorteD0=Valor Corte D0
TotalCredDia=Total Cred Dia
ValorRecDia=Valor Rec Dia
ValorRecJaCreditado=Valor Rec j\u00e1 Creditado
ValorRecACreditar=Valor Rec \u00e0 Creditar
SaldoCofreTotal=Saldo Cofre Total
DepositoJaCreditado=Dep\u00f3sito j\u00e1 Creditado
DepositoProxDU=Dep\u00f3sito Prox DU
SaldoFisCst=Saldo Fis Cst
SenhaDia=Type the day password 
Mostrando=Showing
SenhaSucesso=Password updated! Please check your email inbox.
Arquivo=File
Cancelar=Cancel
Enviar=Upload
ArrasteArquivo=Drag the file or search
ApenasXLS=Only the template file
QtdArquivosInvalida=Number of files exceeded
SATCORPVS=CORPVS
Buscar=Search
ConfirmarSaida=Are you sure you want to quit? All unsaved progress will be lost.
SelecioneArquivo=Select a File
PossuoCodigo=I Have a Code
SatMOB=SatMOB
Template=Template
UsuarioSemAutorizacao=User without authorization. To change the password, contact your system administrator.
#tradu\u00e7\u00e3o do google translate
DicaDownload=Download the template file, fill in the values of the fields and upload it to import the new value.
DadosOrigem=Sender's Info
Remetente=Sender
Veiculo=Vehicle
Chegada=Arrival
Saida=Departure
DadosDestino=Recipient's Info
Destinatario=Recipient
DiscriminacaoValorIdentificacao=Discrimination, value and identification of cargo
ValorDeclarado=Declared value
IdentificacaoMalote=Pouch Identification
Lacres=Seals/Bag Serial
Composicoes=Compositions
TermoAssGuia=We receive the volumes quoted in this Receipt, declare and acknowledge as deliveries without a trace of violation, in perfect conditions, and especially intact the respective security seals - numbered seals/bag serial described.
AssRemetente=Sender's Signature
AssDestinatario=Recipient's Signature
InscEstadual=State Registration
OS=OS
GETV=ELETRONIC MANIFEST
Telefone=Phone Number
QtdParadas=Number of Stops
CamposExportacao=Mark the fields to export
CodPessoa=PersonCode
Postos=Sites
Imprimir=Print
FuncaoIndisponivel=Unavailable Option
Opcoes=Options
ImportacaoCompleta=Import Done
ArquivoInvalido=Invalid File
ImportacaoCompletaComErros=Import complete with errors
ImportadosSucesso=successfully imported
ImportadasSucesso=successfully imported
ErrosImportacao=Import errors
Erros=Erros
ArrasteAqui=Drag and drop the file here
Escalas=Scales
ViaImportacao=Entry by import
BuscarPeriodo=Search by Period
Cofres=Safes
BemVinda=Welcome
FolhaDePonto=Time Sheet
Contracheque=Paycheck
FuncoesAdm=Administrative Tools
AssinarGTV=Sign GTV-e
PortalRH=HR Portal
Matricula=Registration Number
Mensagens=Messages
ListaDatas=List Dates
BemVindo=Welcome
RGn=RG (only digits)
CPFn=CPF (only digits)
Acesso1=1st Access
Dt_Nascn=Birthday (dd/MM/aaaa)
CidadeRes=City of residence
Contatos=Contacts
QtdContatos=Number of Contacts
NomeFantasia=Fantasy Name
PesquisarContato=Contact Search
CadastrarContato=Contact Registration
IndicacaoCliente=Customer Indication
HrRecDia=Hr Rec Dia
IndicacaoFornecedor=Supplier Indication
Propaganda=Ads
JornaisRevistas=News and Magazines
Internet=Internet
ProspeccaoComercial=Commercial Prospecting
ContatoViaSac=Customer Service
CapturaMobile=Mobile Capture
TpCli=Client Type
PAB=PAB
TA=TA
TAC=TAC
OutrasTransp=Other Companies
Prospect=Prospect
AguardandoContato=Awaiting Contact
ApresentacaoServicos=Presentation of Services
Negociacao=Negotiation
PilotoServicos=In Service Pilot
FormalizacaoContratual=On Contract Formalization
ClienteAtivo=Active Client
Contato=Contact
login.senhaerrada=Wrong Password
SemFolhasDePonto=No Time Sheets
RH=Human Resources
InformeRendimentos=Income Report
DadosPessoais=Personal Data
Documentos=Files
Dados=Data
CodigoInvalido=Invalid Code
Comercial=Comercial
Arquivos=Files
ModificadoEm=Modified in
ExcluirDocumento=Delete this document?
CadastrarProdutos=Products Registration
Aplicacao=Application
Preco=Price
Produtos=Products
QtdProdutos=Number of Products
PesquisarProduto=Product Search
SelecioneProduto=Select a Product
Numero=Number
Propostas=Proposals
QtdPropostas=Number of Proposals
Referencia=Reference
Consultor=Consultant
Validade=Expiration
DtValidade=Expiration Date
ProdutosSelecionados=Selected Products
Status=Status
Tickets=Tickets
Responsavel=Responsible
Fazer=To Do
FilaPTeste=Test Queue
Teste=Test
Implantar=Implant
Feito=Done
Desenvolver=Develop
CadastrarTicket=Ticket Registration
QtdTickets=Number of Tickets
Ordem=Sequence
PesquisarTickets=Tickets Search
MarcarDesmarcar=Check/Uncheck All
Ticket=Ticket
CadastrarProposta=Proposal Registration
ValorParcial=Partial Value
Produto=Product
Quantidade=Quantity
ArrasteOuAdicione=Drag and Drop or hit the + button to add an item
FormasPagamento=Form of Payment
Comentarios=Comments
AdicionarComentario=Add a Comment
SalveAlteracoes=Save your changes before proceeding.
Descontos=Discounts
ResumoProposta=Proposal Summary
Total=Total
Subtotal=Subtotal
CustosAdicionaisDescontos=Additional costs / Discounts
SemFoneContato=No phone # registered.
PrecoAjustado=Adjusted Price
MAY=MAY
APRIL=APRIL
JANUARY=JANUARY
FEBRUARY=FEBRUARY
MARCH=MARCH
JUNE=JUNE
JULY=JULY
AUGUST=AUGUST
SEPTEMBER=SEPTEMBER
OCTOBER=OCTOBER
NOVEMBER=NOVEMBER
DECEMBER=DECEMBER
SATQUALIFOCO=QUALIFOCO
PrazoEntregaImplantacao=Deliver Time/Implantation
Garantia=Warranty
Outro=Other
Opcao=Option
CondicoesPagamento=Payment Conditions
DeveConter=must contain
Digitos=digits
SenhaJaCadastrada=Password already registered.
SenhaInvalida=Invalid password. Try a different one
SenhaNumerica6Digitos=New password must contain only numbers and 6 digits.
DigiteASenha=Type your passowrd
SelecioneTicket=Select a Ticket
ExportarProposta=Export Proposal
Modelo=Model
NomeArquivo=File Name
ArquivoNaoEncontrado=File not found
SenhaEnviadaPara=Password sent to
NaoPossuoSenhaDia=I don't have daily password
EmpresaMatricula=company@registration#
UsuarioEmailInvalio=The registered email is not valid. Please contact support.
MatriculaAcessouSistema=This registration number has already accessed the system. Log in with company@registration# and usual password.
ErroValidacao=Validation error. Try again.
Frequencia=Frequency
Faturamento=Faturamento
DataCompetencia=Data de Compet\u00eancia
ValorDeposito=Valor Dep\u00f3sito
TipoDeposito=Tipo Dep\u00f3sito
Entradas=Entradas
ConfirmarEdicaoTicket=Deseja realmente mudar o status do ticket?
SATINTERFORT=INTERFORT
SATGLOVAL=GLOVAL
ClienteP=Customer
RotasValores=CIT Routes
Autenticacao=Authentication
ValorTotal=Total Amount
HistoricoObrigatorio=Todo ticket deve ter um hist\u00f3rico detalhando a descri\u00e7\u00e3o!
AdicionarContato=Adicionar Contato
MeusTickets=Meus Tickets
Notas=Notes
Hora1D=Hr1D
SATIBL=IBL
Ambiente=Ambiente
ExportarESocial=Exportar eSocial
Evento=Event
Homologacao=Homologa\u00e7\u00e3o
Producao=Produ\u00e7\u00e3o
ESocial=ESocial / Reinf
CadastrarESocial=Cadastrar eSocial
CadastroInicial=Cadastro Inicial
XMLEnvio=XML de Envio
XMLRetorno=XML de Retorno
ProtocoloEnvio=Protocolo de Envio
InicioValidade=Validity Start
Dt_Envio=Data de Envio
Hr_Envio=Hora de Envio
Dt_Retorno=Data de Retorno
Hr_Retorno=Hora de Retorno
Eventos=Events
Certificado=Certificado
QtdEventos=Number of Events
Resposta=Answer
GerarXML=Gerar XML
ConsultarRetorno=Consultar Retorno
ConsultaPendentes=Consultar Pend\u00eancias
VerificarProcessamento=Verificar Processamento
Dt_UltimoEnvio=Data \u00daltimo Envio
Prosseguir=Prosseguir
Emails=Emails
Para=To
Assunto=Subject
DeRemetente=From
Mensagem=Message
ReenviarEmail=Send Email Again
SelecioneEmail=Select an Email
EnviarEmail=Send Email
ManifestoPara=Send manifest to
EmailFilaEnvio=Email in queue to be sent
Batidas=Check ins/Check outs
Cargo=Position
SenhasNaoConferem=Passwords don't match
Fotos=Photo
SelecioneRelatorio=Select a Report
SATPROSECUR=PROSECUR
SatMobEW=SatMobEW
MatrAut=Automatic Registration
SATSERVITE=SERVITE
NomeExisteSaspw=Username already in use
NomeExisteFuncion=Employee username already in use
PstDepen=Dependencies
SATCOGAR=COGAR
Tag=Tag
Batida=Check Ins
Dependencia=Dependency
Rondas=Tours
Relatorio=Report
Vigilante=Officer
Ocorrencia=Event
BuscarData=Search by Date
UsuarioNaoAutorizado=Usu\u00e1rio N\u00e3o Autorizado
ValorTotalRotas=Total amount of all routes:
Paradas=Stops
Volumes=Volumes
HrCheg=HrArrival
HrSaida=HrDeparture
CheckIn=Check In
Officer=Officer
DataHora=Date - Time
InicioRonda=Start of Tours
FimRonda=End of Tours
ReportEvento=Event Report
ReportRonda=Tour Report
CheckInReport=Check In Report
CheckOutReport=Check Out Report
QtdRelatorios=Number of Reports
CheckOut=Check Out
UsuarioSemClientes=User without authorized clients
InicioAfastamento=In\u00edcio Afastamento
Identificador=Identificador
Validacao=Valida\u00e7\u00e3o
MuitosEventosValidar=Muitos eventos a validar, enviando automaticamente.
RelatorioPublico=Public Report
RelatorioTornadoPublico=Report made public
RelatorioTornadoPrivado=Report made private
Ronda=Tour
Conformidade=Compliance
Completas=Complete
NomeTag=Tag Name
OrdemLeitura=Order Read
TagTour=Tag # in Tour
Indicacao=Indica\u00e7\u00e3o
Dt_FormIni=Dt Form Ini
Dt_FormFim=Dt Form Fim
LocalForm=Local Forma\u00e7\u00e3o
NCertificado=N\u00ba Certificado
Dt_Recicl=Data Reciclagem
Dt_VenCurs=Data Venc Curso
Reg_PF=Registro PF
Reg_PFUF=UF
Reg_PFDt=Data Reg
CNH=CNH
CNHDtVenc=Dt Validade CNH
ExtensoesTV=Extens\u00f5es TV
ExtTV=Transporte
ExtSPP=Extens\u00e3o Seg. Pes
ExtEscolta=Escolta
DadosFormacao=Training Data
ClockIn=Clock In
ClockOut=Clock Out
ClockOutReport=Clock Out Report
ClockInReport=Clock In Report
Deletar=Delete
AdicionarEmail=Add E-mail
EmailCadastrado=E-mail already registered
ClienteNaoCadastrado=Unregistered Client!
Pergunta=Question
SimNao=Yes/No
Texto=Text
CapturaVideo=Video Capture
CapturaFoto=Photo Capture
AdicionarInspecao=Add Inspection
Inspecoes=Inspections
SelecioneInspecao=Select an Inspection
PstInspecao=Station Inspections
QtdPstInspecao=Number of Inspections
RelatorioInspecao=Inspection Report
Inspecao=Inspection
Inspecionado=Inspected
Assinatura=Signature
ClockInClockOut=Clock In/Clock Out
RelatorioSupervisor=Supervisor Report
CheckInCheckOut=Check In/Check Out
Remover=Delete
A3=A3
SenhaIndisponivelProcureRH=Fun\u00e7\u00e3o indispon\u00edvel no momento. Entre em contato com seu departamente de RH para alterar a senha.
Chamado=Chamado
Recusar=Recusar
Aceitar=Aceitar
descricao=Descri\u00e7\u00e3o
detalhe1=Detalhe 1
detalhe2=Detalhe 2
detalhe4=Detalhe 4
detalhe3=Detalhe 3
detalhe5=Detalhe 5
detalhe6=Detalhe 6
detalhe7=Detalhe 7
detalhe8=Detalhe 8
detalhe9=Detalhe 9
detalhe10=Detalhe 10
detalhe11=Detalhe 11
detalhe12=Detalhe 12
detalhe13=Detalhe 13
detalhe14=Detalhe 14
detalhe15=Detalhe 15
detalhe16=Detalhe 16
detalhe17=Detalhe 17
detalhe18=Detalhe 18
detalhe19=Detalhe 19
detalhe20=Detalhe 20
detalhe21=Detalhe 21
detalhe22=Detalhe 22
detalhe23=Detalhe 23
detalhe24=Detalhe 24
detalhe25=Detalhe 25
detalhe26=Detalhe 26
detalhe27=Detalhe 27
detalhe28=Detalhe 28
detalhe29=Detalhe 29
detalhe30=Detalhe 30
tipoarquivo=Tipo de Arquivo
idarquivo=ID Arquivo
datahorageracaoarquivo=Data/Hora de Gera\u00e7ao do Arquivo
comunicacao=Comunica\u00e7\u00e3o
idgruposuportedemandante=ID Grupo Suporte Demandante
gruposuportedemandante=Grupo Suporte Demandante
no_req=No Req
no_wo=No WO
no_inc=No Inc
no_crq=No Crq
numero_serie=N\u00famero S\u00e9rie
idreq=ID Req
nomereq=Nome Req
idfornecedor=Id Fornecedor
prioridade=Prioridade
nomefornecedor=Nome Fornecedor
categ_op_n1=Categoria Op n1
categ_op_n2=Categoria Op n2
categ_op_n3=Categoria Op n3
categ_prod_n1=Categoria Prod n1
categ_prod_n2=Categoria Prod n2
categ_prod_n3=Categoria Prod n3
nomeproduto=Nome Produto
modeloproduto=Modelo Produto
fabricante=Fabricante
codigodobanco=C\u00f3digo Banco
tipounidade=Tipo Unidade
codigounidade=C\u00f3digo Unidade
siglaunidade=Sigla Unidade
nomeunidade=Nome Unidade
enderecounidade=Endere\u00e7o Unidade
cidadeunidade=Cidade Unidade
ufunidade=UF Unidade
cep=CEP
idsolicitante=ID Solicitante
contatotelefone=Contato Telefone
contatonome=Contato Nome
contatoemail=Contato Email
RecusarChamado=Recusar Chamado
AceitarChamado=Aceitar Chamado




previsaoatendimento=Previs\u00e3o de Atendimento
Confirmar=Confirmar
tiporetorno=Tipo Retorno
chamadofornecedor=Chamado Fornecedor
responsavelatendimento=Respons\u00e1vel Atendimento
Grafico=Graphics
Faltas=Absences
Suspensao=Suspensions
Atestado=Sick note
HorasExtras=Overtime
EvoHoras=Overtime Evolution
Horas50=Overtime 50% 
Horas100=Overtime 100% 
Compet=Competence
FaltasUltimoMes=Absences
EvoFaltas=Evolution of Absences
SuspUltimoMes=Suspensions
EvoSusp=Evolution of Suspensions
AtestadoUltimoMes=Sick Note
EvoAtestados=Evolution of sick note
Competencia=Competency
RelatorioAuditoria=User Audit Report
ControleUsuarios=Users Management
CadastrarGrupos=Cadastrar Grupos
DetalhesGrupo=Detalhes do Grupos
SATTRANSPORTER=TRANSPORTER
Said=Dptr
NRedFat=Billing Costumer
LocalParada=Local Parada
AssinaturaDestino=Destination Signature
UploadPedido=Request Upload
ArquivosRecentes=Recent Files
Tamanho=Tamanho
OrigemPedido=Origem do Pedido
PedidosRecentes=Pedidos Recentes
SubAgencia=Sub Ag\u00eancia
ProcessandoAguarde=PROCESSING...PLEASE WAIT
SelecioneUnicoCliente=Selecione um \u00fanico cliente para realizar a importa\u00e7\u00e3o por arquivo.
DtColeta=Data Coleta
DtEntrega=Data Entrega
SelecionarCliente=Select the type of client
HoraEntrega=Delivery Time
Solicitante=Requester
Pedidos=Orders
QtdPedidos=Number of Orders
classificacao=Ranking
DataEntrega=Delivery date
Servico=Service
Suprimento=Supply
MensagemImportacaoPreOrder=Foram encontrados %s1 lacres para %s2 pedidos. Deseja prosseguir com a importa\u00e7\u00e3o?
MensagemConfirmacaoPreOrder=Foram encontrados pedidos para a data %s1. Deseja remov\u00ea-los e realizar uma nova importa\u00e7\u00e3o?
ReportRota=Route Summary Report
InicioRota=Route Start
UltimoServico=Last Service
RotaPausada=Route Paused
RotaRetomada=Route Resumed
RelatorioPrestador=Service Provider Report
RotaPrestador=Service Provider Route
SATECOVISAO=ECOVIS\u00c3O
TipoVeiculoInvalido=Tipo de ve\u00edculo inv\u00e1lido
Forte=Strong
Leve=Car
Moto=Moto
Pesado=Truck
Aeronave=Aircraft
Nenhum=None
EntregaRecolhimento=Delivery/Pick-up
Transbordo=Transfer
Rotineiro=Routine
Eventual=Eventual
Especial=Special
AssistTecnica=Technical Assistance
Intermediaria=Intermediate
Preliminar=Preliminary
ProjetoEspecial=Special Project
ExisteTrajetoMesmoHorarioEntrega=Existe outra parada no mesmo hor\u00e1rio da entrega
SelecioneCliOri=Selecione o cliente de origem
SelecioneCliDst=Selecione o cliente de destino
ExisteTrajetoMesmoHorario=Existe outra parada no mesmo hor\u00e1rio
HorarioDentroIntervalo=Hor\u00e1rio Inv\u00e1lido: Dentro do intervalo da rota
HorarioForaRota=Hor\u00e1rio Inv\u00e1lido: Fora do hor\u00e1rio da rota
RecolhimentoAnteriorEntrega=Hor\u00e1rio Inv\u00e1lido: Recolhimento anterior \u00e0 entrega
SemInfoCliFat=Sem informa\u00e7\u00f5es de cliente a faturar
SemGuiasParaManifest=N\u00e3o h\u00e1 informa\u00e7\u00f5es de guias para enviar o manifesto
MotoristaDiferenteVeiculo=01 - Driver other than standard.
Pref=Pref
NSOP=N\u00ba SOP
RelatorioUsuarios=Relat\u00f3rio de Controle de Usu\u00e1rios
AtualizarGrupos=Atualizar Grupos
Motorista=Driver
ChEquipe=Leader
Vigilante1=Vigilant1
Vigilante2=Vigilant2
Vigilante3=Vigilant3
FuncionariosFolga=Funcion\u00e1rios de Folga
AceitarFuncionariosFolga=Os funcion\u00e1rios abaixo se encontram de folga. Deseja prosseguir?
AceitarFuncionarioFolga=O funcion\u00e1rio abaixo se encontra de folga. Deseja prosseguir?
SenhaMobile=Mobile Password
PermissaoRotas=Permission Routes
PessoaJaEscalada=Pessoa j\u00e1 escalada
DataAnteriorEdicaoBloqueada=Escala com data anterior \u00e0 atual. Edi\u00e7\u00e3o bloqueada.
MotoristaJaEscalado=Motorista j\u00e1 escalado
ChEquipeJaEscalado=ChEquipe j\u00e1 escalado
Vigilante1JaEscalado=Vigilante 1 j\u00e1 escalado
Vigilante2JaEscalado=Vigilante 2 j\u00e1 escalado
Vigilante3JaEscalado=Vigilante 3 j\u00e1 escalado
HrMotInvalida=HrMot inv\u00e1lida
PessoaNaoMotorista=Essa pessoa n\u00e3o \u00e9 Motorista
PessoaNaoChEquipe=Essa pessoa n\u00e3o \u00e9 ChEquipe
VeiculoJaEscalado=Ve\u00edculo j\u00e1 escalado
HrCheInvalida=HrChe inv\u00e1lida
HrVig1Invalida=HrVig1 inv\u00e1lida
HrVig2Invalida=HrVig2 invalida
HrVig3Invalida=HrVig3 inv\u00e1lida
MovimentacaoContainer=Movimenta\u00e7\u00e3o Container
Container=Container
HrServico=HrServico
Containers=Containers
decimalSeparator=.
thousandSeparator=,
DSEIndividual=DSE Individual
Colaborador=Colaborador
DtInicio=Data de in\u00edcio
DtFim=Data final
DestinoEntrega=Destino Entrega
OrigemColeta=Origem Coleta
SelecioneParada=Selecione a parada para impress\u00e3o do manifesto
ManifestosDisponiveis=Manifestos Dispon\u00edveis
SB=Ag/SB
Vol=Vol
HrColeta=HrColeta
HrEntrega=HrEntrega
SBDst=Ag/SB Dst
QtdGuiasPreOrder=Qtd Guias
Graficos=Graficos
RPV=RPV
Remessa=Remessa
EditarLacre=Editar Lacre
NovoLacre=Novo Lacre
NovaImportacao=Nova Importa\u00e7\u00e3o
Atualizar=Atualizar
MensagemConfirmacaoPreOrders=Foram encontrados pedidos para a data %s1 em %s2 lotes. Deseja atualizar ou realizar uma nova importa\u00e7\u00e3o?
OSRtg=OSRtg
HrPrg=HrPrg
DtEnt=DtEnt
GuiaIndisponivel=Guia n\u00e3o dispon\u00edvel para impress\u00e3o
ErroPrimeiroAcesso=N\u00e3o foi poss\u00edvel finalizar o cadastro. Tente acessar com email e senha.
Identif=Identifica\u00e7\u00e3o
Dt_Inicio=Dt In\u00edcio
Dt_Termino=Dt T\u00e9rmino
CliFat=CliFat
ContratoCli=ContratoCli
RefArq=RefArq
GrpReajuste=GrpReajuste
GrpPagamento=GrpPagamento
OBS=OBS
Processo=Processo
Contratos=Contratos
QtdContratos=Quantidade de Contratos
IdentificacaoCliente=Identifica\u00e7\u00e3o Cliente
TipoContrato=Tipo do Contrato
ClienteContratante=Cliente Contratante
Identificacao=Identifica\u00e7\u00e3o
ContratoAssinado=Contrato Assinado
Determinado=Determinado
Indeterminado=Indeterminado
Transporte=Transporte
Geral=Geral
Cancelado=Cancelado
MostrarSomenteAtivos=Mostrar Somente Ativos
ContratosAVencer=Contratos a Vencer
login.falhageral<message>login.metodoerrado=Forma de login errada. Tente acessar utilizando email e senha.
EventoExcluir=Evento a Excluir
login.falhageral<message>Falha\ ao\ carregar\ filiais\ -\ UsuarioSemPermissao=Usu\u00e1rio sem permiss\u00e3o
NomeCli=Nome Cliente
SelecioneContrato=Select a Contract
OutrasBases=Outras Bases
TrocarFilial=Change Branch
SessaoExpirada=Session Expired
CliqueParaRedirecionar=Click here to return to the homepage.
DadosGerais=General Data
CarNacVig=CNV
DtValCNV=Dt Validade CNV
Mae=Mother
ExclusaoCargoSucesso=Exclus\u00e3o de cargo com sucesso
CargoSucesso=Cargo adicionado com sucesso
CargoPretendido=Cargo Pretendido
PIS=PIS
Historicos=Hist\u00f3ricos
ColetaAntesCorte=Coleta Antes Corte
Credito=Cr\u00e9dito
RecolhimentoDepoisCorte=Recolhimento Depois do Corte
NoCofre=No Cofre
ProximoDia=Pr\u00f3ximo Dia
Custodia=Cust\u00f3dia
PesquisarNFiscal=Pesquisar Nota Fiscal
Praca=Pra\u00e7a
Ativa=Ativa
Cancelada=Cancelada
NFiscais=Notas Fiscais
QtdNFiscais=Quantidade de Notas Fiscais
UploadNFiscal=Upload Arquivo XML
NRedPraca=Pra\u00e7a NRed
SelecioneNFiscal=Selecione uma Nota Fiscal
ErroOcorrido=There was an error processing the request.
CliqueParaRecarregar=Click to reload page
Movimentacoes=Movimenta\u00e7\u00f5es
Rotas=Routes
SATGETLOCK=GETLOCK
SATVANTEC=VANTEC
SATNORSERV=NORSERV
DadosCliente=Dados do Cliente
IdentificacaoContainer=Identifica\u00e7\u00e3o do Container
Download=Download
PortaSuperior=Porta Superior
PortaCofre=Porta do Cofre
Cassete=Cassete
Fechadura=Fechadura
Validadora=Validadora
Impressao=Impressora
Fechada=Fechada
Fechado=Fechado
Aberta=Aberta
Aberto=Aberto
Conectada=Conectada
Desconectada=Desconectada
Marca=Marca
Serial=Serial
IMEI=IMEI
InfoCofre=Informa\u00e7\u00f5es do Cofre
DepositosAte16=Depositos At\u00e9 16:00
DepositosApos16=Depositos Ap\u00f3s 16:00
Depositos=Dep\u00f3sitos
Coletas=Coletas
CofresGerenciados=Cofres Gerenciados
CofresGerenciadosMov=Cofres Gerenciados Movimenta\u00e7\u00e3o
DataLocacao=Rental date
HoraLocacao=Rental time
TempoDias=Time
TipoMovimentacao=Movimenta\u00e7\u00e3o
Atividade=Activity
CNPJCPF=CNPJ/CPF
IERG=IE/RG
Retencoes=Retention
RetemImpostos=Y - Withholds taxes as per parameters
NaoFazRetencoes=N - Make no holds
AgendarColeta=Agendar Coleta
Retorno=Retorno
Envelope=Envelope
LimiteSeguro=Limite Seguro
Cheque=Cheque
CodCofre=C\u00f3digo Cofre
Patrimonio=N\u00famero Patrim\u00f4nio
DadosATM=ATM Data
Interface=Interface
CodExt=Cod. Reference 2
CodPtoCli=Point Code
Normal=Normal
TransportadoraValores=Securities Carrier
MapaIndisponivel=Map Unavailable
CodigoRegiao=C\u00f3digo da Regi\u00e3o
Abrangencia=Abrang\u00eancia
TipoLocalizacao=Tipo da Localiza\u00e7\u00e3o
Regioes=Regions
Urbano=Urban
Interurbano=Interurban
PrazoColeta=Prazo de Coleta
ColetasVencidas=Coletas Vencidas
ColetasHoje=Coletas de Hoje
ColetasVencer=Coletas a Vencer
SemPrazoColeta=Sem Prazo de Coleta
TempoCliente=Tempo no Cliente
Servicos=Servi\u00e7os
PedidoCliente=Pedido Cliente
HoraDe=De
Ate=At\u00e9
SomenteDataSelecionada=Selected Date Only
Todos=All
Dias=Days
Mais15Dias=More Than 15 Days
ClienteServico=Customer - Service
MovimentacaoDiaria=Movimenta\u00e7\u00e3o Di\u00e1ria
buscaMovimentacao=Movimenta\u00e7\u00e3o
ClienteServico=Customer - Service
TrajetoAndamentoExecutado = Track in Progress - Executed
TrajetoAndamentoRestante = Track in Progress - Remaining
TrajetoExecutado = Track Executed
TrajetoProgramado = Programmed Path
OrigemMapa=Map origin
AtualizarMapa=Reload map
TodasRotas=List of Routes
Rastreador=Tracker
Manha=Morning
Tarde=Afternoon
HorarioPosicoes=Time of the positions
SATBRASIFORT=BRASIFORT
ProgramacaoGeral=General Schedule
QtdeCacambas=Buckets Quantity
Orcamento=Budget
InicioHorario=Start time
FinalHorario=End time 
ExclusaoPedidoNaoPermitida=Exclusion allowed for pending orders only
ServicoNaoDisponivel=Servi\u00e7o N\u00e3o Dispon\u00edvel
Atencao=Attention
ConfirmaExclusao=Confirm deletion?
ClienteNaoEncontradoOuSemCacambas=Client not found or without container
EstabelecimentoComercialResidencia=Commercial establishment or residence
Gravar=Record
QtdTrajetos=Number of Paths
FecharAtualizar=Close and Update
ValorDepositadoCXF=Amount deposited in Vault
VlrEntDir=Value direct deliveries
HorarioPosicao=Position Time
Transferencias=Transfers
TotalemRota=Total en Route
R=Collect
E=Delivery
Acessar=Access
EmpresaServico=Empresa/Servi\u00e7o
SelecioneEmpresaServico=Selecione a Empresa e o Servi\u00e7o
ClientesCadastrados=Clientes Cadastrados
TodosUsuarios=Lista de Usu\u00e1rios
AdicionarServico=Adicionar Servi\u00e7o
BancoDados=Banco de Dados
Bancos=Bancos
QtdBancos=Qtd de Bancos
AdicionarBanco=Adicionar Banco de Dados
CodigoLogin=C\u00f3digo Login
JanelaHorario=Time Window
CliqueParaDetalhes=Click for Details
Desconectado=Desconectado
SomenteAlertas=Somente alertas
NomeCofre=Vault Name
acesso.falhageral<message>EmailEmUso=Email j\u00e1 vinculado a outra pessoa.
NumeroCofre=Safe Number
RelatorioMovimentacaoGeralFuncionario=Relat\u00f3rio de Movimenta\u00e7\u00e3o Geral por Funcion\u00e1rio
Noite=Night
Atrasados=Late
COLETA=Coleta
DEPOSITO=Dep\u00f3sito
MOVIMENTACAO_INDIVIDUAL_FUNCIONARIO=Movimenta\u00e7\u00e3o Individual de Funcion\u00e1rio
MOVIMENTACAO_DIARIA=Movimenta\u00e7\u00e3o Di\u00e1ria
MOVIMENTACAO_GERAL_FUNCIONARIO=Movimenta\u00e7\u00e3o Geral por Funcion\u00e1rio
GerarRelatorio=Gerar Relat\u00f3rio
Coleta=Coleta
Deposito=Dep\u00f3sito
RelatorioDeposito=Relat\u00f3rio de Dep\u00f3sito
RelatorioMovimentacaoIndividualFuncionario=Relat\u00f3rio de Movimenta\u00e7\u00e3o de Funcion\u00e1rio
RelatorioMovimentacaoDiaria=Relat\u00f3rio de Movimenta\u00e7\u00e3o Di\u00e1ria
RelatorioColeta=Relat\u00f3rio de Coleta
TotalDeposito=Total em Dep\u00f3sitos
TotalColeta=Total em Coletas
SaldoAnterior=Saldo Anterior
SaldoDia=Saldo do Dia
SemDepositosData=N\u00e3o h\u00e1 dep\u00f3sitos para a data selecionada.
SemColetasData=N\u00e3o h\u00e1 coletas para a data selecionada.
SemMovimentacoesData=N\u00e3o h\u00e1 operadores com movimenta\u00e7\u00e3o para a data selecionada.
SelecioneOperador=Selecione um Operador
SelecioneDeposito=Selecione um Dep\u00f3sito
SelecioneColeta=Selecione uma Coleta
SATCIT=CIT
TotalParadas=Total Stops
TotalGuias=Total Guides
TotalKm=Total KM
Mes=Month
Ano=Year
ParadasGuias=Stops and Guides
AnaliseProd=Productivity Analysis Per Day
AnalseValores=Transported Values \u200b\u200bAnalysis
TotalTransportado=Total Carried per Day
Dashboard=Dashboard
Horas=Hours
AnaliseValoresTransp=Transported Values \u200b\u200bAnalysis
TotalValoresTransp=Total Amounts Carried by Route
AnaliseTrajetoExec=Run Path Analysis
TotalKmPerc=Total Km Traveled Per Day
TotalParadasRota=Total Stops by Route
MediaProdDia=Average Productivity per Day
ConsiderandoDados=Considering Data From First to Last Day of Month
MaximoRotas=Maximum Routes
AnaliseParada=Stop Analysis
OutrosDesc=Others
Saidas=Exits
EmServico=In Service
ForaServico=Out Of Service
SATSHALOM=SHALOM
TipoOS=SO type
SelecioneOS=Select a SO
CliDst=Client destination
ClienteFaturar=Client Invoice
DiaFechaFat=Invoice closing day
Agrupador=Switcher
MsgExtrato=Extract message
CodSrv=Service code
KM=Distance (km)
KMTerra=km dirt road
Aditivo=Addition
CCusto=Cost Center
OSGrp=Switcher II
GTVQtde=CIT quantity
GTVEstMin=CITEstMin
SitFiscal=Fiscal Situation
CofresAtivos=Cofres Ativos
TotalDepositos=Total de Dep\u00f3sitos
TotalColetas=Total de Coletas
Dia=Dia
CofresAtualizados=Cofres Atualizados
StatusComunicacao=Status de Comunica\u00e7\u00e3o
StatusBateria=Status de Bateria
Saldo=Saldo
UltimaMovimentacao=\u00daltima Movimenta\u00e7\u00e3o
Bateria=Bateria
Versao=Vers\u00e3o
Movimentacao=Movimenta\u00e7\u00e3o
MovimentacoesRecentes=Movimenta\u00e7\u00f5es Recentes
UltimasMovimentacoes=\u00daltimas Movimenta\u00e7\u00f5es
AnaliseMovimentacoes=An\u00e1lise de Movimenta\u00e7\u00f5es
TotalMovimentacaoHora=Total de Movimenta\u00e7\u00e3o por Hora
Online=Online
Offline=Offline
SemComunicacaoRecente=Sem Comunica\u00e7\u00e3o Recente
Atualizados=Atualizados
Desatualizados=Desatualizados
SaldoCofres=Saldo dos Cofres
InfoCofres=Informa\u00e7\u00f5es dos cofres: bateria, vers\u00e3o, sensores
StatusCofres=Status dos Cofres
VersaoAtual=Vers\u00e3o Atual
InformacoesGerais=Informa\u00e7\u00f5es Gerais
MovimentacoesDiaHistoricoCompleto=Movimenta\u00e7\u00f5es do Dia e Resumo Geral dos Cofres
RelatorioColetas=Relat\u00f3rio Coletas
GerarRelatorioAntes=Gere o Relat\u00f3rio Antes de Export\u00e1-lo
