/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PropProd;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PropProdDao {

    /**
     * Lista todos os produtos de uma proposta.
     *
     * @param numero
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PropProd> listarProdutosProposta(BigDecimal numero, BigDecimal codFil, Persistencia persistencia) throws Exception {
        List<PropProd> retorno = new ArrayList<>();
        try {
            String sql = " select * from propprod "
                    + " where numero = ? and codfil = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(numero);
            consult.setBigDecimal(codFil);
            consult.select();
            PropProd produto;
            while (consult.Proximo()) {
                produto = new PropProd();
                produto.setCodFil(consult.getBigDecimal("codfil"));
                produto.setCodPro(consult.getBigDecimal("codpro"));
                produto.setCustoUn(consult.getBigDecimal("custoun"));
                produto.setDt_Alter(consult.getString("dt_alter"));
                produto.setDt_Incl(consult.getLocalDate("dt_incl"));
                produto.setHr_Alter(consult.getString("hr_alter"));
                produto.setHr_Incl(consult.getString("hr_incl"));
                produto.setNumero(consult.getBigDecimal("numero"));
                produto.setOperIncl(consult.getString("operincl"));
                produto.setOperador(consult.getString("operador"));
                produto.setOrdem(consult.getBigDecimal("ordem"));
                produto.setQtde(consult.getBigDecimal("qtde"));
                produto.setValorTot(consult.getBigDecimal("valortot"));
                produto.setValorUn(consult.getBigDecimal("valorun"));
                retorno.add(produto);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Failed to list produtos de proposta - " + e.getMessage());
        }
    }

    /**
     * Insere um produto a uma proposta
     *
     * @param produto
     * @param persistencia
     * @throws Exception
     */
    public void inserirProduto(PropProd produto, Persistencia persistencia) throws Exception {
        try {
            String sql = "insert into propprod (codfil, codpro, custoun, dt_alter, dt_incl, hr_alter, "
                    + " hr_incl, numero, operincl, operador, ordem, qtde, valortot, valorun) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(produto.getCodFil());
            consulta.setBigDecimal(produto.getCodPro());
            consulta.setBigDecimal(produto.getCustoUn());
            consulta.setString(produto.getDt_Alter());
            consulta.setDate(DataAtual.LC2Date(produto.getDt_Incl()));
            consulta.setString(produto.getHr_Alter());
            consulta.setString(produto.getHr_Incl());
            consulta.setBigDecimal(produto.getNumero());
            consulta.setString(produto.getOperIncl());
            consulta.setString(produto.getOperador());
            consulta.setBigDecimal(produto.getOrdem());
            consulta.setBigDecimal(produto.getQtde());
            consulta.setBigDecimal(produto.getValorTot());
            consulta.setBigDecimal(produto.getValorUn());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao inserir produto a proposta - " + e.getMessage());
        }
    }

    /**
     * Busca o próximo número de ordem de produto em uma proposta
     *
     * @param numero
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public BigDecimal getOrdem(BigDecimal numero, BigDecimal codFil, Persistencia persistencia) throws Exception {
        BigDecimal retorno = BigDecimal.ZERO;
        try {
            String sql = " select isnull(MAX(ordem),0)+1 ordem "
                    + " FROM propprod "
                    + " WHERE numero = ? and codFil = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(numero);
            consult.setBigDecimal(codFil);
            consult.select();
            while (consult.Proximo()) {
                retorno = consult.getBigDecimal("ordem");
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar número de ordem - " + e.getMessage());
        }
    }

    /**
     * Busca o número de ordem de produto em uma proposta
     *
     * @param numero
     * @param codFil
     * @param codpro
     * @param persistencia
     * @return
     * @throws Exception
     */
    public BigDecimal getOrdemEdicao(BigDecimal numero, BigDecimal codFil, BigDecimal codpro, Persistencia persistencia) throws Exception {
        BigDecimal retorno = BigDecimal.ZERO;
        try {
            String sql = " select isnull(ordem,0) ordem "
                    + " FROM propprod "
                    + " WHERE numero = ? and codFil = ? and codpro = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(numero);
            consult.setBigDecimal(codFil);
            consult.setBigDecimal(codpro);
            consult.select();
            while (consult.Proximo()) {
                retorno = consult.getBigDecimal("ordem");
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar número de ordem - " + e.getMessage());
        }
    }

    /**
     * Atualiza um produto de uma proposta
     *
     * @param produto
     * @param persistencia
     * @throws Exception
     */
    public void atualizarProduto(PropProd produto, Persistencia persistencia) throws Exception {
        try {
            String sql = "update propprod set custoun = ?, dt_alter = ?, hr_alter = ?,"
                    + " operador = ?, qtde = ?, valortot = ?, valorun = ?"
                    + " where numero = ? and codFil = ? and codpro = ? and ordem = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(produto.getCustoUn());
            consulta.setString(produto.getDt_Alter());
            consulta.setString(produto.getHr_Alter());
            consulta.setString(produto.getOperador());
            consulta.setBigDecimal(produto.getQtde());
            consulta.setBigDecimal(produto.getValorTot());
            consulta.setBigDecimal(produto.getValorUn());
            consulta.setBigDecimal(produto.getNumero());
            consulta.setBigDecimal(produto.getCodFil());
            consulta.setBigDecimal(produto.getCodPro());
            consulta.setBigDecimal(produto.getOrdem());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao atulizar produto da proposta - " + e.getMessage());
        }
    }

    public void removerProduto(PropProd produto, Persistencia persistencia) throws Exception {
        try {
            String sql = "delete from propprod"
                    + " where numero = ? and codFil = ? and codpro = ? and ordem = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(produto.getNumero());
            consulta.setBigDecimal(produto.getCodFil());
            consulta.setBigDecimal(produto.getCodPro());
            consulta.setBigDecimal(produto.getOrdem());
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao remover produto de uma proposta - " + e.getMessage());
        }
    }
}
