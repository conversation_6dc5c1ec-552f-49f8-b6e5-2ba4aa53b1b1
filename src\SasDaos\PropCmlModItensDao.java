/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PropCmlMod;
import SasBeans.PropCmlModItens;
import SasBeansCompostas.CarregarRelatorio;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PropCmlModItensDao {

    /**
     * Lista os itens de uma proposta
     *
     * @param codigo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PropCmlModItens> listar(BigDecimal codigo, Persistencia persistencia) throws Exception {
        List<PropCmlModItens> retorno = new ArrayList<>();
        try {
            String sql = " select * from PropCmlModItens"
                    + " where codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codigo);
            consulta.select();
            PropCmlModItens item;
            while (consulta.Proximo()) {
                item = new PropCmlModItens();
                item.setCodigo(consulta.getBigDecimal("codigo"));
                item.setDescricao(consulta.getString("descricao"));
                item.setDetalhe(consulta.getString("detalhe"));
                item.setDt_alter(consulta.getLocalDate("dt_alter"));
                item.setHr_Alter(consulta.getString("hr_alter"));
                item.setItem(consulta.getBigDecimal("item"));
                item.setOperador(consulta.getString("operador"));
                retorno.add(item);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception(" Failed to list itens de proposta - " + e.getMessage());
        }
    }

    public List<CarregarRelatorio> listaColaborador(String codigo, Persistencia persistencia) throws Exception {
        try {
            List<CarregarRelatorio> retorno = new ArrayList<>();
            String sql = "Select PropCmlMod.Codigo, PropCmlMod.Detalhe Introducao,"
                    + " PropCmlMod.Descricao Titulo, PropCmlModItens.Item, "
                    + "PropCmlModItens.Descricao DescricaoItem, \n"
                    + "PropCmlModItens.Detalhe DetalheItem, PropCmlMod.Imprimelogo, "
                    + "PropCmlMod.ImprimeTitulo, PropCmlMod.ImprimeAss"
                    + " From PropCmlModItens \n"
                    + "left join PropCmlMod on PropCmlMod.Codigo = PropCmlModItens.Codigo\n"
                    + "where PropCmlModItens.Codigo = ? \n"
                    + "order by PropCmlModItens.Item";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();

            CarregarRelatorio carregaManual;
            PropCmlModItens propCmlModItens;
            PropCmlMod propCmlMod;

            while (consulta.Proximo()) {
                carregaManual = new CarregarRelatorio();
                propCmlMod = new PropCmlMod();
                propCmlModItens = new PropCmlModItens();

                propCmlMod.setCodigo(consulta.getBigDecimal("Codigo"));
                propCmlMod.setDetalhe(consulta.getString("Introducao"));
                propCmlMod.setDescricao(consulta.getString("Titulo"));

                propCmlModItens.setItem(consulta.getBigDecimal("Item"));
                propCmlModItens.setDescricao(consulta.getString("DescricaoItem"));
                propCmlModItens.setDetalhe(consulta.getString("DetalheItem"));

                carregaManual.setPropCmlMod(propCmlMod);
                carregaManual.setPropCmlModItens(propCmlModItens);

                retorno.add(carregaManual);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PropCmlModItensDao.registroEmpregador - " + e.getMessage()
                    + "Select PropCmlMod.Codigo, PropCmlMod.Detalhe Introducao,"
                    + " PropCmlMod.Descricao Titulo, PropCmlModItens.Item, "
                    + "PropCmlModItens.Descricao DescricaoItem, \n"
                    + "PropCmlModItens.Detalhe DetalheItem PropCmlMod.Imprimelogo, "
                    + "PropCmlMod.ImprimeTitulo, PropCmlMod.ImprimeAss"
                    + " From PropCmlModItens \n"
                    + "left join PropCmlMod on PropCmlMod.Codigo = PropCmlModItens.Codigo\n"
                    + "where PropCmlModItens.Codigo = " + codigo + "\n"
                    + "order by PropCmlModItens.Item");
        }
    }
}
