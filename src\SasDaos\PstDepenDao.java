/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PstDepen;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PstDepenDao {

    /**
     * Busca informações de uma dependência pelo qrCode cadastrado
     *
     * @param qrCode
     * @param persistencia
     * @return
     * @throws Exception
     */
    public PstDepen getPstDepenQrCode(String qrCode, Persistencia persistencia) throws Exception {
        try {
            PstDepen retorno = new PstDepen();
            String sql = " select * from pstdepen "
                    + " left join pstserv on pstserv.secao = pstdepen.secao "
                    + "                  and pstserv.codfil = pstdepen.codfil"
                    + " where qrcode = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(qrCode);
            consulta.select();
            while (consulta.Proximo()) {
                retorno.setDescricao(consulta.getString("descricao"));
                retorno.setSecao(consulta.getString("secao"));
                /**
                 * Salvando o nome do posto de serviço em QrCode
                 */
                retorno.setQrCode(consulta.getString("local"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstDepenDao.getPstDepenQrCode - " + e.getMessage() + "\r\n"
                    + "select * from pstdepen "
                    + " left join pstserv on pstserv.secao = pstdepen.secao "
                    + "                  and pstserv.codfil = pstdepen.codfil"
                    + " where qrcode = " + qrCode);
        }
    }

    /**
     * Retorno o qrcode de uma dependência
     *
     * @param secao
     * @param codfil
     * @param codigo
     * @param persistencia
     * @return
     */
    public String getQRCode(String secao, String codfil, String codigo, Persistencia persistencia) {
        try {
            String retorno = "";
            String sql = "select QRCode from pstdepen where secao = ? and codfil = ? and codigo = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(secao);
            consulta.setString(codfil);
            consulta.setString(codigo);
            consulta.select();
            while (consulta.Proximo()) {
                retorno = consulta.getString("QrCode");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * Lista todos as dependecias de um posto
     *
     * @param secao
     * @param codfil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PstDepen> getPstDepen(String secao, String codfil, Persistencia persistencia) throws Exception {
        try {
            List<PstDepen> retorno = new ArrayList<>();
            String sql = "select * from pstdepen where secao = ? and codfil = ? order by codigo desc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(secao);
            consulta.setString(codfil);
            consulta.select();
            PstDepen pstDepen;
            while (consulta.Proximo()) {
                pstDepen = new PstDepen();
                pstDepen.setCodigo(consulta.getString("Codigo"));
                pstDepen.setSecao(consulta.getString("Secao"));
                pstDepen.setCodFil(consulta.getString("CodFil"));
                pstDepen.setDescricao(consulta.getString("Descricao"));
                pstDepen.setHrIni(consulta.getString("HrIni"));
                pstDepen.setHrFim(consulta.getString("HrFim"));
                pstDepen.setNroVig(consulta.getBigDecimal("NroVig"));
                pstDepen.setEscAuto(consulta.getString("EscAuto"));
                pstDepen.setQrCode(consulta.getString("QrCode"));
                pstDepen.setOperador(consulta.getString("Operador"));
                pstDepen.setDt_Alter(consulta.getString("Dt_Alter"));
                pstDepen.setHr_Alter(consulta.getString("Hr_Alter"));
                pstDepen.setLatitude(consulta.getBigDecimal("latitude"));
                pstDepen.setLongitude(consulta.getBigDecimal("longitude"));
                retorno.add(pstDepen);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstDepenDao.getPstDepen - " + e.getMessage() + "\r\n"
                    + "selec * from pstdepen where secao = " + secao + " and codfil = " + codfil);
        }
    }

    /**
     * Verifica se a tag já foi cadastrada no sistema
     *
     * @param codigo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean verificaExisteTag(String codigo, Persistencia persistencia) throws Exception {
        try {
            boolean retorno = false;
            String sql = " select secao from pstdepen where qrcode = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();
            while (consulta.Proximo()) {
                retorno = true;
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstDepenDao.verificaExisteTag - " + e.getMessage() + "\r\n"
                    + " select secao from pstdepen where qrcode = " + codigo);
        }
    }

    /**
     * Insere uma nova entrada na tabela PstDepn
     *
     * @param Secao
     * @param CodFil
     * @param Descricao
     * @param QrCode
     * @param Operador
     * @param Dt_Alter
     * @param Hr_Alter
     * @param latitude
     * @param longitude
     * @param persistencia
     * @return código da nova dependência
     * @throws Exception
     */
    public String inserePstDepen(String Secao, String CodFil, String Descricao, String QrCode, String Operador,
            String Dt_Alter, String Hr_Alter, BigDecimal latitude, BigDecimal longitude, Persistencia persistencia) throws Exception {
        boolean erro = true;
        int i = 0;
        String codigo = "0";
        while (erro) {
            try {
                String sqlCodigo = " select isnull(max(codigo),0) + 1 codigo"
                        + " from pstdepen "
                        + " where secao = ? and codfil = ? ";
                Consulta consulta = new Consulta(sqlCodigo, persistencia);
                consulta.setString(Secao);
                consulta.setString(CodFil);
                consulta.select();
                while (consulta.Proximo()) {
                    codigo = consulta.getString("codigo");
                }
                consulta.Close();

                String sql = " insert into PstDepen (Secao, CodFil, Descricao, HrIni, HrFim, NroVig, "
                        + " EscAuto, QrCode, Operador, Dt_Alter, Hr_Alter, latitude, longitude, Codigo) "
                        + " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
                consulta = new Consulta(sql, persistencia);
                consulta.setString(Secao);
                consulta.setString(CodFil);
                consulta.setString(Descricao);
                consulta.setString("");
                consulta.setString("");
                consulta.setString("0");
                consulta.setString("S");
                consulta.setString(QrCode);
                consulta.setString(Operador);
                consulta.setString(Dt_Alter);
                consulta.setString(Hr_Alter);
                consulta.setBigDecimal(latitude);
                consulta.setBigDecimal(longitude);
                consulta.setString(codigo);
                consulta.insert();
                consulta.close();
                erro = false;
            } catch (Exception e) {
                erro = true;
                i++;
                if (i == 10) {
                    throw new Exception("PstDepenDao.inserePstDepen - " + e.getMessage());
                }
            }
        }
        return codigo;
    }

    /**
     * Atualiza a descrição de uma dependência de um posto
     *
     * @param codigo
     * @param Secao
     * @param CodFil
     * @param Descricao
     * @param Operador
     * @param Dt_Alter
     * @param Hr_Alter
     * @param latitude
     * @param longitude
     * @param persistencia
     * @throws Exception
     */
    public void atualizaPstDepen(String codigo, String Secao, String CodFil, String Descricao, String Operador,
            String Dt_Alter, String Hr_Alter, BigDecimal latitude, BigDecimal longitude, String qrCode, Persistencia persistencia) throws Exception {
        try {
            String sql = " update PstDepen set Descricao = ?, Operador = ?, Dt_Alter = ?, Hr_Alter = ?, latitude = ?, longitude = ?, "
                    + " qrCode = ? "
                    + " where secao = ? and codfil = ? and Codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Descricao);
            consulta.setString(Operador);
            consulta.setString(Dt_Alter);
            consulta.setString(Hr_Alter);
            consulta.setBigDecimal(latitude);
            consulta.setBigDecimal(longitude);
            consulta.setString(qrCode);
            consulta.setString(Secao);
            consulta.setString(CodFil);
            consulta.setString(codigo);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PstDepenDao.atualizaPstDepen - " + e.getMessage() + "\r\n"
                    + " update PstDepen set Descricao = " + Descricao + ", Operador = " + Operador + ", Dt_Alter = " + Dt_Alter + ", "
                    + " Hr_Alter = " + Hr_Alter + ", latitude = " + latitude + ", longitude = " + longitude
                    + " where secao = " + Secao + " and codfil = " + CodFil + " and Codigo = " + codigo);
        }
    }
}
