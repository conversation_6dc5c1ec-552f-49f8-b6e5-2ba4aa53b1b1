package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Escala;
import SasBeans.Paramet;
import SasBeans.Rastrear;
import SasBeans.Rt_Escala;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.Empresas;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR> servidor para usar debug mais facilmente no integrador com
 * a golsat
 */
public class RastrearDao {

    private Boolean isTranspCacamba(String empresa) throws Exception {
        SasPoolPersistencia pool = new SasPoolPersistencia();
        pool.setCaminho("/Dados/mapconect_deploy.txt");
        Persistencia inSatellite;
        inSatellite = pool.getConexao("SATELLITE", "");

        ParametDao parametDao = new ParametDao();
        Paramet parametGoogle = parametDao.getParametGoogleApi(empresa, inSatellite);

        return parametGoogle.getTranspCacamba().equals("0") ? false : true;
    }

    Empresas empresa = new Empresas();

    /**
     * Grava rastreamento
     *
     * @param Codigo - CÃ³digo sequencial da prÃ³xima posiÃ§Ã£o na tabela -
     * buscar pelo mÃ©todo getProximoRegistro
     * @param ID_Modulo - NÃºmero identificador
     * @param Latitude - NÃºmero latitude
     * @param Longitude - NÃºmero logitude
     * @param Data - Data rastreamento
     * @param Hora - Hora rastreamento
     * @param DtTrans - Data transporte
     * @param HrTrans - Hora transporte
     * @param Satelite - CÃ³digo Satelite
     * @param persistencia - ConexÃ£o ao banco
     * @throws java.lang.Exception - pode gerar exception
     */
    public void gravarRastrear(BigDecimal Codigo, String ID_Modulo, String Latitude, String Longitude, String Data,
            String Hora, String DtTrans, String HrTrans, String Satelite, Persistencia persistencia) throws Exception {
        String sql;
        try {
            sql = "insert into rastrear"
                    + " (Codigo, ID_Modulo, Latitude, Longitude, Data, Hora, DtTrans, HrTrans, Satelite)"
                    + " values (?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(Codigo);
            consulta.setString(ID_Modulo);
            consulta.setString(Latitude);
            consulta.setString(Longitude);
            consulta.setString(Data);
            consulta.setString(Hora);
            consulta.setString(DtTrans);
            consulta.setString(HrTrans);
            consulta.setString(Satelite);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao gravar tabela Rastrear - " + e.getMessage());
        }
    }

    /**
     * Retorna o cÃ³digo da prÃ³xima posiÃ§Ã£o da tabela
     *
     * @param persistencia - conexÃ£o ao banco
     * @return - prÃ³ximo cÃ³digo
     * @throws Exception
     */
    public BigDecimal getProximoRegistro(String vCodPessoa, String vDataPOS, String vHoraPOS, Persistencia persistencia) throws Exception {
        BigDecimal retorno = null;
        try {
            //Consulta consult = new Consulta("select ISNULL(MAX(codigo),0)  Codigo from rastrear ", persistencia);
            Consulta consult = new Consulta("Select max(Codigo) codigoMax, isnull((Select top 1 Codigo from rastrear where Matr = "+vCodPessoa+" and Data = '"+vDataPOS+"' and hora = '"+vHoraPOS+"'),0) Codigo from Rastrear", persistencia);
            
            consult.select();
            retorno = new BigDecimal("-1.00");
            while (consult.Proximo()) {
                try {                    
                   if (consult.getInt("Codigo") == 0){
                       retorno = consult.getBigDecimal("codigoMax");
                    }
                } catch (Exception e) {
                    retorno = new BigDecimal("-1.00");
                }
            }
            if (retorno == null) {
                retorno = new BigDecimal("-1.00");
            }
            consult.Close();
            return (retorno);
        } catch (Exception e) {
            throw new Exception("Falha ao encontrar posiÃ§Ã£o tabela Rastrear - " + e.getMessage());
        }
    }

    /**
     * Inserção de posição na tabela rastrear
     *
     * @param rastrear campos importantes: Codigo, ID_Modulo, Latitude,
     * Longitude, Data, Hora, DtTrans, HrTrans, Satelite
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void gravarRastrear(Rastrear rastrear, Persistencia persistencia) throws Exception {
        try {
                        
            String sql = "insert into Rastrear "
                    + " (Codigo, ID_Modulo, Latitude, Longitude, Data, Hora, DtTrans, HrTrans, Satelite, "
                    + "  Placa, Velocidade, Movimento)"
                    + " values (?,?,?,?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(rastrear.getCodigo());
            consulta.setString(rastrear.getID_MODULO());
            consulta.setString(rastrear.getLatitude());
            consulta.setString(rastrear.getLongitude());
            consulta.setString(rastrear.getData().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            consulta.setString(rastrear.getHora());
            consulta.setString(rastrear.getDtTrans().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            consulta.setString(rastrear.getHrTrans());
            consulta.setString(rastrear.getSatelite());
            consulta.setString(rastrear.getPlaca());
            consulta.setString(rastrear.getVelocidade());
            consulta.setString(rastrear.getMovimento());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao gravar tabela Rastrear - " + e.getMessage());
        }
    }

    /**
     * Excluir todos os registros anteriores a data passada
     *
     * @param Data - serão exluidos todos os registros <= a data passada @param
     * pe rsistencia - conexão ao banco de dados @throws Exception @param
     * persistencia @throws java.lang.Exception
     */
    public void ApagarRegistrosData(LocalDate Data, Persistencia persistencia) throws Exception {
        String sql;
        try {
            sql = "delete from rastrear where data <= ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setDate(DataAtual.LC2Date(Data));
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao excluir registros tabela Rastrear - \r\n" + e.getMessage());
        }
    }

    /**
     * Grava posição com a matricula do usuário
     *
     * @param Codigo
     * @param ID_Modulo
     * @param Latitude
     * @param Longitude
     * @param Data
     * @param Hora
     * @param DtTrans
     * @param HrTrans
     * @param Satelite
     * @param Matr
     * @param velocidade
     * @param precisao
     * @param persistencia
     * @throws Exception
     */
    public void gravarRastrear(BigDecimal Codigo, String ID_Modulo, String Latitude, String Longitude, String Data,
            String Hora, String DtTrans, String HrTrans, String Satelite, String Matr, String velocidade, String precisao,
            Persistencia persistencia) throws Exception {
        String sql;
        try {
            sql = "insert into rastrear"
                    + " (Codigo, ID_Modulo, Latitude, Longitude, Data, Hora, DtTrans, HrTrans, Satelite, Matr, Velocidade, Precisao)"
                    + " values (?,?,?,?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(Codigo);
            consulta.setString(ID_Modulo);
            consulta.setString(Latitude);
            consulta.setString(Longitude);
            consulta.setString(Data);
            consulta.setString(Hora);
            consulta.setString(DtTrans);
            consulta.setString(HrTrans);
            consulta.setString(Satelite);
            consulta.setString(Matr);
            consulta.setString(velocidade);
            consulta.setString(precisao);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("RastrearDao.gravarRastrear - " + e.getMessage() + "\r\n"
                    + "insert into rastrear"
                    + " (Codigo, ID_Modulo, Latitude, Longitude, Data, Hora, DtTrans, HrTrans, Satelite, Matr)"
                    + " values (" +Codigo+ "," + ID_Modulo + "," + Latitude + "," + Longitude + "," + Data + "," + Hora + "," + DtTrans + "," + HrTrans + "," + Satelite + "," + Matr + ")");
        }
    }

    /**
     * Grava posição com a matricula do usuário
     *
     * @param ID_Modulo
     * @param Latitude
     * @param Longitude
     * @param Data
     * @param Hora
     * @param DtTrans
     * @param HrTrans
     * @param Satelite
     * @param Matr
     * @param persistencia
     * @throws Exception
     */
    public void gravarRastrear(String ID_Modulo, String Latitude, String Longitude, String Data,
            String Hora, String DtTrans, String HrTrans, String Satelite, String Matr, Persistencia persistencia) throws Exception {
        String sql;
        try {
            sql = "insert into rastrear"
                    + " (Codigo, ID_Modulo, Latitude, Longitude, Data, Hora, DtTrans, HrTrans, Satelite, Matr)"
                    + " values (?,?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(ID_Modulo);
            consulta.setString(Latitude);
            consulta.setString(Longitude);
            consulta.setString(Data);
            consulta.setString(Hora);
            consulta.setString(DtTrans);
            consulta.setString(HrTrans);
            consulta.setString(Satelite);
            consulta.setString(Matr);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao gravar tabela Rastrear - " + e.getMessage());
        }
    }

    public void gravarRastrearA(String ID_Modulo, String Latitude, String Longitude, String Data,
            String Hora, String DtTrans, String HrTrans, String velocidade, Persistencia persistencia) throws Exception {
        String sql;
        try {
            sql = "insert into rastrear"
                    + " (Codigo, ID_Modulo, Latitude, Longitude, Data, Hora, DtTrans, HrTrans, Velocidade)"
                    + " values ((select ISNULL(max(codigo),0) + 1 FROM Rastrear),?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(ID_Modulo);
            consulta.setString(Latitude);
            consulta.setString(Longitude);
            consulta.setString(Data);
            consulta.setString(Hora);
            consulta.setString(DtTrans);
            consulta.setString(HrTrans);
            consulta.setString(velocidade);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("RastrearDao.gravarRastrear - " + e.getMessage() + "\r\n"
                    + "insert into rastrear"
                    + " (Codigo, ID_Modulo, Latitude, Longitude, Data, Hora, DtTrans, HrTrans, Velocidade)"
                    + " values ((select ISNULL(max(codigo),0) + 1 FROM Rastrear)," + ID_Modulo + "," + Latitude + ","
                    + Longitude + "," + Data + "," + Hora + "," + DtTrans + "," + HrTrans + "," + velocidade + ")");
        }
    }

    /**
     * Retorna a última localização do veículo
     *
     * @param seqRotas
     * @param persistencia - conexÃ£o ao banco
     * @return - prÃ³ximo cÃ³digo
     * @throws Exception
     */
    public List<Rastrear> buscarLocalizacoes(List<Escala> seqRotas, Persistencia persistencia) throws Exception {
        try {
            String sql = "  select  escala.rota, funcion.nome, rastrear.latitude, rastrear.longitude, \n"
                    + " rastrear.data, rastrear.hora, a.seqrota, escala.veiculo, veiculos.placa, VeiculosMod.Descricao ModeloVeic, funcion.nome_guer, \n"
                    + " mot.nome motNome,  \n"
                    + " (Select count(*) from Rt_PercSLA where Rt_PercSLA.Sequencia = a.SeqRota and len(isnull(Rt_PercSLA.HrChegVei,'')) \n"
                    + " > 0 and len(isnull(Rt_PercSla.HrSaidaVei,'')) = 0 ) Atendimento"
                    + " from \n"
                    + " (Select Seqrota, Max(rastrear.Data) Data, rastrearstat.codfil, Max(sequencia) sequencia\n"
                    + " from rastrearstat\n"
                    + " left join rastrear on rastrear.codigo = rastrearstat.sequencia \n";
            if (seqRotas.isEmpty()) {

            } else if (seqRotas.size() == 1) {
                sql += " where rastrearstat.seqrota = ? and rastrearstat.codfil = ? \n";
            } else {
                sql += " where \n";
                for (int i = 0; i < seqRotas.size(); i++) {
                    if (i == seqRotas.size() - 1) {
                        sql += " (rastrearstat.seqrota = ? and rastrearstat.codfil = ?) \n";
                    } else {
                        sql += " (rastrearstat.seqrota = ? and rastrearstat.codfil = ?) OR \n";
                    }
                }
            }
            sql += " Group by Seqrota, rastrearstat.codfil) a\n"
                    + " left join rastrear on rastrear.codigo = a.sequencia \n"
                    + " left join escala on escala.seqrota = a.seqrota \n"
                    + "                 and escala.codfil  = a.codfil \n"
                    + " left join funcion on funcion.matr = escala.matrche \n"
                    + "                  and funcion.codfil = escala.codfil \n"
                    + " left join funcion mot on mot.matr   = escala.matrmot \n"
                    + "                      and mot.codfil = escala.codfil \n"
                    + " left join veiculos  on veiculos.numero = escala.veiculo \n"
                    + " left join VeiculosMod  on VeiculosMod.Codigo = Veiculos.Modelo \n"
                    + " order by rastrear.dttrans desc,  rastrear.hrtrans desc \n";
            List<Rastrear> retorno = new ArrayList<>();
            Consulta consulta = new Consulta(sql, persistencia);
            for (Escala seqRota : seqRotas) {
                consulta.setBigDecimal(seqRota.getSeqRota());
                consulta.setBigDecimal(seqRota.getCodFil());
            }

            consulta.select();
            Rastrear loc;
            while (consulta.Proximo()) {
                loc = new Rastrear();

                loc.setRota(consulta.getString("rota"));
                loc.setNome(consulta.getString("nome"));
                loc.setNomeMotorista(consulta.getString("motNome"));

                loc.setData(consulta.getLocalDate("data"));
                loc.setHora(consulta.getString("hora"));
                loc.setLatitude(consulta.getString("latitude"));
                loc.setLongitude(consulta.getString("longitude"));
                loc.setPlaca(consulta.getString("placa"));
                loc.setVeiculo(consulta.getString("veiculo").replace(".0", ""));
                loc.setModeloVeiculo(consulta.getString("ModeloVeic"));

                loc.setSeqRota(consulta.getString("seqrota"));
                loc.setAtendimento(consulta.getString("Atendimento"));
                retorno.add(loc);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar localização - " + e.getMessage());
        }
    }

    /**
     * Retorna a última localização do veículo
     *
     * @param seqRota
     * @param codFil
     * @param persistencia - conexÃ£o ao banco
     * @return - prÃ³ximo cÃ³digo
     * @throws Exception
     */
    public Rastrear buscarLocalizacao(String seqRota, String codFil, Persistencia persistencia) throws Exception {
        try {
            StringBuilder sb = new StringBuilder();

            sb.append("SELECT TOP 1 ");
            sb.append("0 Atendimento, ");
            sb.append("escala.rota, ");
            sb.append("CASE WHEN funcion.nome IS NULL THEN funcionMot.Nome ELSE funcion.nome END nome, ");
            sb.append("escala.veiculo, ");
            sb.append("veiculos.placa, ");
            sb.append("VeiculosMod.Descricao modeloVeic, ");
            sb.append("funcion.nome nomeChe, ");
            sb.append("funcionMot.nome nomeMot, ");
            sb.append("rastrear.latitude, ");
            sb.append("rastrear.longitude, ");
            sb.append("rastrear.data, ");
            sb.append("rastrear.hora, ");
            sb.append("escala.hora1, ");
            sb.append("escala.hora2, ");
            sb.append("escala.hora3, ");
            sb.append("escala.hora4, ");
            sb.append("0 entOk, ");
            sb.append("0 entPd, ");
            sb.append("0 entGuias, ");
            sb.append("0 entLacres, ");
            sb.append("0 entValor, ");
            sb.append("0 entPdGuias, ");
            sb.append("0 entPdValor, ");
            sb.append("0 recOk, ");
            sb.append("0 recPd, ");
            sb.append("0 recGuias, ");
            sb.append("0 recLacres, ");
            sb.append("0 recValor, ");
            sb.append("0 entPdLacres, ");
            sb.append("0 ValorRecepCXF ");
            sb.append("FROM rastrear (NOLOCK) ");
            sb.append("LEFT JOIN rastrearstat (NOLOCK) ON rastrear.codigo = rastrearstat.sequencia ");
            sb.append("LEFT JOIN Pessoa (NOLOCK) ON rastrearstat.CodPessoa = Pessoa.Codigo ");
            sb.append("LEFT JOIN escala (NOLOCK) ON escala.SeqRota = rastrearstat.SeqRota ");
            sb.append("LEFT JOIN funcion (NOLOCK) ON (funcion.matr = escala.matrche OR funcion.matr = escala.matrMot) ");
            sb.append("AND funcion.codfil = escala.codfil ");
            sb.append("LEFT JOIN funcion funcionMot (NOLOCK) ON funcionMot.matr = escala.matrmot ");
            sb.append("AND funcionMot.codfil = escala.codfil ");
            sb.append("LEFT JOIN veiculos (NOLOCK) ON veiculos.numero = escala.veiculo ");
            sb.append("LEFT JOIN VeiculosMod (NOLOCK) ON VeiculosMod.Codigo = veiculos.Modelo ");
            sb.append("WHERE rastrearstat.SeqRota = ? ");
            sb.append("ORDER BY sequencia DESC");
            String sql = sb.toString();

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(seqRota);
            consulta.select();

            Rastrear retorno = new Rastrear();

            while (consulta.Proximo()) {
                retorno.setRota(consulta.getString("rota"));
                retorno.setAtendimento(consulta.getString("Atendimento"));
                retorno.setNome(consulta.getString("nomeChe"));
                retorno.setNomeMotorista(consulta.getString("nomeMot"));
                retorno.setVeiculo(consulta.getString("veiculo"));
                retorno.setPlaca(consulta.getString("placa"));
                retorno.setModeloVeiculo(consulta.getString("modeloVeic"));
                retorno.setData(consulta.getLocalDate("data"));
                retorno.setHora(consulta.getString("hora"));
                retorno.setLatitude(consulta.getString("latitude"));
                retorno.setLongitude(consulta.getString("longitude"));
                retorno.setHora1(consulta.getString("hora1"));
                retorno.setHora2(consulta.getString("hora2"));
                retorno.setHora3(consulta.getString("hora3"));
                retorno.setHora4(consulta.getString("hora4"));

                retorno.setEntOk(consulta.getString("entOk"));
                retorno.setEntPd(consulta.getString("entPd"));
                retorno.setEntGuias(consulta.getString("entGuias"));
                retorno.setEntLacres(consulta.getString("entLacres"));
                retorno.setEntValor(consulta.getString("entValor"));
                retorno.setEntPdGuias(consulta.getString("entPdGuias"));
                retorno.setEntPdValor(consulta.getString("entPdValor"));
                retorno.setEntPdLacres(consulta.getString("entPdLacres"));
                retorno.setRecOk(consulta.getString("recOk"));
                retorno.setRecPd(consulta.getString("recPd"));
                retorno.setRecGuias(consulta.getString("recGuias"));
                retorno.setRecLacres(consulta.getString("recLacres"));
                retorno.setRecValor(consulta.getString("recValor"));
                retorno.setVlrEntDir("0");

                retorno.setCxfEntValor(consulta.getString("ValorRecepCXF"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar localização - " + e.getMessage());
        }
    }

    public void inserirPosicoesSemMatr(String codFil, String data, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            sql = "  INSERT INTO Rastrear(Codigo, Latitude, Longitude, Data, Hora, DtTRans, HrTrans, Matr)\n"
                    + "  SELECT \n"
                    + "  ISNULL((SELECT MAX(Codigo) + X.Contador FROM Rastrear), 1),\n"
                    + "  X.Latitude,\n"
                    + "  X.Longitude,\n"
                    + "  X.Data,\n"
                    + "  X.Hora1,\n"
                    + "  X.Data,\n"
                    + "  X.Hora1,\n"
                    + "  COALESCE((Select TOP 1 Pessoa.Codigo from Escala\n"
                    + "    left join Pessoa on Pessoa.Matr = escala.MatrChe\n"
                    + "    where Escala.Rota = X.Rota\n"
                    + "      and Escala.CodFil = X.CodFil\n"
                    + "      and Escala.Data   = X.Data) ,\n"
                    + "      (\n"
                    + "      SELECT Tb.Codigo FROM(select \n"
                    + "                            Pessoa.Codigo,\n"
                    + "                            ROW_NUMBER() OVER(ORDER BY Pessoa.Codigo ASC) AS Cont\n"
                    + "                            from pessoa\n"
                    + "                            left join Funcion\n"
                    + "                              on Funcion.Matr = Pessoa.Matr\n"
                    + "                            where Funcion.CodFil   = X.CodFil\n"
                    + "                              and Funcion.Funcao   = 'C'\n"
                    + "                              and Funcion.Situacao = 'A') AS Tb\n"
                    + "      WHERE Tb.Cont = X.Contador)) AS Matr\n"
                    + "  FROM(\n"
                    + "  Select \n"
                    + "  Rotas.Rota,\n"
                    + "  Rotas.CodFil,\n"
                    + "  ROW_NUMBER() OVER(ORDER BY Rotas.Rota ASC) AS Contador,\n"
                    + "  (Select top 1 Clientes.Latitude from Rt_Perc\n"
                    + "    left join Clientes on Clientes.Codigo = rt_Perc.CodCli1\n"
                    + "                       and Clientes.CodFil = Rt_perc.CodFil\n"
                    + "    where Rt_Perc.Sequencia = Rotas.Sequencia \n"
                    + "      and Clientes.Latitude is not null\n"
                    + "    order by Hora1) Latitude,\n"
                    + "  (Select top 1 Clientes.Longitude from Rt_Perc\n"
                    + "    left join Clientes on Clientes.Codigo = rt_Perc.CodCli1\n"
                    + "                       and Clientes.CodFil = Rt_perc.CodFil\n"
                    + "    where Rt_Perc.Sequencia = Rotas.Sequencia \n"
                    + "      and Clientes.Latitude is not null\n"
                    + "    order by Hora1) Longitude,\n"
                    + "    Rotas.Data,   \n"
                    + "  (Select top 1 Rt_Perc.Hora1 from Rt_Perc\n"
                    + "    left join Clientes on Clientes.Codigo = rt_Perc.CodCli1\n"
                    + "                       and Clientes.CodFil = Rt_perc.CodFil\n"
                    + "    where Rt_Perc.Sequencia = Rotas.Sequencia \n"
                    + "      and Clientes.Latitude is not null\n"
                    + "    order by Hora1) Hora1\n"
                    + "    from Rotas\n"
                    + "    where Rotas.Data   = ?\n"
                    + "      and rotas.CodFil = ?\n"
                    + "      and (Rotas.TpVeic = 'F' or Rotas.TpVeic = 'L')) AS X WHERE X.Latitude IS NOT NULL AND X.Longitude IS NOT NULL;\n"
                    + "	\n"
                    + "	  \n"
                    + "	  \n"
                    + "  INSERT INTO rastrearStat(Sequencia, CodFil, SeqRota, CodPessoa, DataPos, HoraPos, Data, Hora, Placa)\n"
                    + "  SELECT\n"
                    + "  ISNULL((SELECT MAX(Sequencia) + X.Contador FROM rastrearStat), 1),\n"
                    + "  X.CodFil,\n"
                    + "  X.Sequencia,\n"
                    + "  COALESCE((Select TOP 1 Pessoa.Codigo from Escala\n"
                    + "    left join Pessoa on Pessoa.Matr = escala.MatrChe\n"
                    + "    where Escala.Rota = X.Rota\n"
                    + "      and Escala.CodFil = X.CodFil\n"
                    + "      and Escala.Data   = X.Data) ,\n"
                    + "      (\n"
                    + "      SELECT Tb.Codigo FROM(select \n"
                    + "                            Pessoa.Codigo,\n"
                    + "                            ROW_NUMBER() OVER(ORDER BY Pessoa.Codigo ASC) AS Cont\n"
                    + "                            from pessoa\n"
                    + "                            left join Funcion\n"
                    + "                              on Funcion.Matr = Pessoa.Matr\n"
                    + "                            where Funcion.CodFil   = X.CodFil\n"
                    + "                              and Funcion.Funcao   = 'C'\n"
                    + "                              and Funcion.Situacao = 'A') AS Tb\n"
                    + "      WHERE Tb.Cont = X.Contador)),\n"
                    + "  X.Data,\n"
                    + "  X.Hora1,\n"
                    + "  X.Data,\n"
                    + "  X.Hora1,\n"
                    + "  X.Placa\n"
                    + "  FROM(\n"
                    + "  Select \n"
                    + "  Rotas.Rota,\n"
                    + "  ROW_NUMBER() OVER(ORDER BY Rotas.Rota ASC) AS Contador,\n"
                    + "  rotas.CodFil,\n"
                    + "    Rotas.Sequencia , \n"
                    + "    Rotas.Data,   \n"
                    + "  (Select top 1 Rt_Perc.Hora1 from Rt_Perc\n"
                    + "    left join Clientes on Clientes.Codigo = rt_Perc.CodCli1\n"
                    + "                       and Clientes.CodFil = Rt_perc.CodFil\n"
                    + "    where Rt_Perc.Sequencia = Rotas.Sequencia \n"
                    + "      and Clientes.Latitude is not null\n"
                    + "    order by Hora1) Hora1, \n"
                    + "    (Select Veiculos.Placa from Escala\n"
                    + "    left join Veiculos on Veiculos.Numero = Escala.Veiculo\n"
                    + "    where Escala.Rota = Rotas.Rota\n"
                    + "      and Escala.CodFil = Rotas.CodFil\n"
                    + "      and Escala.Data   = Rotas.Data) Placa\n"
                    + "    from Rotas\n"
                    + "    where Rotas.Data   = ?\n"
                    + "      and rotas.CodFil = ?\n"
                    + "      and (Rotas.TpVeic = 'F' or Rotas.TpVeic = 'L')) AS X;";

            Consulta consulta = new Consulta(sql, persistencia);
            
            consulta.setString(data);
            consulta.setString(codFil);

            consulta.setString(data);
            consulta.setString(codFil);

            consulta.insert();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("Falha ao inserir localização - " + e.getMessage());
        }
    }

    /**
     * Lista todas as rotas do dia por filial e a última posição dela.
     *
     * @param data
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Rastrear> buscarUltimaPosicaoRotas(String data, String codFil, Persistencia persistencia) throws Exception {
        List<Rastrear> retorno = new ArrayList<>();

        try {
            StringBuilder sb = new StringBuilder();

            sb.append("SELECT (");
            sb.append("    SELECT count(*)");
            sb.append("    FROM Rt_PercSLA");
            sb.append("    WHERE Rt_PercSLA.Sequencia = Rotas.Sequencia");
            sb.append("        AND len(isnull(Rt_PercSLA.HrChegVei, '')) > 0");
            sb.append("        AND len(isnull(Rt_PercSla.HrSaidaVei, '')) = 0");
            sb.append("    ) Atendimento,");
            sb.append("COALESCE(escala.rota, Rotas.Rota) rota,");
            sb.append("escala.codfil,");
            sb.append("CASE");
            sb.append("    WHEN funcion.Nome IS NULL THEN funcionMot.Nome");
            sb.append("    ELSE funcion.Nome");
            sb.append(" END nome,");
            sb.append(" escala.veiculo,");
            sb.append(" COALESCE(Rt_Escala.Sequencia, escala.SeqRota) AS SeqRota,");

            sb.append(" (SELECT Latitude FROM Rastrear WHERE Codigo = ");
            sb.append("    (SELECT MAX(R.Codigo) FROM Rastrear R");
            sb.append("     LEFT JOIN RastrearStat RS ON RS.Sequencia = R.Codigo");
            sb.append("     WHERE RS.SeqRota = Rotas.Sequencia)) Latitude,");

            sb.append(" (SELECT Longitude FROM Rastrear WHERE Codigo = ");
            sb.append("    (SELECT MAX(R.Codigo) FROM Rastrear R");
            sb.append("     LEFT JOIN RastrearStat RS ON RS.Sequencia = R.Codigo");
            sb.append("     WHERE RS.SeqRota = Rotas.Sequencia)) Longitude,");

            sb.append("(SELECT Data FROM Rastrear WHERE Codigo = ");
            sb.append("    (SELECT MAX(R.Codigo) FROM Rastrear R");
            sb.append("     LEFT JOIN RastrearStat RS ON RS.Sequencia = R.Codigo");
            sb.append("     WHERE RS.SeqRota = Rotas.Sequencia)) Data,");

            sb.append("(SELECT Hora FROM Rastrear WHERE Codigo = ");
            sb.append("    (SELECT MAX(R.Codigo) FROM Rastrear R");
            sb.append("     LEFT JOIN RastrearStat RS ON RS.Sequencia = R.Codigo");
            sb.append("     WHERE RS.SeqRota = Rotas.Sequencia)) Hora,");

            sb.append(" veiculos.placa,");
            sb.append(" VeiculosMod.Descricao modeloVeic,");
            sb.append(" funcion.nome nomeChe,");
            sb.append(" funcionMot.nome nomeMot,");
            sb.append(" escala.hora1,");
            sb.append(" escala.hora2,");
            sb.append(" escala.hora3,");
            sb.append(" escala.hora4,");

            sb.append(" (SELECT COUNT(*) FROM Rt_Perc RtP WHERE RtP.Sequencia = Rotas.Sequencia AND RtP.ER = 'E' AND RtP.HrCheg <> '' AND RtP.Flag_Excl <> '') entOk,");
            sb.append(" (SELECT COUNT(*) FROM Rt_Perc RtP WHERE RtP.Sequencia = Rotas.Sequencia AND RtP.ER = 'E' AND (RtP.HrCheg IS NULL OR RtP.HrCheg = '') AND RtP.Flag_Excl <> '') entPd,");

            sb.append(" ISNULL(NULL, 0) entGuias,");
            sb.append(" ISNULL(NULL, 0) entLacres,");
            sb.append(" ISNULL(NULL, 0) entValor,");
            sb.append(" ISNULL(NULL, 0) entPdGuias,");
            sb.append(" ISNULL(NULL, 0) entPdValor,");

            sb.append(" (SELECT COUNT(*) FROM Rt_Perc RtP WHERE RtP.Sequencia = Rotas.Sequencia AND RtP.ER = 'R' AND RtP.HrCheg <> '' AND RtP.Flag_Excl <> '') recOk,");
            sb.append(" (SELECT COUNT(*) FROM Rt_Perc RtP WHERE RtP.Sequencia = Rotas.Sequencia AND RtP.ER = 'R' AND (RtP.HrCheg IS NULL OR RtP.HrCheg = '') AND RtP.Flag_Excl <> '') recPd,");

            sb.append(" ISNULL(NULL, 0) recGuias,");
            sb.append(" ISNULL(NULL, 0) recLacres,");
            sb.append(" ISNULL(NULL, 0) recValor,");
            sb.append(" ISNULL(NULL, 0) entPdLacres,");
            sb.append(" ISNULL(NULL, 0) ValorRecepCXF,");
            sb.append(" ISNULL(NULL, 0) ValorEntDir,");
            sb.append(" ISNULL(NULL, 0) ServAtrasados,");
            sb.append(" ISNULL(NULL, 0) SrvEfetivos,");
            sb.append(" ISNULL(NULL, 0) SrvAdiantados,");
            sb.append(" ISNULL(NULL, 0) SrvAtrasados,");
            sb.append(" ISNULL(NULL, 0) SrvPendentes");

            sb.append(" FROM Rotas (NOLOCK)");
            sb.append(" LEFT JOIN escala ON escala.SeqRota = Rotas.Sequencia");
            sb.append(" LEFT JOIN Rt_escala ON Rt_Escala.Sequencia = Rotas.Sequencia AND Rt_Escala.Matr = MatrChe");
            sb.append(" LEFT JOIN Pessoa ON Pessoa.Codigo = Rt_escala.Matr");
            sb.append(" LEFT JOIN funcion ON funcion.matr = escala.matrche AND funcion.codfil = escala.codfil");
            sb.append(" LEFT JOIN funcion funcionMot ON funcionMot.matr = escala.matrmot AND funcionMot.codfil = escala.codfil");
            sb.append(" LEFT JOIN veiculos ON veiculos.numero = escala.veiculo");
            sb.append(" LEFT JOIN VeiculosMod ON VeiculosMod.Codigo = Veiculos.Modelo");

            sb.append(" WHERE Rotas.Data = ?");
            sb.append("    AND Rotas.Flag_Excl <> '*'");
            sb.append("    AND Rotas.TpVeic <> 'N' AND  COALESCE(Rt_Escala.Sequencia, escala.SeqRota) <> ''");

            if (!codFil.equals("")) {
                sb.append("    AND Rotas.codFil = ?");
            }
            
            sb.append(" ORDER BY Rota");
                        
            String sql = sb.toString();
            Consulta consulta;
            
            consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            consulta.select();
            Rastrear rastrear;
            while (consulta.Proximo()) {
                rastrear = new Rastrear();
                rastrear.setRota(consulta.getString("rota"));
                rastrear.setSeqRota(consulta.getString("SeqRota"));
                rastrear.setAtendimento(consulta.getString("Atendimento"));
                rastrear.setCodFil(consulta.getString("CodFil"));
                rastrear.setNome(consulta.getString("nomeChe"));
                rastrear.setNomeMotorista(consulta.getString("nomeMot"));
                rastrear.setVeiculo(consulta.getString("veiculo"));
                rastrear.setPlaca(consulta.getString("placa"));
                rastrear.setModeloVeiculo(consulta.getString("modeloVeic"));
                rastrear.setData(consulta.getLocalDate("Data"));
                rastrear.setHora(consulta.getString("Hora"));
                rastrear.setLatitude(consulta.getString("Latitude"));
                rastrear.setLongitude(consulta.getString("Longitude"));
                rastrear.setHora1(consulta.getString("hora1"));
                rastrear.setHora2(consulta.getString("hora2"));
                rastrear.setHora3(consulta.getString("hora3"));
                rastrear.setHora4(consulta.getString("hora4"));

                rastrear.setEntOk(consulta.getString("entOk"));
                rastrear.setEntPd(consulta.getString("entPd"));
                rastrear.setEntGuias(consulta.getString("entGuias"));
                rastrear.setEntLacres(consulta.getString("entLacres"));
                rastrear.setEntValor(consulta.getString("entValor"));
                rastrear.setEntPdGuias(consulta.getString("entPdGuias"));
                rastrear.setEntPdValor(consulta.getString("entPdValor"));
                rastrear.setEntPdLacres(consulta.getString("entPdLacres"));
                rastrear.setRecOk(consulta.getString("recOk"));
                rastrear.setRecPd(consulta.getString("recPd"));
                rastrear.setRecGuias(consulta.getString("recGuias"));
                rastrear.setRecLacres(consulta.getString("recLacres"));
                rastrear.setRecValor(consulta.getString("recValor"));

                rastrear.setCxfEntValor(consulta.getString("ValorRecepCXF"));
                rastrear.setVlrEntDir(consulta.getString("ValorEntDir"));
                rastrear.setServAtrasados(consulta.getString("ServAtrasados"));

                rastrear.setSrvEfetivos(consulta.getString("SrvEfetivos"));
                rastrear.setSrvAdiantados(consulta.getString("SrvAdiantados"));
                rastrear.setSrvAtrasados(consulta.getString("SrvAtrasados"));
                rastrear.setSrvPendentes(consulta.getString("SrvPendentes"));

                retorno.add(rastrear);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            if (retorno.size() > 0) {
                return retorno;
            }

            throw new Exception("RastrearDao.buscarUltimaPosicaoRotas - " + e.getMessage());
        }
    }

    public List<Rastrear> posicoesRotas(String seqRota, Persistencia persistencia) throws Exception {
        try {
            List<Rastrear> retorno = new ArrayList<>();
            String sql = " SELECT Rastrear.* FROM Rastrear \n"
                    + " LEFT JOIN RastrearStat ON RastrearStat.Sequencia = Rastrear.Codigo\n"
                    + " WHERE RastrearStat.SeqRota = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(seqRota);
            consulta.select();
            Rastrear rastrear;
            while (consulta.Proximo()) {
                rastrear = new Rastrear();
                rastrear.setCodigo(consulta.getString("Codigo"));
                rastrear.setCODCLI(consulta.getString("CODCLI"));
                rastrear.setID_MODULO(consulta.getString("ID_MODULO"));
                rastrear.setPlaca(consulta.getString("Placa"));
                rastrear.setLatitude(consulta.getString("Latitude"));
                rastrear.setLongitude(consulta.getString("Longitude"));
                rastrear.setData(consulta.getLocalDate("Data"));
                rastrear.setHora(consulta.getString("Hora"));
                rastrear.setVelocidade(consulta.getString("Velocidade"));
                rastrear.setDirecao(consulta.getString("Direcao"));
                rastrear.setRua(consulta.getString("Rua"));
                rastrear.setCidade(consulta.getString("Cidade"));
                rastrear.setIntegrador(consulta.getString("Integrador"));
                rastrear.setEnviado(consulta.getString("Enviado"));
                rastrear.setEnvioMaspasR(consulta.getString("EnvioMaspasR"));
                rastrear.setEnvioCliente(consulta.getString("EnvioCliente"));
                rastrear.setInsereBD(consulta.getString("InsereBD"));
                rastrear.setDtTrans(consulta.getLocalDate("DtTrans"));
                rastrear.setHrTrans(consulta.getString("HrTrans"));
                rastrear.setCodMem(consulta.getString("CodMem"));
                rastrear.setSatelite(consulta.getString("Satelite"));
                rastrear.setGps(consulta.getString("Gps"));
                rastrear.setMovimento(consulta.getString("Movimento"));
                rastrear.setViolacao(consulta.getString("Violacao"));
                rastrear.setStatusPorta(consulta.getString("StatusPorta"));
                rastrear.setPrecisao(consulta.getString("Precisao"));
                rastrear.setMatr(consulta.getString("Matr"));
                rastrear.setBatida(consulta.getString("Batida"));
                retorno.add(rastrear);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RastrearDao.posicoesRotas - " + e.getMessage() + "\r\n"
                    + " SELECT Rastrear.* FROM Rastrear \n"
                    + " LEFT JOIN RastrearStat ON RastrearStat.Sequencia = Rastrear.Codigo\n"
                    + " WHERE RastrearStat.SeqRota = " + seqRota);
        }
    }

    public List<Rastrear> posicaoSupervisores(String codFil, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            List<Rastrear> retorno = new ArrayList<>();
            sql = " SELECT\n"
                    + " RastrearEW.*,\n"
                    + " Pessoa.Nome\n"
                    + " FROM(\n"
                    + "      SELECT\n"
                    + "      MAX(Sequencia) Sequencia\n"
                    + "      FROM RastrearEW\n"
                    + "      GROUP BY CodPessoa\n"
                    + "     ) UltimaPosicao\n"
                    + " Left JOIN RastrearEW\n"
                    + "   ON UltimaPosicao.Sequencia = RastrearEW.Sequencia\n"
                    + " Left JOIN Pessoa\n"
                    + "   ON RastrearEW.CodPessoa = Pessoa.Codigo\n"
                    + " Left JOIN saspw\n"
                    + "   ON RastrearEW.CodPessoa = saspw.CodPessoa\n"
                    + " Left JOIN saspwac\n"
                    + "   ON saspw.Nome = saspwac.Nome\n"
                    + " Left JOIN Funcion\n"
                    + "  ON Pessoa.Matr = Funcion.Matr\n"
                    + " AND saspw.CodFil = Funcion.CodFil\n"
                    + " WHERE saspwac.Sistema = 302001\n"
                    + " AND   saspw.CodFil = ?"
                    + " AND   Funcion.Situacao <> 'D'"
                    + " ORDER BY Pessoa.Nome";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.select();
            Rastrear rastrear;

            while (consulta.Proximo()) {
                rastrear = new Rastrear();
                rastrear.setLatitude(consulta.getString("Latitude"));
                rastrear.setLongitude(consulta.getString("Longitude"));
                rastrear.setData(consulta.getLocalDate("Data"));
                rastrear.setHora(consulta.getString("Hora"));
                rastrear.setNome(consulta.getString("Nome"));
                rastrear.setCodigo(consulta.getString("CodPessoa"));
                retorno.add(rastrear);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RastrearDao.posicaoSupervisores - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public List<Rastrear> posicaoSupervisoresTempoReal(String codPessoa, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            List<Rastrear> retorno = new ArrayList<>();
            sql = "select\n"
                    + "RastrearEWHist.*,\n"
                    + "Pessoa.Nome\n"
                    + "FROM(\n"
                    + "     SELECT\n"
                    + "     MAX(Sequencia) Sequencia\n"
                    + "     FROM RastrearEW\n"
                    + "     GROUP BY CodPessoa\n"
                    + "    ) UltimaPosicao\n"
                    + " Left JOIN RastrearEW\n"
                    + "  ON UltimaPosicao.Sequencia = RastrearEW.Sequencia\n"
                    + " Left JOIN Pessoa\n"
                    + "  ON RastrearEW.CodPessoa = Pessoa.Codigo\n"
                    + " Left JOIN saspw\n"
                    + "  ON RastrearEW.CodPessoa = saspw.CodPessoa\n"
                    + " Left JOIN Funcion\n"
                    + " ON Pessoa.Matr = Funcion.Matr\n"
                    + "AND saspw.CodFil = Funcion.CodFil\n"
                    + " Left JOIN RastrearEW RastrearEWHist\n"
                    + "  ON RastrearEWHist.CodPessoa = RastrearEW.CodPessoa\n"
                    + " AND  RastrearEWHist.Data = RastrearEW.Data\n"
                    + "WHERE Pessoa.Codigo = ?\n"
                    + "ORDER BY Pessoa.Nome";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codPessoa);
            consulta.select();
            Rastrear rastrear;

            while (consulta.Proximo()) {
                rastrear = new Rastrear();
                rastrear.setLatitude(consulta.getString("Latitude"));
                rastrear.setLongitude(consulta.getString("Longitude"));
                rastrear.setData(consulta.getLocalDate("Data"));
                rastrear.setHora(consulta.getString("Hora"));
                rastrear.setNome(consulta.getString("Nome"));
                rastrear.setCodigo(consulta.getString("CodPessoa"));
                retorno.add(rastrear);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RastrearDao.posicaoSupervisores - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public Rastrear posicaoCentral(String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select top 1 Clientes.Ende + ', ' + Clientes.Cidade + '/' + Clientes.Estado Cidade, \n"
                    + "Clientes.CodFil, Clientes.Latitude, Clientes.Longitude, Filiais.Descricao  \n"
                    + "From CXForte \n"
                    + "Left join Clientes  on Clientes.Codigo = CXForte.CodCli \n"
                    + "                   and Clientes.CodFil = CXForte.CodFil \n"
                    + "Left join Filiais   on Filiais.CodFil  = CXForte.CodFil \n"
                    + "Where CXForte.CodFil = ? \n"
                    + "Order by CXForte.DtFecha Desc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.select();
            Rastrear retorno = new Rastrear();
            if (consulta.Proximo()) {
                retorno.setLatitude(consulta.getString("Latitude"));
                retorno.setLongitude(consulta.getString("Longitude"));
                retorno.setNome(consulta.getString("Descricao"));
                retorno.setCidade(consulta.getString("Cidade"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar localização central - " + e.getMessage());
        }
    }

    public Rastrear posicaoCentral(BigDecimal sequencia, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select top 1 Clientes.Ende + ', ' + Clientes.Cidade + '/' + Clientes.Estado Cidade, \n"
                    + "Clientes.CodFil, Clientes.Latitude, Clientes.Longitude, Filiais.Descricao  \n"
                    + "From CXForte \n"
                    + " Left join Rt_Perc        on CXForte.CodFil  = Rt_Perc.CodFil \n"
                    + "Left join Clientes  on Clientes.Codigo = CXForte.CodCli \n"
                    + "                   and Clientes.CodFil = CXForte.CodFil \n"
                    + "Left join Filiais   on Filiais.CodFil  = CXForte.CodFil \n"
                    + "Where Rt_Perc.Sequencia = ? \n"
                    + "Order by CXForte.DtFecha Desc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);
            consulta.select();
            Rastrear retorno = new Rastrear();
            if (consulta.Proximo()) {
                retorno.setLatitude(consulta.getString("Latitude"));
                retorno.setLongitude(consulta.getString("Longitude"));
                retorno.setNome(consulta.getString("Descricao"));
                retorno.setCidade(consulta.getString("Cidade"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar localização central - " + e.getMessage());
        }
    }

    public void inserirPosicaoIncilRegra30Min(String data, Persistencia persistencia) {
        String sql = "";

        try {

            sql = "DECLARE @dataReferencia VARCHAR(8);\n"
                    + " DECLARE @horaReferencia VARCHAR(5);\n"
                    + " DECLARE @horaReferencia30minAdd VARCHAR(5);\n"
                    + " DECLARE @horaReferencia30minSub VARCHAR(5);\n"
                    + " \n"
                    + " SET @dataReferencia = ?;\n"
                    + " SET @horaReferencia = CONVERT(VARCHAR(5),GETDATE(),114);\n"
                    + " SET @horaReferencia30minAdd = CONVERT(VARCHAR(5),DATEADD(\"mi\",  30, GETDATE()),114);\n"
                    + " SET @horaReferencia30minSub = CONVERT(VARCHAR(5),DATEADD(\"mi\", -30, GETDATE()),114);\n"
                    + " \n"
                    + " \n"
                    + " /* Montar Tabela temporária com Rotas/Dados para Insert */\n"
                    + " SELECT\n"
                    + " ROW_NUMBER() OVER(ORDER BY Rotas.Sequencia ASC) AS Contador,\n"
                    + " Rotas.CodFil,\n"
                    + " Rotas.Sequencia SeqRota,\n"
                    + " Escala.MatrChe,\n"
                    + " Pessoa.Codigo CodPessoa,\n"
                    + " @dataReferencia DataPos,\n"
                    + " @horaReferencia HoraPos,\n"
                    + " @dataReferencia Data,\n"
                    + " @horaReferencia Hora\n"
                    + " INTO #tmpRotasPosicao\n"
                    + " FROM Rotas\n"
                    + " LEFT JOIN (SELECT\n"
                    + "            SeqRota,\n"
                    + "            COUNT(*) qtde\n"
                    + "            FROM RastrearStat\n"
                    + "            WHERE Data = @dataReferencia\n"
                    + "            GROUP BY SeqRota) RastrearStatQtde\n"
                    + "   ON Rotas.Sequencia = RastrearStatQtde.SeqRota\n"
                    + " LEFT JOIN Escala\n"
                    + "   ON Rotas.Rota   = Escala.Rota\n"
                    + "  AND Rotas.CodFil = Escala.CodFil\n"
                    + "  AND Rotas.Data   = Escala.Data\n"
                    + "  Left JOIN Pessoa\n"
                    + "   ON Escala.MatrChe = Pessoa.Matr  \n"
                    + " WHERE Rotas.Data = @dataReferencia\n"
                    + " AND   (RastrearStatQtde.qtde IS NULL OR RastrearStatQtde.qtde <= 1)\n"
                    + " AND   (CAST(@horaReferencia30minAdd as time) > CAST(Rotas.HrLargada as time) OR CAST(@horaReferencia30minSub as time) < CAST(Rotas.HrLargada as time))\n"
                    + " ORDER BY Rotas.Sequencia;\n"
                    + " \n"
                    + " \n"
                    + " /* Excluir Posição em Tabela Rastrear e RastrearStat */\n"
                    + " DELETE Rastrear\n"
                    + " FROM RastrearStat\n"
                    + "  Left JOIN Rastrear     \n"
                    + "   ON RastrearStat.Sequencia = Rastrear.Codigo\n"
                    + " WHERE RastrearStat.SeqRota IN (SELECT SeqRota FROM #tmpRotasPosicao) ;\n"
                    + " \n"
                    + " DELETE FROM RastrearStat WHERE SeqRota IN (SELECT SeqRota FROM #tmpRotasPosicao); \n"
                    + " \n"
                    + " \n"
                    + " /* Inserir Posição em RastrearStat */\n"
                    + " INSERT INTO RastrearStat (Sequencia,\n"
                    + "                           CodFil,\n"
                    + "                           SeqRota,\n"
                    + "                           CodPessoa,\n"
                    + "                           DataPOS,\n"
                    + "                           HoraPOS,\n"
                    + "                           Data,\n"
                    + "                           Hora)\n"
                    + " SELECT\n"
                    + " ISNULL((SELECT (MAX(Sequencia) + tblReferencia.Contador) FROM RastrearStat),1),\n"
                    + " tblReferencia.CodFil,\n"
                    + " tblReferencia.SeqRota,\n"
                    + " tblReferencia.CodPessoa,\n"
                    + " tblReferencia.DataPos,\n"
                    + " tblReferencia.HoraPos,\n"
                    + " tblReferencia.Data,\n"
                    + " tblReferencia.Hora\n"
                    + " FROM #tmpRotasPosicao tblReferencia;\n"
                    + " \n"
                    + " \n"
                    + " /* Inserir Posição em Rastrear */\n"
                    + " INSERT INTO Rastrear (Codigo, \n"
                    + "                       Latitude, \n"
                    + "                       Longitude, \n"
                    + "                       Data, \n"
                    + "                       Hora, \n"
                    + "                       DtTrans, \n"
                    + "                       HrTrans, \n"
                    + "                       Satelite, \n"
                    + "                       Matr) \n"
                    + " SELECT\n"
                    + " ISNULL((SELECT MAX(Sequencia) FROM RastrearStat WHERE SeqRota = tblReferencia.SeqRota),1),\n"
                    + " (SELECT TOP 1 Clientes.Latitude  FROM CXForte  Left JOIN Clientes ON CXForte.CodFil = Clientes.CodFil AND CXForte.CodCli = Clientes.Codigo WHERE CXForte.CodFil = tblReferencia.CodFil),\n"
                    + " (SELECT TOP 1 Clientes.Longitude FROM CXForte  Left JOIN Clientes ON CXForte.CodFil = Clientes.CodFil AND CXForte.CodCli = Clientes.Codigo WHERE CXForte.CodFil = tblReferencia.CodFil),\n"
                    + " tblReferencia.DataPos,\n"
                    + " tblReferencia.HoraPos,\n"
                    + " tblReferencia.Data,\n"
                    + " tblReferencia.Hora,\n"
                    + " 'Ger-SATMOB',\n"
                    + " tblReferencia.CodPessoa\n"
                    + " FROM #tmpRotasPosicao tblReferencia\n"
                    + " GROUP BY tblReferencia.codfil, tblReferencia.SeqRota,tblReferencia.DataPos,tblReferencia.HoraPos,tblReferencia.Data,tblReferencia.Hora,tblReferencia.CodPessoa;\n"
                    + " \n"
                    + " \n"
                    + " DROP TABLE #tmpRotasPosicao;";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(data);

            consulta.insert();
            consulta.close();

        } catch (Exception e) {
            // OBS: >> Pode não criar.... Se não houver Caixa Forte/Cliente Lat/Lon para Filial
            String msgErro = e.getMessage();
        }
    }

    public void inserirPosicaoIncilRegra30MinDiaSeguinte(String data, Persistencia persistencia) {
        String sql = "";

        try {

            sql = "DECLARE @dataReferencia VARCHAR(8);\n"
                    + " DECLARE @horaReferencia VARCHAR(5);\n"
                    + " \n"
                    + " SET @dataReferencia = ?;\n"
                    + " SET @horaReferencia = CONVERT(VARCHAR(5),GETDATE(),114);\n"
                    + " \n"
                    + " \n"
                    + " /* Montar Tabela temporária com Rotas/Dados para Insert */\n"
                    + " SELECT\n"
                    + " ROW_NUMBER() OVER(ORDER BY Rotas.Sequencia ASC) AS Contador,\n"
                    + " Rotas.CodFil,\n"
                    + " Rotas.Sequencia SeqRota,\n"
                    + " Escala.MatrChe,\n"
                    + " Pessoa.Codigo CodPessoa,\n"
                    + " Rotas.Data DataPos,\n"
                    + " @horaReferencia HoraPos,\n"
                    + " Rotas.Data Data,\n"
                    + " @horaReferencia Hora\n"
                    + " INTO #tmpRotasPosicao\n"
                    + " FROM Rotas\n"
                    + " LEFT JOIN (SELECT\n"
                    + "            SeqRota,\n"
                    + "            COUNT(*) qtde\n"
                    + "            FROM RastrearStat\n"
                    + "            WHERE Data > @dataReferencia\n"
                    + "            GROUP BY SeqRota) RastrearStatQtde\n"
                    + "   ON Rotas.Sequencia = RastrearStatQtde.SeqRota\n"
                    + " LEFT JOIN Escala\n"
                    + "   ON Rotas.Rota   = Escala.Rota\n"
                    + "  AND Rotas.CodFil = Escala.CodFil\n"
                    + "  AND Rotas.Data   = Escala.Data\n"
                    + " LEFT JOIN Pessoa\n"
                    + "   ON Escala.MatrChe = Pessoa.Matr  \n"
                    + " WHERE Rotas.Data > @dataReferencia\n"
                    + " AND   (RastrearStatQtde.qtde IS NULL OR RastrearStatQtde.qtde <= 1)\n"
                    + " ORDER BY Rotas.Sequencia;\n"
                    + " \n"
                    + " \n"
                    + " /* Excluir Posição em Tabela Rastrear e RastrearStat */\n"
                    + " DELETE Rastrear\n"
                    + " FROM RastrearStat\n"
                    + "  Left JOIN Rastrear     \n"
                    + "   ON RastrearStat.Sequencia = Rastrear.Codigo\n"
                    + " WHERE RastrearStat.SeqRota IN (SELECT SeqRota FROM #tmpRotasPosicao) ;\n"
                    + " \n"
                    + " DELETE FROM RastrearStat WHERE SeqRota IN (SELECT SeqRota FROM #tmpRotasPosicao); \n"
                    + " \n"
                    + " \n"
                    + " /* Inserir Posição em RastrearStat */\n"
                    + " INSERT INTO RastrearStat (Sequencia,\n"
                    + "                           CodFil,\n"
                    + "                           SeqRota,\n"
                    + "                           CodPessoa,\n"
                    + "                           DataPOS,\n"
                    + "                           HoraPOS,\n"
                    + "                           Data,\n"
                    + "                           Hora)\n"
                    + " SELECT\n"
                    + " ISNULL((SELECT (MAX(Sequencia) + tblReferencia.Contador) FROM RastrearStat),1),\n"
                    + " tblReferencia.CodFil,\n"
                    + " tblReferencia.SeqRota,\n"
                    + " tblReferencia.CodPessoa,\n"
                    + " tblReferencia.DataPos,\n"
                    + " tblReferencia.HoraPos,\n"
                    + " tblReferencia.Data,\n"
                    + " tblReferencia.Hora\n"
                    + " FROM #tmpRotasPosicao tblReferencia;\n"
                    + " \n"
                    + " \n"
                    + " /* Inserir Posição em Rastrear */\n"
                    + " INSERT INTO Rastrear (Codigo, \n"
                    + "                       Latitude, \n"
                    + "                       Longitude, \n"
                    + "                       Data, \n"
                    + "                       Hora, \n"
                    + "                       DtTrans, \n"
                    + "                       HrTrans, \n"
                    + "                       Satelite, \n"
                    + "                       Matr) \n"
                    + " SELECT\n"
                    + " ISNULL((SELECT MAX(Sequencia) FROM RastrearStat WHERE SeqRota = tblReferencia.SeqRota),1),\n"
                    + " (SELECT TOP 1 Clientes.Latitude  FROM CXForte  Left JOIN Clientes ON CXForte.CodFil = Clientes.CodFil AND CXForte.CodCli = Clientes.Codigo WHERE CXForte.CodFil = tblReferencia.CodFil ORDER BY CXForte.DtFecha DESC),\n"
                    + " (SELECT TOP 1 Clientes.Longitude FROM CXForte  Left JOIN Clientes ON CXForte.CodFil = Clientes.CodFil AND CXForte.CodCli = Clientes.Codigo WHERE CXForte.CodFil = tblReferencia.CodFil ORDER BY CXForte.DtFecha DESC),\n"
                    + " tblReferencia.DataPos,\n"
                    + " tblReferencia.HoraPos,\n"
                    + " tblReferencia.Data,\n"
                    + " tblReferencia.Hora,\n"
                    + " 'Ger-SATMOB',\n"
                    + " tblReferencia.CodPessoa\n"
                    + " FROM #tmpRotasPosicao tblReferencia;\n"
                    + " \n"
                    + " \n"
                    + " DROP TABLE #tmpRotasPosicao;";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(data);

            consulta.insert();
            consulta.close();

        } catch (Exception e) {
            // OBS: >> Pode não criar.... Se não houver Caixa Forte/Cliente Lat/Lon para Filial
            String msgErro = e.getMessage();
        }
    }

    public void inserirPosicaoInicial(Rt_Escala escalas, Double centroLat, Double centroLon, Persistencia persistencia) {
        try {
            StringBuilder str = new StringBuilder();

            str.append("DECLARE @CodFil AS INT;\n");
            str.append(" SET @CodFil = (SELECT TOP 1 CodFil FROM Rotas WHERE Sequencia = ?);");

            str.append(" DELETE Rastrear\n");
            str.append(" FROM RastrearStat\n");
            str.append("  Left JOIN Rastrear     \n");
            str.append("   ON RastrearStat.Sequencia = Rastrear.Codigo\n");
            str.append(" WHERE RastrearStat.SeqRota = ?; \n");
            str.append(" DELETE FROM RastrearStat WHERE SeqRota = ?; \n");

            str.append(" INSERT INTO RastrearStat (Sequencia, \n");
            str.append("                           CodFil, \n");
            str.append("                           SeqRota, \n");
            str.append("                           CodPessoa, \n");
            str.append("                           DataPOS, \n");
            str.append("                           HoraPOS, \n");
            str.append("                           Data, \n");
            str.append("                           Hora) VALUES(\n");
            str.append(" ISNULL((SELECT (MAX(Sequencia) + 1) FROM RastrearStat),1),\n");
            str.append(" @CodFil,\n");
            str.append(" ?,\n");
            str.append(" (SELECT Codigo FROM Pessoa WHERE Matr = ?),\n");
            str.append(" ?,\n");
            str.append(" ?,\n");
            str.append(" ?,\n");
            str.append(" ?);\n");

            str.append(" INSERT INTO Rastrear (Codigo, \n");
            str.append("                       Latitude, \n");
            str.append("                       Longitude, \n");
            str.append("                       Data, \n");
            str.append("                       Hora, \n");
            str.append("                       DtTrans, \n");
            str.append("                       HrTrans, \n");
            str.append("                       Satelite, \n");
            str.append("                       Matr) VALUES(\n");
            str.append(" ISNULL((SELECT MAX(Sequencia) FROM RastrearStat WHERE SeqRota = ?),1),\n");
            str.append(" ?,\n");
            str.append(" ?,\n");
            str.append(" ?,\n");
            str.append(" ?,\n");
            str.append(" ?,\n");
            str.append(" ?,\n");
            str.append(" 'Ger-SATMOB',\n");
            str.append(" (SELECT Codigo FROM Pessoa WHERE Matr = ?));\n");

            Consulta consulta = new Consulta(str.toString(), persistencia);

            // Parâmetros DECLARE
            consulta.setBigDecimal(escalas.getSequencia());

            // Parâmetros DELETE
            consulta.setBigDecimal(escalas.getSequencia());
            consulta.setBigDecimal(escalas.getSequencia());

            // Parâmetros RASTREAR-STAT
            consulta.setBigDecimal(escalas.getSequencia());
            consulta.setBigDecimal(escalas.getMatr());
            consulta.setString(escalas.getDt_alter());
            consulta.setString(escalas.getHr_alter());
            consulta.setString(escalas.getDt_alter());
            consulta.setString(escalas.getHr_alter());

            // Parâmetros RASTREAR
            consulta.setBigDecimal(escalas.getSequencia());
            consulta.setString(Double.toString(centroLat));
            consulta.setString(Double.toString(centroLon));
            consulta.setString(escalas.getDt_alter());
            consulta.setString(escalas.getHr_alter());
            consulta.setString(escalas.getDt_alter());
            consulta.setString(escalas.getHr_alter());
            consulta.setBigDecimal(escalas.getMatr());

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            // OBS: >> Pode não criar.... Se não houver Caixa Forte/Cliente Lat/Lon para Filial
            String msgErro = e.getMessage();
        }
    }

    /**
     * Consulta trajeto do dia por SeqRota e Data
     *
     * @param Sequencia
     * @param Data
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Rastrear consultarDadosTrajetosDia(BigDecimal Sequencia, String Data, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();
        try {

            sql.append("DECLARE @CodigoSequecia AS int");
            sql.append(" DECLARE @DataTrajeto    AS varchar(10)");

            sql.append(" SET @CodigoSequecia = ?");
            sql.append(" SET @DataTrajeto    = ?");

            sql.append(" SELECT TOP 1");
            /* DADOS DO ULTIMO TRAJETO CONCLUIDO */
            sql.append(" TrajetoAnterior.Hora1 AS anterior_perc_hora,");
            sql.append(" TrajetoAnterior.latitude AS anterior_perc_lat,  ");
            sql.append(" TrajetoAnterior.longitude  AS anterior_perc_lon,");
            /* DADOS DA ULTIMA COMUNICACAO */
            sql.append(" UltimaComunicacao.hora AS ultima_comunicacao_hora,");
            sql.append(" UltimaComunicacao.latitude AS ultima_comunicacao_lat,");
            sql.append(" UltimaComunicacao.longitude AS ultima_comunicacao_lon,");
            /* DADOS DO TRAJETO EM ANDAMENTO */
            sql.append(" ProximoTrajeto.Hora1 AS prox_perc_hora1,");
            sql.append(" ProximoTrajeto.latitude AS prox_perc_lat,  ");
            sql.append(" ProximoTrajeto.longitude  AS prox_perc_lon");
            sql.append(" FROM rt_perc (nolock) ");
            sql.append("  Left JOIN Rotas         AS Rotas ");
            sql.append("   ON Rotas.Sequencia = rt_perc.sequencia ");
            sql.append("  AND Rotas.Flag_Excl <> '*'");
            sql.append("  Left JOIN Clientes      AS CliOri ");
            sql.append("   ON CliOri.Codigo = rt_perc.CodCli1 ");
            sql.append("  AND CliOri.CodFil = Rotas.CodFil ");
            sql.append(" LEFT JOIN Clientes AS CliDst ");
            sql.append("   ON CliDst.Codigo = rt_perc.CodCli2");
            sql.append("  AND CliDst.CodFil = Rotas.CodFil ");
            sql.append(" LEFT JOIN (SELECT TOP 1");
            sql.append("            X.sequencia,");
            sql.append("            X.Hora1,");
            sql.append("            X.CodCli1,");
            sql.append("            Z.latitude,");
            sql.append("            Z.longitude");
            sql.append("            FROM rt_perc       AS X");
            sql.append("             Left JOIN Rotas         AS Y ");
            sql.append("              ON X.sequencia = Y.Sequencia");
            sql.append("             AND Y.Flag_Excl <> '*'");
            sql.append("             Left JOIN Clientes     AS Z ");
            sql.append("              ON X.CodCli1 = Z.Codigo");
            sql.append("             AND Y.CodFil = Z.CodFil");
            sql.append("            WHERE X.sequencia = @CodigoSequecia");
            sql.append("            AND   X.HrCheg  IS NULL");
            sql.append("            AND   X.HrSaida IS NULL");
            sql.append("            AND   LEN(Z.latitude) > 0 ");
            sql.append("            AND   LEN(Z.longitude) > 0");
            sql.append("            AND   X.flag_excl <> '*'  ");
            sql.append("            GROUP BY X.sequencia, X.CodCli1, Z.latitude, Z.longitude, X.Hora1");
            sql.append("            ORDER BY X.Hora1) AS ProximoTrajeto");
            sql.append("   ON rt_perc.sequencia = ProximoTrajeto.sequencia");
            sql.append(" LEFT JOIN (SELECT TOP 1");
            sql.append("            X.sequencia,");
            sql.append("            X.Hora1 AS Hora1,");
            sql.append("            X.CodCli1,");
            sql.append("            Z.latitude,");
            sql.append("            Z.longitude");
            sql.append("            FROM rt_perc       AS X");
            sql.append("            Left JOIN Rotas         AS Y ");
            sql.append("              ON X.sequencia = Y.Sequencia");
            sql.append("             AND Y.Flag_Excl <> '*'");
            sql.append("            Left JOIN Clientes     AS Z ");
            sql.append("              ON X.CodCli1 = Z.Codigo");
            sql.append("             AND Y.CodFil = Z.CodFil");
            sql.append("            WHERE X.sequencia = @CodigoSequecia");
            sql.append("            AND   X.HrCheg  IS NOT NULL");
            sql.append("            AND   X.HrSaida IS NOT NULL");
            sql.append("            AND   LEN(Z.latitude) > 0 ");
            sql.append("            AND   LEN(Z.longitude) > 0");
            sql.append("            AND   X.flag_excl <> '*'  ");
            sql.append("            GROUP BY X.sequencia, X.CodCli1, Z.latitude, Z.longitude,X.Hora1, X.HrSaida");
            sql.append("            ORDER BY X.HrSaida DESC) AS TrajetoAnterior");
            sql.append("   ON rt_perc.sequencia = TrajetoAnterior.sequencia");
            sql.append(" LEFT JOIN (SELECT ");
            sql.append("            escala.SeqRota,");
            sql.append("            escala.rota,  ");
            sql.append("            escala.codfil, ");
            sql.append("            rastrear.latitude, ");
            sql.append("            rastrear.longitude, ");
            sql.append("            rastrear.data, ");
            sql.append("            rastrear.hora");
            sql.append("            FROM rastrearstat (nolock) ");
            sql.append("            Left JOIN rastrear (nolock) ");
            sql.append("              ON rastrearstat.sequencia = rastrear.codigo");
            sql.append("            Left JOIN escala (nolock) ");
            sql.append("              ON rastrearstat.seqrota = escala.seqrota");
            sql.append("             AND rastrearstat.codfil = escala.codfil");
            sql.append("            WHERE rastrearstat.Sequencia = (SELECT MAX (Sequencia) ");
            sql.append("                                            FROM rastrearstat");
            sql.append("                                            WHERE rastrearstat.SeqRota = @CodigoSequecia");
            sql.append("                                            AND   rastrearstat.Data    = @DataTrajeto");
            sql.append("                                            GROUP BY SeqRota)  ");
            sql.append("            AND   escala.SeqRota =  @CodigoSequecia) AS UltimaComunicacao");
            sql.append("   ON rt_perc.sequencia = UltimaComunicacao.SeqRota");
            sql.append(" WHERE rt_perc.sequencia = @CodigoSequecia");
            sql.append(" AND   rt_perc.flag_excl <> '*'  ");
            sql.append(" AND   len(cliori.latitude) > 1 ");
            sql.append(" AND   len(cliori.longitude) > 1");
            sql.append(" ORDER BY CASE WHEN len(Rt_Perc.HrCheg) > 0 THEN REPLACE(Rt_Perc.HrCheg,':','') ELSE rt_Perc.Hora1 END");

            Consulta consulta = new Consulta(sql.toString(), persistencia);
            consulta.setBigDecimal(Sequencia);
            consulta.setString(Data);
            consulta.select();

            Rastrear retorno = new Rastrear();

            if (consulta.Proximo()) {
                retorno.setTrajetoDiaPontoAntHora(consulta.getString("anterior_perc_hora"));
                retorno.setTrajetoDiaPontoAntLat(consulta.getString("anterior_perc_lat"));
                retorno.setTrajetoDiaPontoAntLon(consulta.getString("anterior_perc_lon"));

                retorno.setTrajetoDiaPontoUltComHora(consulta.getString("ultima_comunicacao_hora"));
                retorno.setTrajetoDiaPontoUltComLat(consulta.getString("ultima_comunicacao_lat"));
                retorno.setTrajetoDiaPontoUltComLon(consulta.getString("ultima_comunicacao_lon"));

                retorno.setTrajetoDiaPontoProxHora(consulta.getString("prox_perc_hora1"));
                retorno.setTrajetoDiaPontoProxLat(consulta.getString("prox_perc_lat"));
                retorno.setTrajetoDiaPontoProxLon(consulta.getString("prox_perc_lon"));
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao consultar trajetos do dia - " + e.getMessage());
        }
    }
}
