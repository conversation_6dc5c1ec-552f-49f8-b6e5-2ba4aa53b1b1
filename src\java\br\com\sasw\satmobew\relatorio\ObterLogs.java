/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satmobew.relatorio;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.PstLstRelat;
import SasBeansCompostas.LogsSatMobEW;
import br.com.sasw.pacotesuteis.sasdaos.formatadas.LogsSatMobEWDao;
import SasDaos.PstLstRelatDao;
import static SasLibrary.ValidarUsuario.ValidaUsuarioPonto;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import Xml.Xmls;
import br.com.sasw.satmobew.ValidarUsuario;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.List;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "ObterLogs", urlPatterns = {"/relatorio/ObterLogs"})
public class ObterLogs extends HttpServlet {
    
    private ArquivoLog logerro;
    private SasPoolPersistencia pool;
    
    /**
     * Cria a pool de persistencias na criação da servlet
     */
    @Override
    public void init(){
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter resp = response.getWriter(); 
            
        String resposta = "<?xml version=\"1.0\"?>";
        String param = request.getParameter("param");
        String codPessoa = request.getParameter("codpessoa");
        String matricula = request.getParameter("matr");
        String senha = request.getParameter("senha");
        String supervisor = request.getParameter("supervisor");
        String prestador = request.getParameter("prestador");
        String diasS = request.getParameter("dias");
        String dataAtual = request.getParameter("dataAtual");
        String horaAtual = request.getParameter("horaAtual");
        String codFil = request.getParameter("codfil");
        String secao = request.getParameter("secao") == null ? "" : request.getParameter("secao");
        logerro = new ArquivoLog();
        
        int dias = diasS == null ? 0 : Integer.valueOf(diasS);
        if(null == dataAtual || dataAtual.equals("")) dataAtual = DataAtual.getDataAtual("SQL");
        if(null == horaAtual || horaAtual.equals("")) horaAtual = DataAtual.getDataAtual("HORA");
        
        try {
            // Gera o trace com os valores da requisição
            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codPessoa,  param, logerro);
            tStart = System.currentTimeMillis();
            
            //Cria a persistencia
            Persistencia persistencia = pool.getConexao(param);
            
            if (ValidaUsuarioPonto(codPessoa, senha, persistencia)) {
                LogsSatMobEW log;
                DateTimeFormatter dd = DateTimeFormatter.ofPattern("yyyyMMdd");
               
                Calendar c = Calendar.getInstance();
                c.setTime(new SimpleDateFormat("yyyyMMdd").parse(LocalDate.parse(dataAtual, dd).minusDays(dias).format(dd)));
                String tipo = "";
                switch(c.get(Calendar.DAY_OF_WEEK)){
                    case 1:
                        tipo = "Dom";
                        break;
                    case 2:
                    case 3:
                    case 4:
                    case 5:
                    case 6:
                        tipo = "SegSex";
                        break;
                    case 7:
                        tipo = "Sab";
                        break;
                }
             
                StringBuilder sb;
                
                /**
                 * Tipo 0
                 * Cabeçalho da lista
                 */
                log = new LogsSatMobEW();
                log.setTipo("0");
                log.setChave("0;"+LocalDate.parse(dataAtual, dd).minusDays(dias).format(dd)+";cabealho");
                log.setSecao("");
                log.setPosto("");
                log.setFuncionario("");
                log.setData(LocalDate.parse(dataAtual, dd).minusDays(dias).format(dd));
                log.setHora("24:00");
                log.setLatitude("0");
                log.setLongitude("0");
                log.setTitulo("");
                log.setDetalhes("");
                log.setFotos("");
                log.setURL("");
                
                sb = new StringBuilder();
                sb.append(Xmls.tag("tipo",log.getTipo()));
                sb.append(Xmls.tag("chave",log.getChave()));
                sb.append(Xmls.tag("secao",log.getSecao()));
                sb.append(Xmls.tag("posto",log.getPosto()));
                sb.append(Xmls.tag("funcionario",log.getFuncionario()));
                sb.append(Xmls.tag("data",log.getData()));
                sb.append(Xmls.tag("hora",log.getHora()));
                sb.append(Xmls.tag("latitude",log.getLatitude()));
                sb.append(Xmls.tag("longitude",log.getLongitude()));
                sb.append(Xmls.tag("titulo",log.getTitulo()));
                sb.append(Xmls.tag("detalhes",log.getDetalhes()));
                sb.append(Xmls.tag("fotos",log.getFotos()));
                sb.append(Xmls.tag("url",log.getURL()));
                resposta += Xmls.tag("log", sb.toString());
                
                LogsSatMobEWDao dao = new LogsSatMobEWDao();
                List<LogsSatMobEW> logs;
                if (persistencia.getEmpresa().contains("CONFE")){
                   logs = dao.obterLogs(LocalDate.parse(dataAtual, dd).minusDays(dias).format(dd), codFil, 
                            matricula, codPessoa, secao, tipo, persistencia); 
                }else{
                   logs = dao.obterLogs(LocalDate.parse(dataAtual, dd).minusDays(dias).format(dd), codFil, 
                           supervisor.equals("1") ? "" : matricula, supervisor.equals("1") ? "" : codPessoa, secao, tipo, persistencia);
                }
                for(LogsSatMobEW l : logs){
                    l = (LogsSatMobEW) FuncoesString.removeAcentoObjeto(l); 
                    sb = new StringBuilder();
                    sb.append(Xmls.tag("tipo",l.getTipo()));
                    sb.append(Xmls.tag("chave",l.getChave()));
                    sb.append(Xmls.tag("secao",l.getSecao()));
                    sb.append(Xmls.tag("posto",l.getPosto()));
                    sb.append(Xmls.tag("funcionario",l.getFuncionario()));
                    sb.append(Xmls.tag("data",l.getData()));
                    sb.append(Xmls.tag("hora",l.getHora()));
                    sb.append(Xmls.tag("latitude",l.getLatitude()));
                    sb.append(Xmls.tag("longitude",l.getLongitude()));
                    sb.append(Xmls.tag("titulo",l.getTitulo()));
                    sb.append(Xmls.tag("detalhes",l.getDetalhes()));
                    sb.append(Xmls.tag("fotos",l.getFotos()));
                    sb.append(Xmls.tag("url",l.getURL()));
                    resposta += Xmls.tag("log", sb.toString());
                }

                PstLstRelatDao ptLstRelatDao = new PstLstRelatDao();
                List<PstLstRelat> pstLstRelats = ptLstRelatDao.getRelatorios(secao, codFil, persistencia);
                StringBuilder mot, mots = new StringBuilder();
                for(PstLstRelat pstLstRelat : pstLstRelats){
                    mot = new StringBuilder();
                    pstLstRelat = (PstLstRelat) FuncoesString.removeAcentoObjeto(pstLstRelat);
                    mot.append(Xmls.tag("secao",pstLstRelat.getSecao()));
                    mot.append(Xmls.tag("codigo",pstLstRelat.getCodigo()));
                    mot.append(Xmls.tag("descricao",pstLstRelat.getDescricao()));
                    mot.append(Xmls.tag("foto",pstLstRelat.isFoto()));
                    mot.append(Xmls.tag("video",pstLstRelat.isVideo()));
                    mots.append(Xmls.tag("motivos", mot.toString()));
                }
                resposta += mots.toString();
            } else {
                resposta += Xmls.tag("resp", "2");//erro ao validar usuáio
            }
            resp.print(resposta);
            // Opcional: gera um trace com a resposta do servidor
            Trace.gerarTrace(getServletContext(), this.getServletName(),resposta, codPessoa, param, logerro);
            
            // Fecha a conexão
            persistencia.FechaConexao();
            
            // Calcula o tempo gasto pelo servidor entre criar a persistencia e enviar a respota de volta ao android e gera o trace disso.
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0)+" segundos.\r\n", codPessoa, param, logerro); 
        } catch (Exception e) {
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Falha - "+ e.getMessage(), codPessoa, param, logerro);
            resp.print("<?xml version=\"1.0\"?>" + Xmls.tag("resp", "0")+Xmls.tag("erro", e.getMessage()));
        }
    }
    
    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

}
