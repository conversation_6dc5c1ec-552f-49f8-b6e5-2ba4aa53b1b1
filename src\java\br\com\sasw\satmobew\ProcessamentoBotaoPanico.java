/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satmobew;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.QueueFech;
import SasDaos.QueueFechDao;
import SasLibrary.ValidarUsuario;
import Xml.Xmls;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "ProcessamentoBotaoPanico", urlPatterns = {"/ProcessamentoBotaoPanico"})
public class ProcessamentoBotaoPanico extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;

    /**
     * Cria a pool de persistencias na criação da servlet
     */
    @Override
    public void init() {
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(BotaoPanico.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter resp = response.getWriter();

        String resposta = "<?xml version=\"1.0\"?>";
        String param = request.getParameter("param");
        String codpessoa = request.getParameter("codpessoa");
        String senha = request.getParameter("senha");
        logerro = new ArquivoLog();
        
        try {
            // Gera o trace com os valores da requisição
            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codpessoa, param, logerro);
            tStart = System.currentTimeMillis();

            //Cria a persistencia
            Persistencia persistencia = pool.getConexao(param);

            if (ValidarUsuario.ValidaUsuario(codpessoa, senha, persistencia)) {
                
                QueueFechDao queueFechDao = new QueueFechDao();
                QueueFech queueFech = queueFechDao.verificaSolicitacaoPanico(codpessoa, persistencia);
                resposta += Xmls.tag("resp", "1");// sucesso
                resposta += Xmls.tag("Operador", queueFech.getOperador());
                resposta += Xmls.tag("Data", queueFech.getData());
                resposta += Xmls.tag("Hora", queueFech.getHora());
                resposta += Xmls.tag("Comando_Crt", queueFech.getComando_Crt());
            } else {
                resposta += Xmls.tag("resp", "2");// falha validação
            }
            resp.print(resposta);
            // Opcional: gera um trace com a resposta do servidor
            Trace.gerarTrace(getServletContext(), this.getServletName(), resposta, codpessoa, param, logerro);

            // Fecha a conexão
            persistencia.FechaConexao();

            // Calcula o tempo gasto pelo servidor entre criar a persistencia e enviar a respota de volta ao android e gera o trace disso.
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0) + " segundos.\r\n", codpessoa, param, logerro);
        } catch (Exception e) {
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Falha - " + e.getMessage(), codpessoa, param, logerro);
            resp.print("<?xml version=\"1.0\"?>" + Xmls.tag("resp", "0"));
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

}
