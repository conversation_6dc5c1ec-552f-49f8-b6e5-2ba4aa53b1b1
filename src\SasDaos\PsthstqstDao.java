/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import java.math.BigDecimal;
import java.sql.SQLException;

/**
 *
 * <AUTHOR>
 */
public class PsthstqstDao {

    public BigDecimal getSequencia(Persistencia persistencia) throws Exception {
        BigDecimal seq = null;
        try {
            Consulta stm = new Consulta("select max(sequencia) as sequencia from Psthstqst", persistencia);
            stm.select();

            while (stm.Proximo()) {
                seq = new BigDecimal(stm.getString("sequencia"));
            }
            seq = seq.add(new BigDecimal(1));
            stm.Close();
            return seq;
        } catch (SQLException e) {
            throw new Exception("Falha ao carregar sequência - " + e.getMessage());
        }

    }

    public Integer getQtdeFt(String seq, String matr, Persistencia persistencia) throws Exception {
        Integer qtdFt = 0;
        try {
            Consulta stm = new Consulta("select QtdeFotos from Psthstqst where Sequencia=? and matr = ? ", persistencia);
            stm.setString(seq);
            stm.setString(matr);
            stm.select();

            while (stm.Proximo()) {
                qtdFt = stm.getInt("QtdeFotos");
            }
            if (qtdFt == null) {
                qtdFt = 1;
            } else {
                qtdFt++;
            }
            stm.Close();
        } catch (SQLException e) {
            throw new Exception("Falha ao carregar qtde fotos - " + e.getMessage());
        }
        return qtdFt;
    }

    public void updateQtdeFoto(String seq, String matr, Integer qtde, Persistencia persistencia) throws Exception {

        String sql = "update Psthstqst set QtdeFotos = ? "
                + " where Sequencia = ? and matr = ?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(qtde);
            consulta.setString(seq);
            consulta.setString(matr);
            consulta.update();
            consulta.close();
        } catch (SQLException e) {
            throw new Exception("Falha ao atualizar qtde fotos - " + e.getMessage());
        }
    }

    public void updatePsthstqst(String obsNa, String Seq, String Mtr, Integer Quest, Integer Resp, String Det, BigDecimal CodP, Persistencia persistencia) throws Exception {

        //String data_formatada = Utilidades.DataAtual.getDataAtual("TELA");
        String data_atual = br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL");
        String hora = br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA");

        String sql = "update Psthstqst set  Resposta = ?, "
                + " Detalhes = ?, CodPessoa = ?, Dt_Alter = ?, Hr_alter = ? "
                + " where Sequencia = ? "
                + " and Matr = ?"
                + " and CodQuestao = ?";

        try {

            //repete até que
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(Resp);
            consulta.setString(formataQuestoes(Det, obsNa, Quest));
            consulta.setBigDecimal(CodP);
            consulta.setString(data_atual);
            consulta.setString(hora);
            consulta.setString(Seq);
            consulta.setString(Mtr);
            consulta.setInt(Quest);
            consulta.update();
            //stm.execute();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar questionários - " + e.getMessage());
        }
    }

    public void updatePsthstqst(String obsNa, String Seq, String Mtr, Integer Quest, Integer Resp, String Det, BigDecimal CodP, String data_atual, String hora, Persistencia persistencia) throws Exception {

        String sql = "update Psthstqst set  Resposta = ?, "
                + " Detalhes = ?, CodPessoa = ?, Dt_Alter = ?, Hr_alter = ? "
                + " where Sequencia = ? "
                + " and Matr = ?"
                + " and CodQuestao = ?";

        try {

            //repete até que
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(Resp);
            consulta.setString(formataQuestoes(Det, obsNa, Quest));
            consulta.setBigDecimal(CodP);
            consulta.setString(data_atual);
            consulta.setString(hora);
            consulta.setString(Seq);
            consulta.setString(Mtr);
            consulta.setInt(Quest);
            consulta.update();
            //stm.execute();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar questionários - " + e.getMessage());
        }
    }

    public void gravaPsthstqst(String obsNa, String Seq, String Mtr, Integer Quest, Integer Resp, String Det, BigDecimal CodP, Persistencia persistencia) throws Exception {

        //String data_formatada = Utilidades.DataAtual.getDataAtual("TELA");
        String data_atual = br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL");
        String hora = br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA");

        String sql = "insert into Psthstqst (Sequencia, Matr, CodQuestao, Resposta, Detalhes, CodPessoa, Dt_Alter, Hr_alter) "
                + "Values (?,?,?,?,?,?,?,?)";
        try {
            //repete até que
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Seq);
            consulta.setString(Mtr);
            consulta.setInt(Quest);
            consulta.setInt(Resp);
            consulta.setString(formataQuestoes(Det, obsNa, Quest));
            consulta.setBigDecimal(CodP);
            consulta.setString(data_atual);
            consulta.setString(hora);

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao gravar questionário supervisão - " + e.getMessage());
        }
    }

    public void gravaPsthstqst(String obsNa, String Seq, String Mtr, Integer Quest, Integer Resp, String Det, BigDecimal CodP, String data_atual, String hora, Persistencia persistencia) throws Exception {

        String sql = "insert into Psthstqst (Sequencia, Matr, CodQuestao, Resposta, Detalhes, CodPessoa, Dt_Alter, Hr_alter) "
                + "Values (?,?,?,?,?,?,?,?)";
        try {
            //repete até que
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Seq);
            consulta.setString(Mtr);
            consulta.setInt(Quest);
            consulta.setInt(Resp);
            consulta.setString(formataQuestoes(Det, obsNa, Quest));
            consulta.setBigDecimal(CodP);
            consulta.setString(data_atual);
            consulta.setString(hora);

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao gravar questionário supervisão - " + e.getMessage());
        }
    }

    private String formataQuestoes(String det, String obs, Integer n) {
        String quest = null;
        if (!"null".equals(obs)) {
            //quest = "Não se aplica: "+obs + "\r\n";
            quest = obs;
            //quest += "Obs.: "+det;
        } else if (n == 0) {
            quest = det;
        }

        return quest;
    }
//     private String formataQuestoes(String det) {
//        String quest = null;
//        try {
//            String[] sDet = det.split(",");
////            quest = matr.replace(".0", "") + "-";
////            quest += nome + "\r\n";
//
//            for( int i = 0; i < sDet.length; i ++){
//                quest += this.resposta(sDet[i]) + "\r\n";
//		}
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//
//        return quest;
//    }
//
//    private String resposta(String qst) {
//        String resp;
//        if ("1".equals(qst)) {
//            resp = "Sim.";
//        } else if ("2".equals(qst)){
//            resp = "Não.";
//         } else if ("3".equals(qst)){
//            resp = "Não Aplicável.";   
//        }else{
//          resp = qst;
//        }
//     return resp;
//    }
}
