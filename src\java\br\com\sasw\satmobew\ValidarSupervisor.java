package br.com.sasw.satmobew;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.PstServ;
import SasBeansCompostas.Login;
import SasDaos.LoginDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.Collections;
import java.util.List;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "ValidarSupervisor", urlPatterns = {"/ValidarSupervisor"})
public class ValidarSupervisor extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;

    @Override
    public void init() {
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();

        String param = request.getParameter("param");
        String codpessoa = request.getParameter("codpessoa");
        String nomepessoa = request.getParameter("nomepessoa");
        try {

            logerro = new ArquivoLog();

            String xml;

            String senha = request.getParameter("senha");
            String dataAtual = request.getParameter("dataAtual");
            String horaAtual = request.getParameter("horaAtual");

            if (null == dataAtual || dataAtual.equals("")) {
                dataAtual = DataAtual.getDataAtual("SQL");
            }
            if (null == horaAtual || horaAtual.equals("")) {
                horaAtual = DataAtual.getDataAtual("HORA");
            }

            if (codpessoa.equals(DataAtual.getDataAtual("SQL")) && senha.equals(DataAtual.getDataAtual("SQL"))) {
                xml = "<?xml version=\"1.0\"?><resp>1</resp>";
                xml += "<supervisor>"
                        + "1"
                        + "</supervisor>";
                out.print(xml);
            } else {
                long tStart = 0, tEnd;
                Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codpessoa, param, logerro);
                tStart = System.currentTimeMillis();

                Persistencia persistenceLocal = pool.getConexao(param);
                Persistencia persistenceGeral = pool.getConexao("SATELLITE");

                if (null != persistenceLocal) {

                    // Verificando se o login contém seis dígitos e se o primeiro é 8 para verificar se é funcionário ou prestador de serviços.
                    List<Login> login = LoginDao.LoginMobilePonto(codpessoa, nomepessoa, dataAtual, persistenceLocal);

                    if (login.isEmpty() || (!senha.equals(login.get(0).getPessoa().getPWWeb())) && (persistenceLocal.getEmpresa().equals("SATINTERFORT") || persistenceLocal.getEmpresa().equals("SATSERVITE"))) {
                        if (persistenceLocal.getEmpresa().equals("SATSERVITE")) {
                            persistenceLocal = pool.getConexao("SATINTERFORT");
                        } else {
                            persistenceLocal = pool.getConexao("SATSERVITE");
                        }
                        
                        login = LoginDao.LoginMobilePonto(codpessoa, nomepessoa, dataAtual, persistenceLocal);
                    }

                    if (login.isEmpty()) {
                        Trace.gerarTrace(getServletContext(), this.getServletName(), "Codigo incorreto", codpessoa, param, logerro);
                        xml = "<?xml version=\"1.0\"?><resp>mobilelogin_1</resp>"; //codigo incorreto
                    } else if (!senha.equals(login.get(0).getPessoa().getPWWeb())) {
                        Trace.gerarTrace(getServletContext(), this.getServletName(), "Senha incorreta - " + codpessoa, codpessoa, param, logerro);
                        xml = "<?xml version=\"1.0\"?><resp>mobilelogin_3</resp>"; //senha incorreta
                    } else {
                        Trace.gerarTrace(getServletContext(), this.getServletName(), "Carregado com sucesso - " + codpessoa, codpessoa, param, logerro);
                        xml = "<?xml version=\"1.0\"?><resp>1</resp>";
                        xml += "<supervisor>"
                                + (LoginDao.isSupervisorRondas(login.get(0).getPessoa().getCodigo().toPlainString(), persistenceLocal) ? "1" : "0")
                                + "</supervisor>";
                    }

                    //escreve a resposta no buffer de saida
                    out.print(xml);

                    Trace.gerarTrace(getServletContext(), this.getServletName(), xml, codpessoa, param, logerro);

                    persistenceLocal.FechaConexao();
                    persistenceGeral.FechaConexao();

                } else {
                    out.print("<?xml version=\"1.0\"?><resp>mobilelogin_6</resp>");
                }

                tEnd = System.currentTimeMillis();
                NumberFormat formatter = new DecimalFormat("#0.00");
                Trace.gerarTrace(getServletContext(), this.getServletName(), "Tempo gasto: "
                        + formatter.format((tEnd - tStart) / 1000.0) + " segundos.\r\n", codpessoa, param, logerro);
            }
        } catch (Exception e) {
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Falha - " + e.getMessage(), codpessoa, param, logerro);
            //define saida por texto
            response.setContentType("text/plain");
            PrintWriter resp = response.getWriter();
            //escreve a resposta no buffer de saida
            resp.print("<?xml version=\"1.0\"?><resp>0</resp><erro>" + e.getMessage() + "</erro>");
        } finally {
            Trace.Erros(getServletContext(), request, logerro);
            out.close();
        }
    }

    private static void orderPostos(List<PstServ> postos) {
        Collections.sort(postos, (Object o1, Object o2) -> {
            int sComp;
            try {
                BigDecimal dist1 = ((PstServ) o1).getDist();
                BigDecimal dist2 = ((PstServ) o2).getDist();
                sComp = dist1.compareTo(dist2);
            } catch (Exception e) {
                sComp = -1;
            }
            return sComp;
        });
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

}
