/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satmobew.ronda;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Rondas;
import SasDaos.PstDepenDao;
import SasDaos.RondasDao;
import static SasLibrary.ValidarUsuario.ValidaUsuarioPonto;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import Xml.Xmls;
import br.com.sasw.satmobew.ValidarUsuario;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "RealizarRonda", urlPatterns = {"/ronda/RealizarRonda"})
public class RealizarRonda extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;
    
    /**
     * Cria a pool de persistencias na criação da servlet
     */
    @Override
    public void init(){
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter resp = response.getWriter(); 
            
        String resposta = "<?xml version=\"1.0\"?>";
        String param = request.getParameter("param");
        String sCodPessoa = request.getParameter("codpessoa");
        String senha = request.getParameter("senha");
        String secao = request.getParameter("secao");
        String codfil = request.getParameter("codfil");
        String codDepen = request.getParameter("codigo");
        String matr = request.getParameter("matricula");
        String latitude = request.getParameter("latitude");
        String longitude = request.getParameter("longitude");
        String qrcode = request.getParameter("qrcode");
        String operador = FuncoesString.RecortaAteEspaço(request.getParameter("operador"),0,10);
        String dataAtual = request.getParameter("dataAtual");
        String horaAtual = request.getParameter("horaAtual");
        logerro = new ArquivoLog();
        
        if(null == dataAtual || dataAtual.equals("")) dataAtual = DataAtual.getDataAtual("SQL");
        if(null == horaAtual || horaAtual.equals("")) horaAtual = DataAtual.getDataAtual("HORA");
        
        try {
            // Gera o trace com os valores da requisição
            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), sCodPessoa,  param, logerro);
            tStart = System.currentTimeMillis();
            
            //Cria a persistencia
            Persistencia persistencia = pool.getConexao(param);
            
            if (ValidaUsuarioPonto(sCodPessoa, senha, persistencia)) {
                
                PstDepenDao pstDepenDao = new PstDepenDao();
                String qrCodePstDepen = pstDepenDao.getQRCode(secao, codfil, codDepen, persistencia);
                if(qrCodePstDepen.equals(qrcode)){
                    String dtCompet = dataAtual;
                    
                    // ALTERAÇÃO PRO-SECUR 21/06/2018
                    // Salvar a data que foi realizada, e não a competência da ronda.
                    
//                    /**
//                     * Se a batida for antes do meio dia e não houverem batidas anteriores, verificar se no dia da semana anterior
//                     * há previsão de trabalho diuturno (se for quarta verificar parâmetro de terça).
//                     * Nesse caso o sistema deve considerar que é saída do dia anterior. Essa batida receberá DtCompet do dia anterior.
//                     */
//                    LocalTime h = LocalTime.parse(horaAtual, DateTimeFormatter.ofPattern("HH:mm"));
//                    if(h.isBefore(LocalTime.parse("12:00", DateTimeFormatter.ofPattern("HH:mm")))){
//                        /**
//                         * 0 - Domingo 1
//                         * 1 - Segunda 1
//                         * 2 - Terça 1
//                         * 3 - Quarta 1
//                         * 4 - Quinta 1
//                         * 5 - Sexta 1
//                         * 6 - Sábado 1
//                         * 7 - Feriado 1
//                         * = 11111111
//                         */
//                        FuncionDao funcionDao = new FuncionDao();
//                        String diuturno = funcionDao.getDiuTurnoFuncion(matr, persistencia);
//
//                        DateTimeFormatter dd = DateTimeFormatter.ofPattern("yyyyMMdd");
//                        /**
//                         * 1 - segunda, 2 - terça, 3 - quarta, 4 - quinta, 5 - sexta, 6 - sábado, 7 - domingo
//                         */
//                        int ontem;
//                        if(LocalDate.parse(dataAtual, dd).getDayOfWeek().getValue() == 7) ontem = 0;
//                        else ontem = LocalDate.parse(dataAtual, dd).getDayOfWeek().getValue() - 1;
//
//                        /**
//                         * Verifica se a escala do dia anterior é notura.
//                         */
//                        if(diuturno.charAt(ontem) == '1') dtCompet = LocalDate.parse(dtCompet, dd).minusDays(1).format(dd);
//                    }
                    RondasDao rondasDao = new RondasDao();
                    Rondas rondas = new Rondas();
                    rondas.setSecao(secao);
                    rondas.setCodDepen(codDepen);
                    rondas.setCodFil(codfil);
                    rondas.setMatr(matr);
                    rondas.setLatitude(latitude);
                    rondas.setLongitude(longitude);
                    rondas.setOperador(operador);
                    rondas.setData(dtCompet);
                    rondas.setDt_alter(dataAtual);
                    rondas.setHora(Integer.valueOf(horaAtual.split(":")[0]));
                    rondas.setHr_alter(horaAtual);
                    String sequencia = rondasDao.insereRonda(rondas, persistencia);
                    resposta += Xmls.tag("resp", "1");
                    resposta += Xmls.tag("sequencia", sequencia);
                } else resposta += Xmls.tag("resp", "3");
            } 
            else resposta += Xmls.tag("resp", "2");//erro ao validar usuáio
            resp.print(resposta);
            // Opcional: gera um trace com a resposta do servidor
            Trace.gerarTrace(getServletContext(), this.getServletName(),resposta, sCodPessoa, param, logerro);
            
            // Fecha a conexão
            persistencia.FechaConexao();
            
            // Calcula o tempo gasto pelo servidor entre criar a persistencia e enviar a respota de volta ao android e gera o trace disso.
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0)+" segundos.\r\n", sCodPessoa, param, logerro); 
        } catch (Exception e) {
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Falha - "+ e.getMessage(), sCodPessoa, param, logerro);
            resp.print("<?xml version=\"1.0\"?>" + Xmls.tag("resp", "0"));
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

}