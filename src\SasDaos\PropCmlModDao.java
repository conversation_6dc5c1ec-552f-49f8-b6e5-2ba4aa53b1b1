/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PropCmlMod;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PropCmlModDao {

    /**
     * Lista todos os modelos de proposta
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PropCmlMod> listarModelos(Persistencia persistencia) throws Exception {
        List<PropCmlMod> retorno = new ArrayList<>();
        try {
            String sql = "select * from propcmlMod ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            PropCmlMod modelo;
            while (consulta.Proximo()) {
                modelo = new PropCmlMod();
                modelo.setCodigo(consulta.getBigDecimal("Codigo"));
                modelo.setTipo(consulta.getBigDecimal("Tipo"));
                modelo.setModelo(consulta.getBigDecimal("Modelo"));
                modelo.setDescricao(consulta.getString("Descricao"));
                modelo.setDetalhe(consulta.getString("Detalhe"));
                modelo.setOperador(consulta.getString("Operador"));
                modelo.setDt_alter(consulta.getLocalDate("Dt_alter"));
                modelo.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(modelo);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Failed to list modelos de proposta - " + e.getMessage());
        }
    }
}
