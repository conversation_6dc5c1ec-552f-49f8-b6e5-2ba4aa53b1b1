/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satmobew.relatorio;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeansCompostas.LogsSatMobEW;
import br.com.sasw.pacotesuteis.sasdaos.formatadas.LogsSatMobEWDao;
import static SasLibrary.ValidarUsuario.ValidaUsuarioPonto;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import Xml.Xmls;
import br.com.sasw.satmobew.ValidarUsuario;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.List;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "ObterLogsRondas", urlPatterns = {"/relatorio/ObterLogsRondas"})
public class ObterLogsRondas extends HttpServlet {
    
    private ArquivoLog logerro;
    private SasPoolPersistencia pool;
    
    /**
     * Cria a pool de persistencias na criação da servlet
     */
    @Override
    public void init(){
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter resp = response.getWriter(); 
            
        String resposta = "<?xml version=\"1.0\"?>";
        String param = request.getParameter("param");
        String codPessoa = request.getParameter("codpessoa");
        String matricula = request.getParameter("matr");
        String senha = request.getParameter("senha");
        String supervisor = request.getParameter("supervisor");
        String data = request.getParameter("data");
        String hora = request.getParameter("hora");
        String secao = request.getParameter("secao");
        String dataAtual = request.getParameter("dataAtual");
        String horaAtual = request.getParameter("horaAtual");
        String codFil = request.getParameter("codfil");
        logerro = new ArquivoLog();
        
        if(null == dataAtual || dataAtual.equals("")) dataAtual = DataAtual.getDataAtual("SQL");
        if(null == horaAtual || horaAtual.equals("")) horaAtual = DataAtual.getDataAtual("HORA");
        
        try {
            // Gera o trace com os valores da requisição
            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codPessoa,  param, logerro);
            tStart = System.currentTimeMillis();
            
            //Cria a persistencia
            Persistencia persistencia = pool.getConexao(param);
            
            if (ValidaUsuarioPonto(codPessoa, senha, persistencia)) {
                /**
                 * 1 - Batida de Ponto
                 * 2 - Relatório
                 * 3 - Relatório Supervisor
                 * 4 - Resumo Ronda
                 * 5 - Check Ins Supervisores
                 * 6 - Rondas
                 */
                
                // buscar rondas
                LogsSatMobEWDao rondasDao = new LogsSatMobEWDao();
                List<LogsSatMobEW> logs = rondasDao.obterRondaSecao(data, codFil, secao, hora, persistencia);
                
                StringBuilder sb;
                for(LogsSatMobEW l : logs){
                    l = (LogsSatMobEW) FuncoesString.removeAcentoObjeto(l); 
                    sb = new StringBuilder();
                    sb.append(Xmls.tag("tipo",l.getTipo()));
                    sb.append(Xmls.tag("chave",l.getChave()));
                    sb.append(Xmls.tag("secao",l.getSecao()));
                    sb.append(Xmls.tag("posto",l.getPosto()));
                    sb.append(Xmls.tag("funcionario",l.getFuncionario()));
                    sb.append(Xmls.tag("data",l.getData()));
                    sb.append(Xmls.tag("hora",l.getHora()));
                    sb.append(Xmls.tag("latitude",l.getLatitude()));
                    sb.append(Xmls.tag("longitude",l.getLongitude()));
                    sb.append(Xmls.tag("titulo",l.getTitulo()));
                    sb.append(Xmls.tag("detalhes",l.getDetalhes()));
                    sb.append(Xmls.tag("fotos",l.getFotos()));
                    resposta += Xmls.tag("log", sb.toString());
                }
            }
            else {
                resposta += Xmls.tag("resp", "2");//erro ao validar usuáio
            }
            resp.print(resposta);
            // Opcional: gera um trace com a resposta do servidor
            Trace.gerarTrace(getServletContext(), this.getServletName(),resposta, codPessoa, param, logerro);
            
            // Fecha a conexão
            persistencia.FechaConexao();
            
            // Calcula o tempo gasto pelo servidor entre criar a persistencia e enviar a respota de volta ao android e gera o trace disso.
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0)+" segundos.\r\n", codPessoa, param, logerro); 
        } catch (Exception e) {
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Falha - "+ e.getMessage(), codPessoa, param, logerro);
            resp.print("<?xml version=\"1.0\"?>" + Xmls.tag("resp", "0")+Xmls.tag("erro", e.getMessage()));
        }
    }
    
    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

}
