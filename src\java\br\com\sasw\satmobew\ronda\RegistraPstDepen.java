/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satmobew.ronda;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.SASLog;
import SasDaos.PstDepenDao;
import SasDaos.PstDepenRondaDao;
import SasDaos.SASLogDao;
import static SasLibrary.ValidarUsuario.ValidaUsuarioPonto;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import br.com.sasw.satmobew.ValidarUsuario;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "RegistraPstDepen", urlPatterns = {"/ronda/RegistraPstDepen"})
public class RegistraPstDepen extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;
    
    @Override
    public void init(){
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");        
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        logerro = new ArquivoLog();
        
        String codPessoa = request.getParameter("codpessoa");
        String senha = request.getParameter("senha");
        String param = request.getParameter("param");
        
        // PstDepen
        String secao = request.getParameter("secao");
        String codFil = request.getParameter("codfil");      
        String descricao = request.getParameter("descricao");
        String codigo = request.getParameter("codigo");
        String horarios = request.getParameter("horarios");
//        String HrIni = request.getParameter("HrIni");
//        String HrFim = request.getParameter("HrFim");
        String qrCode = request.getParameter("qrcode"); 
//        String NroVig = request.getParameter("NroVig");      
//        String EscAuto = request.getParameter("EscAuto");
        String operador = FuncoesString.RecortaAteEspaço(request.getParameter("operador"),0,10);
//        String Dt_Alter = request.getParameter("Dt_Alter");       
//        String Hr_Alter = request.getParameter("Hr_Alter");
        String latitude = request.getParameter("latitude");
        String longitude = request.getParameter("longitude");
        
        String dataAtual = request.getParameter("dataAtual");
        String horaAtual = request.getParameter("horaAtual");
        String idioma = request.getParameter("idioma");
        if(null == dataAtual || dataAtual.equals("")) dataAtual = DataAtual.getDataAtual("SQL");
        if(null == horaAtual || horaAtual.equals("")) horaAtual = DataAtual.getDataAtual("HORA");
        
        String retorno = "<?xml version=\"1.0\"?>";

        try {
            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codPessoa,  param, logerro);
            tStart = System.currentTimeMillis();

            Persistencia persistencia = pool.getConexao(param);

            if (ValidaUsuarioPonto(codPessoa, senha, persistencia)) {
                PstDepenDao pstDepenDao = new PstDepenDao();
                PstDepenRondaDao pstDepenRondaDao = new PstDepenRondaDao();
                
                /**
                 * Os horários vem como um número inteiro representando uma string binária onde 1 significa ronda no horário 
                 *e 0 não.
                 */
                String[] arrayHorarios = horarios.split(";");
                String segSex, sab, dom;
                segSex = FuncoesString.PreencheEsquerda(Integer.toBinaryString(Integer.valueOf(arrayHorarios[0])),24,"0");
                sab = FuncoesString.PreencheEsquerda(Integer.toBinaryString(Integer.valueOf(arrayHorarios[1])),24,"0");
                dom = FuncoesString.PreencheEsquerda(Integer.toBinaryString(Integer.valueOf(arrayHorarios[2])),24,"0");
                
                /**
                 * Se o código for 0, significa que é uma nova entrada nas tabelas PstDepen e PstDepenRonda.
                 * Se não for, é atualização das mesmas tabelas.
                 */
                String novoCodigo = codigo;
                if(codigo.equals("0")){
                    if(pstDepenDao.verificaExisteTag(qrCode, persistencia)) throw new Exception("TagExiste");
                    novoCodigo = pstDepenDao.inserePstDepen(secao, codFil, descricao, qrCode, operador, dataAtual,
                            horaAtual, new BigDecimal(latitude), new BigDecimal(longitude), persistencia);
                    for(int i = 0; i < segSex.length(); i ++){
                        if(segSex.charAt(i) == '1') pstDepenRondaDao.inserePstDepenRonda(novoCodigo, secao, codFil, "SegSex", i, operador, dataAtual, horaAtual, persistencia);
                    }
                    for(int i = 0; i < sab.length(); i ++){
                        if(sab.charAt(i) == '1') pstDepenRondaDao.inserePstDepenRonda(novoCodigo, secao, codFil, "Sab", i, operador, dataAtual, horaAtual, persistencia);
                    }
                    for(int i = 0; i < dom.length(); i ++){
                        if(dom.charAt(i) == '1') pstDepenRondaDao.inserePstDepenRonda(novoCodigo, secao, codFil, "Dom", i, operador, dataAtual, horaAtual, persistencia);
                    }
                } else {
                    if(!qrCode.equals(pstDepenDao.getQRCode(secao, codFil, codigo, persistencia))){
                        if(pstDepenDao.verificaExisteTag(qrCode, persistencia)) throw new Exception("TagExiste");
                    }
                    pstDepenDao.atualizaPstDepen(codigo, secao, codFil, descricao, operador, dataAtual, horaAtual,
                            new BigDecimal(latitude), new BigDecimal(longitude), qrCode, persistencia);
                    for(int i = 0; i < segSex.length(); i ++){
                        if(segSex.charAt(i) == '1'){
                            if(!pstDepenRondaDao.existePstDepenRonda(secao, codigo, codFil, "SegSex", i, persistencia)){
                                pstDepenRondaDao.inserePstDepenRonda(novoCodigo, secao, codFil, "SegSex", i, operador, dataAtual, horaAtual, persistencia);
                            }
                        } else {
                            if(pstDepenRondaDao.existePstDepenRonda(secao, codigo, codFil, "SegSex", i, persistencia)){
                                String delete = pstDepenRondaDao.deletePstDepenRonda(novoCodigo, secao, codFil, "SegSex", i, persistencia);
                                SASLogDao sasLogDao = new SASLogDao();
                                SASLog saslog;
                                boolean repete = false;
                                int cont = 1;
                                while (!repete) {
                                    String seq_log = sasLogDao.maxSasLog(persistencia);
                                    saslog = new SASLog();
                                    saslog.setSequencia(seq_log);
                                    saslog.setTabela("PstDepenRonda");
                                    saslog.setCodFil(codFil);
                                    saslog.setComando("Delete from PstDepenRonda");
                                    saslog.setHistorico("Excluir horário de ronda por operador mobile.\r\n"+delete);
                                    saslog.setOperador(operador);
                                    saslog.setData(getDataAtual("SQL"));
                                    saslog.setHora(DataAtual.getDataAtual("HORA"));
                                    repete = sasLogDao.inserirSasLog(saslog,persistencia);
                                    if (cont == 20) {
                                        repete = true;
                                    }
                                    cont++;
                                }
                            }
                        }
                    }
                    for(int i = 0; i < sab.length(); i ++){
                        if(sab.charAt(i) == '1'){
                            if(!pstDepenRondaDao.existePstDepenRonda(secao, codigo, codFil, "Sab", i, persistencia)){
                                pstDepenRondaDao.inserePstDepenRonda(novoCodigo, secao, codFil, "Sab", i, operador, dataAtual, horaAtual, persistencia);
                            }
                        } else {
                            if(pstDepenRondaDao.existePstDepenRonda(secao, codigo, codFil, "Sab", i, persistencia)){
                                String delete = pstDepenRondaDao.deletePstDepenRonda(novoCodigo, secao, codFil, "Sab", i, persistencia);
                                SASLogDao sasLogDao = new SASLogDao();
                                SASLog saslog;
                                boolean repete = false;
                                int cont = 1;
                                while (!repete) {
                                    String seq_log = sasLogDao.maxSasLog(persistencia);
                                    saslog = new SASLog();
                                    saslog.setSequencia(seq_log);
                                    saslog.setTabela("PstDepenRonda");
                                    saslog.setCodFil(codFil);
                                    saslog.setComando(delete);
                                    saslog.setHistorico("Excluir horário de ronda por operador mobile.");
                                    saslog.setOperador(operador);
                                    saslog.setData(getDataAtual("SQL"));
                                    saslog.setHora(DataAtual.getDataAtual("HORA"));
                                    repete = sasLogDao.inserirSasLog(saslog,persistencia);
                                    if (cont == 20) {
                                        repete = true;
                                    }
                                    cont++;
                                }
                            }
                        }
                    }
                    for(int i = 0; i < dom.length(); i ++){
                        if(dom.charAt(i) == '1'){
                            if(!pstDepenRondaDao.existePstDepenRonda(secao, codigo, codFil, "Dom", i, persistencia)){
                                pstDepenRondaDao.inserePstDepenRonda(novoCodigo, secao, codFil, "Dom", i, operador, dataAtual, horaAtual, persistencia);
                            }
                        } else {
                            if(pstDepenRondaDao.existePstDepenRonda(secao, codigo, codFil, "Dom", i, persistencia)){
                                String delete = pstDepenRondaDao.deletePstDepenRonda(novoCodigo, secao, codFil, "Dom", i, persistencia);
                                SASLogDao sasLogDao = new SASLogDao();
                                SASLog saslog;
                                boolean repete = false;
                                int cont = 1;
                                while (!repete) {
                                    String seq_log = sasLogDao.maxSasLog(persistencia);
                                    saslog = new SASLog();
                                    saslog.setSequencia(seq_log);
                                    saslog.setTabela("PstDepenRonda");
                                    saslog.setCodFil(codFil);
                                    saslog.setComando(delete);
                                    saslog.setHistorico("Excluir horário de ronda por operador mobile.");
                                    saslog.setOperador(operador);
                                    saslog.setData(getDataAtual("SQL"));
                                    saslog.setHora(DataAtual.getDataAtual("HORA"));
                                    repete = sasLogDao.inserirSasLog(saslog,persistencia);
                                    if (cont == 20) {
                                        repete = true;
                                    }
                                    cont++;
                                }
                            }
                        }
                    }
                }
                retorno += "<resp>1</resp><codigo>"+novoCodigo+"</codigo>";
            }else{
                retorno += "<resp>2</resp>";
            }
            persistencia.FechaConexao();
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0)+" segundos.\r\n", codPessoa, param, logerro); 
        }catch(Exception e){
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Falha RegistraPstDepen - " + e.getMessage(), codPessoa, param, logerro);
            out.print("<?xml version=\"1.0\"?><resp>0</resp><erro>"+e.getMessage()+"</erro>");
        }
        
        out.print(retorno);
    }
    
    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
