/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satmobew.veiculo;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Veiculos;
import SasBeans.VeiculosInspec;
import SasDaos.VeiculosDao;
import SasDaos.VeiculosInspecDao;
import static SasLibrary.ValidarUsuario.ValidaUsuarioPonto;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import Xml.Xmls;
import br.com.sasw.satmobew.ValidarUsuario;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "ObterInspecoesVeiculo", urlPatterns = {"/veiculo/ObterInspecoesVeiculo"})
public class ObterInspecoesVeiculo extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;
    
    @Override
    public void init(){
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

     /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
    
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        logerro = new ArquivoLog();
            
        String codPessoa = request.getParameter("codpessoa");
        String senha = request.getParameter("senha");
        String param = request.getParameter("param");
        String diasS = request.getParameter("dias");
        String dataAtual = request.getParameter("dataAtual");
        String horaAtual = request.getParameter("horaAtual");
        String codfil = request.getParameter("codfil");
        
        int dias = diasS == null ? 0 : Integer.valueOf(diasS);
        if(null == dataAtual || dataAtual.equals("")) dataAtual = DataAtual.getDataAtual("SQL");
        if(null == horaAtual || horaAtual.equals("")) horaAtual = DataAtual.getDataAtual("HORA");
        
        String retorno = "<?xml version=\"1.0\"?>";

        try {
            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codPessoa,  param, logerro);
            tStart = System.currentTimeMillis();
            Persistencia persistencia = pool.getConexao(param);
            if (ValidaUsuarioPonto(codPessoa, senha, persistencia)) {
                DateTimeFormatter dd = DateTimeFormatter.ofPattern("yyyyMMdd");
                DateTimeFormatter dh = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
                VeiculosInspecDao veiculosInspecDao = new VeiculosInspecDao();
                List<VeiculosInspec> inspecoes = veiculosInspecDao.getInspecoesData(LocalDate.parse(dataAtual, dd).minusDays(dias).format(dd),
                        codPessoa, persistencia);
                
                StringBuilder inspec, inspecs = new StringBuilder();
                inspec = new StringBuilder();
                inspec.append(Xmls.tag("Sequencia","-1"));
                inspec.append(Xmls.tag("Numero",""));
                inspec.append(Xmls.tag("Data",LocalDate.parse(dataAtual, dd).minusDays(dias).format(dd)));
                inspec.append(Xmls.tag("Hora","24:00"));
                inspec.append(Xmls.tag("MarcaModelo",""));
                inspec.append(Xmls.tag("KMIni",""));
                inspec.append(Xmls.tag("imgKMIni",""));
                inspec.append(Xmls.tag("KMFim",""));
                inspec.append(Xmls.tag("imgKMFim",""));
                inspec.append(Xmls.tag("Combustivel",""));
                inspec.append(Xmls.tag("imgCombustivel",""));
                inspec.append(Xmls.tag("ProblemaLuzes","0"));
                inspec.append(Xmls.tag("RelatoDanos",""));
                inspec.append(Xmls.tag("imgRelatoDanos",""));
                inspec.append(Xmls.tag("RelatoProblemas",""));
                inspec.append(Xmls.tag("Operador",""));

                inspecs.append(Xmls.tag("inspecoes", inspec.toString()));
                
                for(VeiculosInspec inspecao : inspecoes){
                    inspec = new StringBuilder();
                    inspecao = (VeiculosInspec) FuncoesString.removeAcentoObjeto(inspecao);
                    inspec.append(Xmls.tag("Sequencia",inspecao.getSequencia()));
                    inspec.append(Xmls.tag("Numero",inspecao.getNumero()));
                    inspec.append(Xmls.tag("Data",LocalDate.parse(inspecao.getData(),dh).format(dd)));
                    inspec.append(Xmls.tag("Hora",inspecao.getHora()));
                    inspec.append(Xmls.tag("MarcaModelo",inspecao.getMarcaModelo()));
                    inspec.append(Xmls.tag("KMIni",inspecao.getKMIni()));
                    inspec.append(Xmls.tag("imgKMIni",inspecao.getImgKMIni()));
                    inspec.append(Xmls.tag("KMFim",inspecao.getKMFim()));
                    inspec.append(Xmls.tag("imgKMFim",inspecao.getImgKMFim()));
                    inspec.append(Xmls.tag("Combustivel",inspecao.getCombustivel()));
                    inspec.append(Xmls.tag("imgCombustivel",inspecao.getImgCombustivel()));
                    inspec.append(Xmls.tag("ProblemaLuzes",inspecao.getProblemaLuzes()));
                    inspec.append(Xmls.tag("RelatoDanos",inspecao.getRelatoDanos()));
                    inspec.append(Xmls.tag("imgRelatoDanos",inspecao.getImgRelatoDanos()));
                    inspec.append(Xmls.tag("RelatoProblemas",inspecao.getRelatoProblemas()));
                    inspec.append(Xmls.tag("Operador",inspecao.getOperador()));
                   
                    inspecs.append(Xmls.tag("inspecoes", inspec.toString()));
                }
                retorno += inspecs.toString();
                
                if(dias == 0){
                    VeiculosDao veiculosDao = new VeiculosDao();
                    List<Veiculos> veiculos = veiculosDao.getVeiculos(codfil, persistencia);
                    StringBuilder vei, veis = new StringBuilder();
                    for(Veiculos veiculo : veiculos){
                        veiculo = (Veiculos) FuncoesString.removeAcentoObjeto(veiculo);
                        vei = new StringBuilder();
                        vei.append(Xmls.tag("numero",veiculo.getNumero()));
                        vei.append(Xmls.tag("placa",veiculo.getPlaca()));
                        vei.append(Xmls.tag("modelo",veiculo.getObs()));
                        veis.append(Xmls.tag("veiculos", vei.toString()));
                    }
                    retorno += veis.toString();
                }
                
            } else {
                retorno += "<resp>2</resp>";
            }
            out.print(retorno);
            persistencia.FechaConexao();
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0)+" segundos.\r\n", codPessoa, param, logerro); 
        }catch(Exception e){
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Falha ObterRelatorios - " + e.getMessage(), codPessoa, param, logerro);
            out.print("<?xml version=\"1.0\"?><resp>0</resp>");
        }
    }
    
    
    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}