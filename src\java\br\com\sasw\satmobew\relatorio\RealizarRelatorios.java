/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satmobew.relatorio;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Clientes;
import SasBeans.EmailsEnviar;
import SasBeans.EmailsEnviarAnexo;
import SasBeans.Filiais;
import SasBeans.TmktDet;
import SasBeans.TmktDetPst;
import SasDaos.ClientesDao;
import SasDaos.EmailsEnviarAnexoDao;
import SasDaos.EmailsEnviarDao;
import SasDaos.FiliaisDao;
import SasDaos.PstLstRelatDao;
import SasDaos.TmktDetDao;
import SasDaos.TmktDetPstDao;
import static SasLibrary.ValidarUsuario.ValidaUsuarioPonto;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.LerArquivo;
import static br.com.sasw.pacotesuteis.utilidades.Logos.getLogo;
import static br.com.sasw.pacotesuteis.utilidades.Logos.getLogoAnexo;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import br.com.sasw.satmobew.MultipartMap;
import br.com.sasw.satmobew.ValidarUsuario;
import br.com.sasw.satmobew.mensagem.Messages;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfSignatureAppearance;
import com.itextpdf.text.pdf.PdfStamper;
import com.itextpdf.text.pdf.security.BouncyCastleDigest;
import com.itextpdf.text.pdf.security.DigestAlgorithms;
import com.itextpdf.text.pdf.security.ExternalDigest;
import com.itextpdf.text.pdf.security.ExternalSignature;
import com.itextpdf.text.pdf.security.MakeSignature;
import com.itextpdf.text.pdf.security.MakeSignature.CryptoStandard;
import com.itextpdf.text.pdf.security.PrivateKeySignature;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.font.TextAttribute;
import java.awt.font.TextLayout;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileFilter;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.Security;
import java.security.cert.Certificate;
import java.security.cert.X509Certificate;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.HashMap;
import java.util.Map;
import javax.imageio.ImageIO;
import javax.servlet.ServletException;
import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.io.FileUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.w3c.dom.Document;
import org.w3c.tidy.Tidy;
import org.xhtmlrenderer.pdf.ITextRenderer;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "RealizarRelatorios", urlPatterns = {"/relatorio/RealizarRelatorios"})
@MultipartConfig(location = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobEWTemp", maxFileSize = 50485760L) // 10MB.
public class RealizarRelatorios extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;

    @Override
    public void init() {
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(ValidarUsuario.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        logerro = new ArquivoLog();

        String codPessoa = "SATMOBEW";
        String param = "SATMOBEW";
        try {
            long tStart = 0, tEnd;

            MultipartMap map = new MultipartMap(request, this);
            codPessoa = map.getParameter("codpessoa");
            String senha = map.getParameter("senha");
            param = map.getParameter("param");
            boolean supervisor = request.getParameter("supervisor").equals("1");
            boolean prestador = request.getParameter("prestador").equals("1");
            String empresa = param;
            String data = map.getParameter("data");
            String hora = map.getParameter("hora");
            String secao = map.getParameter("secao");
            String codfil = map.getParameter("codfil");
            String codigo = map.getParameter("codigo");
            String ocorrencia = map.getParameter("ocorrencia");
            String detalhes = map.getParameter("detalhes");
            String operador = map.getParameter("operador");
//            String imagens = map.getParameter("imagens");
            String latitude = map.getParameter("latitude");
            String longitude = map.getParameter("longitude");
            String dataAtual = map.getParameter("dataAtual");
            String horaAtual = map.getParameter("horaAtual");
            String idioma = map.getParameter("idioma");
            String arquivos = map.getParameter("arquivos");
            Messages messages = new Messages();
            messages.setIdioma(idioma);

            String sequencia = request.getParameter("sequencia");

            if (null == dataAtual || dataAtual.equals("")) {
                dataAtual = DataAtual.getDataAtual("SQL");
            }
            if (null == horaAtual || horaAtual.equals("")) {
                horaAtual = DataAtual.getDataAtual("HORA");
            }

            String retorno = "<?xml version=\"1.0\"?>";

            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codPessoa, param, logerro);
            tStart = System.currentTimeMillis();

            Persistencia persistencia = pool.getConexao(param);
            empresa = persistencia.getEmpresa().equals("SATSASEX") ? "SASW" : persistencia.getEmpresa().replace("SAT", "");

            if (ValidaUsuarioPonto(codPessoa, senha, persistencia)) {

                StringBuilder fotos = new StringBuilder();
                String dataHtml = messages.getData(data, "yyyy-MM-dd"), historico, dets;
                boolean filtroWeb;

                if (prestador) {
                    TmktDet tmktDet = new TmktDet();
                    tmktDet.setAndamento("0");
                    tmktDet.setData(data);
                    tmktDet.setHora(hora);
                    tmktDet.setTipoCont("R");
                    tmktDet.setCodPessoa(codPessoa);
                    tmktDet.setCodFil(codfil);
                    tmktDet.setCodCont(secao);
                    tmktDet.setHistorico(ocorrencia.toUpperCase());
                    tmktDet.setDetalhes(detalhes.toUpperCase());
                    tmktDet.setOperador(operador.toUpperCase());
                    tmktDet.setCodPrestAtual(codPessoa);
                    tmktDet.setLatitude(latitude);
                    tmktDet.setLongitude(longitude);
                    tmktDet.setFiltroWeb("0");
                    tmktDet.setPrecisao("0");
                    tmktDet.setQtdeFotos("0");
                    tmktDet = (TmktDet) FuncoesString.removeAcentoObjeto(tmktDet);

                    historico = tmktDet.getHistorico();
                    dets = tmktDet.getDetalhes().replace("\\N", "<br>");
                    filtroWeb = false;

                    TmktDetDao tmktDetDao = new TmktDetDao();
                    if (sequencia.equals("0")) {
                        boolean inserido = false;
                        for (int i = 0; i < 20; i++) {
                            try {
                                sequencia = tmktDetDao.getSequencia(persistencia).toBigInteger().toString();
                                tmktDet.setSequencia(sequencia);
                                tmktDet.setHistorico(tmktDet.getHistorico() + " - " + dataHtml);
                                tmktDetDao.insereTmktDet(tmktDet, dataAtual, horaAtual, persistencia);
                                inserido = true;
                                break;
                            } catch (Exception e) {

                            }
                            if (!inserido) {
                                throw new Exception("Falha inserir sequencia");
                            }
                        }
                    } else {
                        tmktDet.setSequencia(sequencia);
                    }
                    if (arquivos != null) {
                        String[] arquivosArray = arquivos.split(";");
                        tmktDet.setQtdeFotos(String.valueOf(arquivosArray.length));
                        String url = "https://mobile.sasw.com.br:9091/satellite/fotos/" + param + "/" + data.replace("-", "")
                                + "/" + trataMatricula(sequencia.replace(".0", "")) + "/";
                        File file;
                        byte[] bFile;
                        FileInputStream fileInputStream;
                        FileOutputStream fileOuputStream;
                        for (String arquivo : arquivosArray) {
                            // se arquivo conter a palavra thumbnail, não salva, apenas 
                            if (arquivo.contains("thumbnail_")) {
                                arquivo = arquivo.replace("thumbnail_", "");
                            } else {
                                file = map.getFile(arquivo);
                                bFile = new byte[(int) file.length()];

                                //read file into bytes[]
                                fileInputStream = new FileInputStream(file);
                                fileInputStream.read(bFile);

                                File diretorio = new File("C:/xampp/htdocs/satellite/fotos/" + param + "/" + data.replace("-", "")
                                        + "/" + trataMatricula(sequencia.replace(".0", "")) + "/");
                                if (!diretorio.exists()) {
                                    diretorio.mkdirs();  // cria diretórios caso não estejam criados
                                }

                                fileOuputStream = new FileOutputStream("C:/xampp/htdocs/satellite/fotos/" + param + "/" + data.replace("-", "")
                                        + "/" + trataMatricula(sequencia.replace(".0", "")) + "/" + arquivo);

                                fileOuputStream.write(bFile);
                                fileInputStream.close();
                                fileOuputStream.close();

                                if (!arquivo.contains(".mp4")) {
                                    try {
                                        final BufferedImage image = ImageIO.read(new ByteArrayInputStream(bFile));

                                        Graphics g = image.getGraphics();

                                        Font font = Font.decode("Times New Roman");
                                        Graphics2D g1 = (Graphics2D) g;
                                        g1.setRenderingHint(
                                                RenderingHints.KEY_FRACTIONALMETRICS,
                                                RenderingHints.VALUE_FRACTIONALMETRICS_ON);
                                        g1.setRenderingHint(
                                                RenderingHints.KEY_TEXT_ANTIALIASING,
                                                RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

                                        String maior = data + " " + hora;
                                        if (operador.length() > maior.length()) {
                                            maior = operador;
                                        }
                                        if ((latitude + ", " + longitude).length() > maior.length()) {
                                            maior = latitude + ", " + longitude;
                                        }

                                        Rectangle2D r2d = g.getFontMetrics(font).getStringBounds(maior, g);
                                        font = font.deriveFont((float) (font.getSize2D() * 0.03 * image.getHeight() / r2d.getHeight()));

                                        float x = font.getSize2D() * 0.1f;
                                        float y = font.getSize2D();

                                        Map<TextAttribute, Object> atts = new HashMap<TextAttribute, Object>();
                                        atts.put(TextAttribute.KERNING, TextAttribute.KERNING_ON);
                                        font = font.deriveFont(atts);

                                        TextLayout textLayout = new TextLayout(data + " " + hora, font, g1.getFontRenderContext());
                                        g1.setPaint(Color.BLACK);
                                        textLayout.draw(g1, x + 1.5f, y + 1.5f);

                                        g1.setPaint(Color.WHITE);
                                        textLayout.draw(g1, x, y);

                                        y = y + font.getSize2D();
                                        textLayout = new TextLayout(operador, font, g1.getFontRenderContext());
                                        g1.setPaint(Color.BLACK);
                                        textLayout.draw(g1, x + 1.5f, y + 1.5f);

                                        g1.setPaint(Color.WHITE);
                                        textLayout.draw(g1, x, y);

                                        y = y + font.getSize2D();
                                        textLayout = new TextLayout(latitude + ", " + longitude, font, g1.getFontRenderContext());
                                        g1.setPaint(Color.BLACK);
                                        textLayout.draw(g1, x + 1.5f, y + 1.5f);

                                        g1.setPaint(Color.WHITE);
                                        textLayout.draw(g1, x, y);

                                        g.dispose();

                                        ImageIO.write(image, "jpg", new File("C:/xampp/htdocs/satellite/fotos/" + param + "/" + data.replace("-", "")
                                                + "/" + trataMatricula(sequencia.replace(".0", "")) + "/" + arquivo));
                                    } catch (Exception eFoto) {

                                    }
                                }
                            }
                            fotos.append(url).append(arquivo).append(";");
                        }
                    }
                    tmktDet.setFotos(fotos.toString());
                    tmktDetDao.updateTmkt(tmktDet, dataAtual, horaAtual, persistencia);

                } else {

                    PstLstRelatDao pstLstRelatDao = new PstLstRelatDao();
                    TmktDetPst tmktDetPst = new TmktDetPst();
                    tmktDetPst.setAndamento(0);
                    tmktDetPst.setData(data);
                    tmktDetPst.setHora(hora);
                    tmktDetPst.setTipoCont("R" + (supervisor ? "S" : ""));
                    tmktDetPst.setCodPessoa(codPessoa);
                    tmktDetPst.setSecao(secao);
                    tmktDetPst.setCodFil(codfil);
                    tmktDetPst.setHistorico(ocorrencia.toUpperCase());
                    tmktDetPst.setDetalhes(detalhes.toUpperCase());
                    tmktDetPst.setOperador(operador.toUpperCase());
                    tmktDetPst.setLatitude(latitude);
                    tmktDetPst.setLongitude(longitude);
                    tmktDetPst.setFiltroWeb(pstLstRelatDao.getFiltroWeb(secao, codfil, codigo, persistencia));
                    tmktDetPst.setPrecisao("0");
                    tmktDetPst.setQtdeFotos(0);
                    tmktDetPst = (TmktDetPst) FuncoesString.removeAcentoObjeto(tmktDetPst);

                    historico = tmktDetPst.getHistorico();
                    dets = tmktDetPst.getDetalhes().replace("\\N", "<br>");
                    filtroWeb = pstLstRelatDao.getFiltroWeb(secao, codfil, codigo, persistencia);

                    TmktDetPstDao tmktDetPstDao = new TmktDetPstDao();
                    if (sequencia.equals("0")) {
                        boolean inserido = false;
                        for (int i = 0; i < 20; i++) {
                            try {
                                sequencia = tmktDetPstDao.getSequencia(persistencia).toBigInteger().toString();
                                tmktDetPst.setSequencia(sequencia);
                                tmktDetPst.setHistorico(tmktDetPst.getHistorico() + " - " + dataHtml);
                                tmktDetPstDao.insereTmktDetPst(tmktDetPst, dataAtual, horaAtual, persistencia);
                                inserido = true;
                                break;
                            } catch (Exception e) {

                            }
                            if (!inserido) {
                                throw new Exception("Falha inserir sequencia");
                            }
                        }
                    } else {
                        tmktDetPst.setSequencia(sequencia);
                    }
                    if (arquivos != null) {
                        String[] arquivosArray = arquivos.split(";");
                        tmktDetPst.setQtdeFotos(arquivosArray.length);
                        String url = "https://mobile.sasw.com.br:9091/satellite/fotos/" + param + "/" + data.replace("-", "")
                                + "/" + trataMatricula(sequencia.replace(".0", "")) + "/";
                        File file;
                        byte[] bFile;
                        FileInputStream fileInputStream;
                        FileOutputStream fileOuputStream;
                        for (String arquivo : arquivosArray) {
                            // se arquivo conter a palavra thumbnail, não salva, apenas 
                            if (arquivo.contains("thumbnail_")) {
                                arquivo = arquivo.replace("thumbnail_", "");
                            } else {
                                file = map.getFile(arquivo);
                                bFile = new byte[(int) file.length()];

                                //read file into bytes[]
                                fileInputStream = new FileInputStream(file);
                                fileInputStream.read(bFile);

                                File diretorio = new File("C:/xampp/htdocs/satellite/fotos/" + param + "/" + data.replace("-", "")
                                        + "/" + trataMatricula(sequencia.replace(".0", "")) + "/");
                                if (!diretorio.exists()) {
                                    diretorio.mkdirs();  // cria diretórios caso não estejam criados
                                }

                                fileOuputStream = new FileOutputStream("C:/xampp/htdocs/satellite/fotos/" + param + "/" + data.replace("-", "")
                                        + "/" + trataMatricula(sequencia.replace(".0", "")) + "/" + arquivo);

                                fileOuputStream.write(bFile);
                                fileInputStream.close();
                                fileOuputStream.close();

                                if (!arquivo.contains(".mp4")) {
                                    try {
                                        final BufferedImage image = ImageIO.read(new ByteArrayInputStream(bFile));

                                        Graphics g = image.getGraphics();

                                        Font font = Font.decode("Times New Roman");
                                        Graphics2D g1 = (Graphics2D) g;
                                        g1.setRenderingHint(
                                                RenderingHints.KEY_FRACTIONALMETRICS,
                                                RenderingHints.VALUE_FRACTIONALMETRICS_ON);
                                        g1.setRenderingHint(
                                                RenderingHints.KEY_TEXT_ANTIALIASING,
                                                RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

                                        String maior = data + " " + hora;
                                        if (operador.length() > maior.length()) {
                                            maior = operador;
                                        }
                                        if ((latitude + ", " + longitude).length() > maior.length()) {
                                            maior = latitude + ", " + longitude;
                                        }

                                        Rectangle2D r2d = g.getFontMetrics(font).getStringBounds(maior, g);
                                        font = font.deriveFont((float) (font.getSize2D() * 0.03 * image.getHeight() / r2d.getHeight()));

                                        float x = font.getSize2D() * 0.1f;
                                        float y = font.getSize2D();

                                        Map<TextAttribute, Object> atts = new HashMap<TextAttribute, Object>();
                                        atts.put(TextAttribute.KERNING, TextAttribute.KERNING_ON);
                                        font = font.deriveFont(atts);

                                        TextLayout textLayout = new TextLayout(data + " " + hora, font, g1.getFontRenderContext());
                                        g1.setPaint(Color.BLACK);
                                        textLayout.draw(g1, x + 1.5f, y + 1.5f);

                                        g1.setPaint(Color.WHITE);
                                        textLayout.draw(g1, x, y);

                                        y = y + font.getSize2D();
                                        textLayout = new TextLayout(operador, font, g1.getFontRenderContext());
                                        g1.setPaint(Color.BLACK);
                                        textLayout.draw(g1, x + 1.5f, y + 1.5f);

                                        g1.setPaint(Color.WHITE);
                                        textLayout.draw(g1, x, y);

                                        y = y + font.getSize2D();
                                        textLayout = new TextLayout(latitude + ", " + longitude, font, g1.getFontRenderContext());
                                        g1.setPaint(Color.BLACK);
                                        textLayout.draw(g1, x + 1.5f, y + 1.5f);

                                        g1.setPaint(Color.WHITE);
                                        textLayout.draw(g1, x, y);

                                        g.dispose();

                                        ImageIO.write(image, "jpg", new File("C:/xampp/htdocs/satellite/fotos/" + param + "/" + data.replace("-", "")
                                                + "/" + trataMatricula(sequencia.replace(".0", "")) + "/" + arquivo));
                                    } catch (Exception eFoto) {

                                    }
                                }
                            }
                            fotos.append(url).append(arquivo).append(";");
                        }
                    }
                    tmktDetPst.setFotos(fotos.toString());
                    tmktDetPstDao.updateQtdeFoto(sequencia, tmktDetPst.getQtdeFotos(), persistencia);
                    tmktDetPstDao.updateTmkt(tmktDetPst, dataAtual, horaAtual, persistencia);
                }

                ClientesDao clientesDao = new ClientesDao();
                Clientes cliente;
                if (prestador) {
                    cliente = clientesDao.clienteContato(secao, codfil, persistencia); // Secao recebeu o valor do código do contato anteriormente
                } else {
                    cliente = clientesDao.clientePosto(secao, codfil, persistencia);
                }

                FiliaisDao filiaisDao = new FiliaisDao();
                Filiais filial = filiaisDao.getFilial(codfil, persistencia);

                String htmlEmail = LerArquivo.obterConteudo(RealizarRelatorios.class.getResourceAsStream("/relatorios/relatorioEW.html"));
                String htmlAlt;
                String htmlAnexo;

                String padrao1coluna = LerArquivo.obterConteudo(RealizarRelatorios.class.getResourceAsStream("/relatorios/EW_uma_coluna.html"));
                String padrao2colunas = LerArquivo.obterConteudo(RealizarRelatorios.class.getResourceAsStream("/relatorios/EW_duas_colunas.html"));
                String padraoImagem = LerArquivo.obterConteudo(RealizarRelatorios.class.getResourceAsStream("/relatorios/EW_imagem.html"));
                String padraoImagemBotoes = LerArquivo.obterConteudo(RealizarRelatorios.class.getResourceAsStream("/relatorios/EW_imagem_botoes.html"));
                String padraoMapa = LerArquivo.obterConteudo(RealizarRelatorios.class.getResourceAsStream("/relatorios/EW_mapa.html"));
                String padraoVideo = LerArquivo.obterConteudo(RealizarRelatorios.class.getResourceAsStream("/relatorios/EW_video.html"));

                htmlEmail = htmlEmail.replace("@TituloPagina", messages.getMessage("RelatorioEvento") + " - " + sequencia);

                htmlEmail = htmlEmail.replace("@TituloRelatorio", messages.getMessage("RelatorioEvento").toUpperCase());
                htmlEmail = htmlEmail.replace("@SubTituloRelatorio", cliente.getContato());
                htmlEmail = htmlEmail.replace("@TituloInfo", "");
                htmlEmail = htmlEmail.replace("@TituloEndereco", cliente.getEnde() + ", " + cliente.getBairro() + ". " + cliente.getCidade() + "/" + cliente.getEstado());
                htmlEmail = htmlEmail.replace("@TituloTelefone", messages.getTelefone(filial.getFone()));
                htmlEmail = htmlEmail.replace("@Detalhes", messages.getMessage("Detalhes"));

                StringBuilder relatorioEmail = new StringBuilder();
                StringBuilder relatorioAlt = new StringBuilder();
                StringBuilder relatorioAnexo = new StringBuilder();

                relatorioEmail.append(padrao2colunas.replace("@Padrao", messages.getMessage("Local")).replace("@TextoPadrao", cliente.getContato()));
                if (!prestador) {
                    relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getNRed() + " - " + cliente.getNome()));
                }
                relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getEnde() + " - " + cliente.getBairro()));
                relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getCidade() + "/" + cliente.getEstado() + " - "
                        + messages.getCEP(cliente.getCEP())));

                relatorioEmail.append(padrao2colunas.replace("@Padrao", messages.getMessage("Ocorrencia")).replace("@TextoPadrao", historico));
                relatorioEmail.append(padrao2colunas.replace("@Padrao", messages.getMessage("Detalhes")).replace("@TextoPadrao", dets));

                relatorioEmail.append(padrao2colunas.replace("@Padrao", messages.getMessage("Data")).replace("@TextoPadrao", dataHtml));
                relatorioEmail.append(padrao2colunas.replace("@Padrao", messages.getMessage("Hora")).replace("@TextoPadrao", hora));

                String urlEmail = "https://mobile.sasw.com.br:9091/satellite/fotos/" + param + "/";
//                String urlAnexo = "http://localhost:9080/satellite/fotos/" + param + "/";
                String urlAnexo = "file:\\C:\\xampp\\htdocs\\satellite\\fotos\\" + param + "\\";

                String[] fotosArray = fotos.toString().contains(";") ? fotos.toString().split(";") : null;

                if (null != fotosArray) {
                    relatorioEmail.append(padrao1coluna.replace("@Padrao",
                            fotosArray.length == 1 ? messages.getMessage("Foto") : messages.getMessage("Fotos")));
                }

                relatorioAlt.append(relatorioEmail);
                relatorioAnexo.append(relatorioEmail);

                if (null != fotosArray) {
                    for (int i = 1; i <= fotosArray.length; i++) {
                        if (fotosArray[i - 1].contains(".mp4")) {
                            relatorioEmail.append(padraoVideo.replace("@VideoId", "imggirar_" + i)
                                    .replace("@VideoMensagem", messages.getMessage("MensagemVideo"))
                                    .replace("@VideoTipo", "video/mp4")
                                    .replace("@VideoRelatorio", fotosArray[i - 1]));
                            relatorioAlt.append(padraoVideo.replace("@VideoId", "imggirar_" + i)
                                    .replace("@VideoMensagem", messages.getMessage("MensagemVideo"))
                                    .replace("@VideoTipo", "video/mp4")
                                    .replace("@VideoRelatorio", fotosArray[i - 1]));
                            relatorioAnexo.append(padraoVideo.replace("@VideoId", "imggirar_" + i)
                                    .replace("@VideoMensagem", messages.getMessage("MensagemVideo"))
                                    .replace("@VideoTipo", "video/mp4")
                                    .replace("@VideoRelatorio", fotosArray[i - 1].replace(urlEmail, urlAnexo)));
                        } else {
                            relatorioEmail.append(padraoImagem.replace("@ImagemId", "imggirar_" + i)
                                    .replace("@ImagemMensagem", messages.getMessage("ImagemNaoEncontrada"))
                                    .replace("@ImagemRelatorio", fotosArray[i - 1]));
                            relatorioAnexo.append(padraoImagem.replace("@ImagemId", "imggirar_" + i)
                                    .replace("@ImagemMensagem", messages.getMessage("ImagemNaoEncontrada"))
                                    .replace("@ImagemRelatorio", fotosArray[i - 1].replace(urlEmail, urlAnexo)));
                            relatorioAlt.append(padraoImagemBotoes.replace("@ImagemId", "imggirar_" + i)
                                    .replace("@ImagemRelatorio", fotosArray[i - 1])
                                    .replace("@ImagemMensagem", messages.getMessage("ImagemNaoEncontrada"))
                                    .replace("@ImagemBack", "back_" + i)
                                    .replace("@ImagemNext", "next_" + i)
                                    .replace("@GirarEsquerda", messages.getMessage("Girar Esquerda"))
                                    .replace("@GirarDireita", messages.getMessage("Girar Direita")));
                        }
                    }
                }

                relatorioEmail.append(padrao1coluna.replace("@Padrao", messages.getMessage("Mapa")));
                relatorioAlt.append(padrao1coluna.replace("@Padrao", messages.getMessage("Mapa")));
                relatorioAnexo.append(padrao1coluna.replace("@Padrao", messages.getMessage("Mapa")));

                String mapa = FuncoesString.RecortaString(data, 0, 10).replaceAll("-", "") + "/"
                        + trataMatricula(sequencia.replace(".0", "")) + "/mapa.png";

                //enviandoImagemMapa(mapaEstatico(latitude, longitude, cliente.getNRed()), param, trataMatricula(sequencia.replace(".0", "")), data); Desativado em 21/03/2023
                // relatorioEmail.append(padraoImagem.replace("@ImagemId", "mapa")
                //         .replace("@ImagemMensagem", messages.getMessage("ErroMapa"))
                //         .replace("@ImagemRelatorio", mapaEstatico(latitude, longitude, cliente.getNRed()))); Desativado em 21/03/2023
                relatorioAlt.append(padraoMapa.replace("@MensagemErroMapa", messages.getMessage("ErroMapa")));
                relatorioAnexo.append(padraoImagem.replace("@ImagemId", "mapa")
                        .replace("@ImagemMensagem", messages.getMessage("ErroMapa"))
                        .replace("@ImagemRelatorio", urlAnexo + mapa));

                htmlAlt = htmlEmail.replace("@Relatorio", relatorioAlt.toString());
                htmlAnexo = htmlEmail.replace("@Relatorio", relatorioAnexo.toString());
                htmlEmail = htmlEmail.replace("@Relatorio", relatorioEmail.toString());

                htmlEmail = htmlEmail.replace("@Script", "");
                htmlAnexo = htmlAnexo.replace("@Script", "");
                htmlAlt = htmlAlt.replace("@Script", mapa(latitude, longitude, cliente.getNRed(), fotosArray != null ? fotosArray.length : 0));

                htmlEmail = htmlEmail.replace("@ImagemLogo", getLogo(empresa, "0"));
                htmlAnexo = htmlAnexo.replace("@ImagemLogo", getLogoAnexo(empresa, "0"));
                htmlAlt = htmlAlt.replace("@ImagemLogo", getLogo(empresa, "0"));

                String anexoHTML = empresa + "_report_" + sequencia.replace(".0", "") + ".html";
                String anexoPDF = empresa + "_report_" + sequencia.replace(".0", "") + ".pdf";

                htmlEmail = htmlEmail.replace("@URL", "https://mobile.sasw.com.br:9091/Satmobile/documentos/anexo-email/" + anexoHTML);
                htmlAlt = htmlAlt.replace("@URL", "");
                htmlAnexo = htmlAnexo.replace("@URL", "");

                htmlEmail = htmlEmail.replace("@MensagemUrl", messages.getMessage("MensagemUrl"));
                htmlAlt = htmlAlt.replace("@MensagemUrl", "");
                htmlAnexo = htmlAnexo.replace("@MensagemUrl", "");

                File filePDFanexo = new File("C:/xampp/htdocs/satmobile/documentos/anexo-email/");
                filePDFanexo.mkdirs();
                OutputStream osPdf = new FileOutputStream("C:/xampp/htdocs/satmobile/documentos/anexo-email/" + anexoPDF);

                Tidy tidy = new Tidy();
                tidy.setShowWarnings(false);
                InputStream input = new ByteArrayInputStream(htmlAnexo.getBytes());
                Document doc = tidy.parseDOM(input, null);
                ITextRenderer renderer = new ITextRenderer();
                renderer.setDocument(doc, null);
                renderer.layout();
                renderer.createPDF(osPdf);
                input.close();
                osPdf.close();

                if (param.contains("SATPISCINAFACIL")) {
                    try {
                        char[] pass = "06956831fatima".toCharArray();
                        BouncyCastleProvider provider = new BouncyCastleProvider();
                        Security.addProvider(provider);
                        KeyStore ks = KeyStore.getInstance(KeyStore.getDefaultType());
                        ks.load(new FileInputStream(new File(getClass().getResource("/br/com/sasw/satmobew/piscina").toURI())), pass);
                        String alias = (String) ks.aliases().nextElement();
                        PrivateKey pk = (PrivateKey) ks.getKey(alias, pass);
                        Certificate[] chain = ks.getCertificateChain(alias);
                        X509Certificate signerCertificate = (X509Certificate) chain[0];

                        PdfReader reader = new PdfReader("C:/xampp/htdocs/satmobile/documentos/anexo-email/" + anexoPDF);
                        FileOutputStream os = new FileOutputStream("C:/xampp/htdocs/satmobile/documentos/anexo-email/"
                                + empresa + "_sreport_" + sequencia.replace(".0", "") + ".pdf");
                        PdfStamper stamper = PdfStamper.createSignature(reader, os, '\0');
                        // Creating the appearance
                        PdfSignatureAppearance appearance = stamper.getSignatureAppearance();
                        appearance.setReason("AUTENTICAÇÃO");
                        appearance.setLocation("BRASÍLIA");
                        appearance.setCertificate(signerCertificate);
                        Rectangle cropBox = reader.getCropBox(1);
                        float width = 200;
                        float height = 60;
                        appearance.setVisibleSignature(new Rectangle(cropBox.getRight(width), cropBox.getBottom(),
                                cropBox.getRight(), cropBox.getBottom(height)), 1, "sig");
                        // Creating the signature
                        ExternalDigest digest = new BouncyCastleDigest();
                        ExternalSignature signature = new PrivateKeySignature(pk, DigestAlgorithms.SHA256, provider.getName());
                        MakeSignature.signDetached(appearance, digest, signature, chain, null, null, null, 0, CryptoStandard.CMS);

                        anexoPDF = empresa + "_sreport_" + sequencia.replace(".0", "") + ".pdf";
                    } catch (Exception eC) {
                        Trace.gerarTrace(getServletContext(), this.getServletName(), eC.getMessage(), codPessoa, param, logerro);
                    }
                }

                File newHtmlFile = new File("C:/xampp/htdocs/satmobile/documentos/anexo-email/" + anexoHTML);
                FileUtils.writeStringToFile(newHtmlFile, htmlAlt, StandardCharsets.UTF_8);

                if (param.contains("GSI") || param.contains("PROSECUR") || param.contains("GLOVAL")) {
                    EmailsEnviarDao emailDao = new EmailsEnviarDao();
                    EmailsEnviarAnexoDao emailanexodao = new EmailsEnviarAnexoDao();

                    EmailsEnviar email1 = new EmailsEnviar();
                    EmailsEnviar email2 = new EmailsEnviar();
                    EmailsEnviar email3 = new EmailsEnviar();

                    email1.setRemet_email("<EMAIL>");
                    email2.setRemet_email("<EMAIL>");
                    email3.setRemet_email("<EMAIL>");

                    email1.setRemet_nome(operador);
                    email2.setRemet_nome(operador);
                    email3.setRemet_nome(operador);

                    email1.setDest_nome(filial.getRazaoSocial());
                    email2.setDest_nome(filial.getRazaoSocial());
                    email3.setDest_nome(cliente.getContato());

                    email1.setDest_email("<EMAIL>;<EMAIL>");
                    email2.setDest_email("<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>");
                    email3.setDest_email(cliente.getEmail());
                    if (cliente.getEmail().equals("7350001")) {
                        email3.setDest_email("<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>");
                    } else {
                        email3.setDest_email(cliente.getEmail());
                    }

                    email1.setParametro(param);
                    email2.setParametro(param);
                    email3.setParametro(param);
                    email1.setCodFil(codfil);
                    email2.setCodFil(codfil);
                    email3.setCodFil(codfil);
                    email1.setCodCli(cliente.getCodigo());
                    email2.setCodCli(cliente.getCodigo());
                    email3.setCodCli(cliente.getCodigo());

                    String assunto = empresa + " " + messages.getMessage("Relatorio") + " " + sequencia + " " + ocorrencia;

                    email1.setAssunto(assunto);
                    email1.setSmtp("smtplw.com.br");
                    email1.setMensagem(htmlEmail.replace("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">\n", ""));
                    email1.setAut_login("sasw");
                    email1.setAut_senha("xNiadJEj9607");
                    email1.setPorta(587);

                    email2.setAssunto(assunto);
                    email2.setSmtp("smtplw.com.br");
                    email2.setMensagem(htmlEmail.replace("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">\n", ""));
                    email2.setAut_login("sasw");
                    email2.setAut_senha("xNiadJEj9607");
                    email2.setPorta(587);

                    email3.setAssunto(assunto);
                    email3.setSmtp("smtplw.com.br");
                    email3.setMensagem(htmlEmail.replace("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">\n", ""));
                    email3.setAut_login("sasw");
                    email3.setAut_senha("xNiadJEj9607");
                    email3.setPorta(587);

                    Persistencia satellite = pool.getConexao("SATELLITE");
                    String seq1 = emailDao.inserirEmail(email1, satellite);
                    String seq2 = emailDao.inserirEmail(email2, satellite);

                    EmailsEnviarAnexo emailanexo1 = new EmailsEnviarAnexo();
                    emailanexo1.setSequencia(new BigDecimal(seq1));
                    emailanexo1.setOrdem(1);
                    emailanexo1.setEndAnexo(anexoPDF);
                    emailanexo1.setNomeAnexo(anexoPDF);
                    emailanexo1.setDescAnexo(anexoPDF);

                    EmailsEnviarAnexo emailanexo2 = new EmailsEnviarAnexo();
                    emailanexo2.setSequencia(new BigDecimal(seq2));
                    emailanexo2.setOrdem(1);
                    emailanexo2.setEndAnexo(anexoPDF);
                    emailanexo2.setNomeAnexo(anexoPDF);
                    emailanexo2.setDescAnexo(anexoPDF);

                    emailanexodao.inserirAnexos(emailanexo1, satellite);
                    emailanexodao.inserirAnexos(emailanexo2, satellite);

                    if (!email3.getDest_email().equals("") && !supervisor && filtroWeb) {
                        String seq3 = emailDao.inserirEmail(email3, satellite);
                        EmailsEnviarAnexo emailanexo3 = new EmailsEnviarAnexo();
                        emailanexo3.setSequencia(new BigDecimal(seq3));
                        emailanexo3.setOrdem(1);
                        emailanexo3.setEndAnexo(anexoPDF);
                        emailanexo3.setNomeAnexo(anexoPDF);
                        emailanexo3.setDescAnexo(anexoPDF);
                        emailanexodao.inserirAnexos(emailanexo3, satellite);
                    }

                    satellite.FechaConexao();

                } else if (param.contains("SASW") || param.contains("SASEX")) {
                    EmailsEnviarDao emailDao = new EmailsEnviarDao();
                    EmailsEnviarAnexoDao emailanexodao = new EmailsEnviarAnexoDao();

                    EmailsEnviar email1 = new EmailsEnviar();
                    EmailsEnviar email2 = new EmailsEnviar();

                    email1.setRemet_email("<EMAIL>");
                    email2.setRemet_email("<EMAIL>");

                    email1.setRemet_nome(operador);
                    email2.setRemet_nome(operador);

                    email1.setDest_email("<EMAIL>;<EMAIL>;<EMAIL>");
                    email2.setDest_email(cliente.getEmail());

                    email1.setDest_nome(filial.getRazaoSocial());
                    email2.setDest_nome(cliente.getContato());

                    email1.setParametro(param);
                    email2.setParametro(param);
                    email1.setCodFil(codfil);
                    email2.setCodFil(codfil);
                    email1.setCodCli(cliente.getCodigo());
                    email2.setCodCli(cliente.getCodigo());

                    String assunto = empresa + " " + messages.getMessage("Relatorio") + " " + sequencia + " " + ocorrencia;

                    email1.setAssunto(assunto);
                    email1.setSmtp("smtplw.com.br");
                    email1.setMensagem(htmlEmail.replace("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">\n", ""));
                    email1.setAut_login("sasw");
                    email1.setAut_senha("xNiadJEj9607");
                    email1.setPorta(587);

                    email2.setAssunto(assunto);
                    email2.setSmtp("smtplw.com.br");
                    email2.setMensagem(htmlEmail.replace("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">\n", ""));
                    email2.setAut_login("sasw");
                    email2.setAut_senha("xNiadJEj9607");
                    email2.setPorta(587);

                    Persistencia satellite = pool.getConexao("SATELLITE");
                    String seq1 = emailDao.inserirEmail(email1, satellite);

                    EmailsEnviarAnexo emailanexo1 = new EmailsEnviarAnexo();
                    emailanexo1.setSequencia(new BigDecimal(seq1));
                    emailanexo1.setOrdem(1);
                    emailanexo1.setEndAnexo(anexoPDF);
                    emailanexo1.setNomeAnexo(anexoPDF);
                    emailanexo1.setDescAnexo(anexoPDF);

                    emailanexodao.inserirAnexos(emailanexo1, satellite);

                    if (!email2.getDest_email().equals("") && !supervisor && filtroWeb) {
                        String seq2 = emailDao.inserirEmail(email2, satellite);
                        EmailsEnviarAnexo emailanexo2 = new EmailsEnviarAnexo();
                        emailanexo2.setSequencia(new BigDecimal(seq2));
                        emailanexo2.setOrdem(1);
                        emailanexo2.setEndAnexo(anexoPDF);
                        emailanexo2.setNomeAnexo(anexoPDF);
                        emailanexo2.setDescAnexo(anexoPDF);
                        emailanexodao.inserirAnexos(emailanexo2, satellite);
                    }
                    File fHTML = new File(anexoHTML);
                    fHTML.delete();
                    File fPDF = new File(anexoPDF);
                    fPDF.delete();

                    satellite.FechaConexao();
                } else {
                    EmailsEnviarDao emailDao = new EmailsEnviarDao();
                    EmailsEnviarAnexoDao emailanexodao = new EmailsEnviarAnexoDao();

                    EmailsEnviar email1 = new EmailsEnviar();
                    EmailsEnviar email2 = new EmailsEnviar();

                    email1.setRemet_email("<EMAIL>");
                    email2.setRemet_email("<EMAIL>");

                    email1.setRemet_nome(operador);
                    email2.setRemet_nome(operador);

                    email1.setDest_nome(filial.getRazaoSocial());
                    email2.setDest_nome(cliente.getContato());

                    email1.setDest_email(filial.getEmail());
                    email2.setDest_email(cliente.getEmail());

                    email1.setParametro(param);
                    email2.setParametro(param);
                    email1.setCodFil(codfil);
                    email2.setCodFil(codfil);
                    email1.setCodCli(cliente.getCodigo());
                    email2.setCodCli(cliente.getCodigo());

                    String assunto = empresa + " " + messages.getMessage("Relatorio") + " " + sequencia + " " + ocorrencia;

                    email1.setAssunto(assunto);
                    email1.setSmtp("smtplw.com.br");
                    email1.setMensagem(htmlEmail.replace("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">\n", ""));
                    email1.setAut_login("sasw");
                    email1.setAut_senha("xNiadJEj9607");
                    email1.setPorta(587);

                    email2.setAssunto(assunto);
                    email2.setSmtp("smtplw.com.br");
                    email2.setMensagem(htmlEmail.replace("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">\n", ""));
                    email2.setAut_login("sasw");
                    email2.setAut_senha("xNiadJEj9607");
                    email2.setPorta(587);

                    Persistencia satellite = pool.getConexao("SATELLITE");
                    String seq1 = emailDao.inserirEmail(email1, satellite);

                    EmailsEnviarAnexo emailanexo1 = new EmailsEnviarAnexo();
                    emailanexo1.setSequencia(new BigDecimal(seq1));
                    emailanexo1.setOrdem(1);
                    emailanexo1.setEndAnexo(anexoPDF);
                    emailanexo1.setNomeAnexo(anexoPDF);
                    emailanexo1.setDescAnexo(anexoPDF);

                    emailanexodao.inserirAnexos(emailanexo1, satellite);

                    if (!email2.getDest_email().equals("") && !supervisor && filtroWeb) {
                        String seq2 = emailDao.inserirEmail(email2, satellite);
                        EmailsEnviarAnexo emailanexo2 = new EmailsEnviarAnexo();
                        emailanexo2.setSequencia(new BigDecimal(seq2));
                        emailanexo2.setOrdem(1);
                        emailanexo2.setEndAnexo(anexoPDF);
                        emailanexo2.setNomeAnexo(anexoPDF);
                        emailanexo2.setDescAnexo(anexoPDF);
                        emailanexodao.inserirAnexos(emailanexo2, satellite);
                    }
                    File fHTML = new File(anexoHTML);
                    fHTML.delete();
                    File fPDF = new File(anexoPDF);
                    fPDF.delete();
                    satellite.FechaConexao();
                }
//                
//                out.print(htmlAlt);

                retorno += "<resp>1</resp>";
            } else {
                retorno += "<resp>2</resp>";
            }
            persistencia.FechaConexao();
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0) + " segundos.\r\n", codPessoa, param, logerro);

            //apagar arquivos
            apagarWsSASTemp();
            
            out.print(retorno);
        } catch (Exception e) {
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Falha RealizarRelatorio - " + e.getMessage(), codPessoa, param, logerro);
            out.print("<?xml version=\"1.0\"?><resp>0</resp>");
        }
    }

    public String mapaEstatico(String lat, String lon, String localidade) {
        String url = "https://maps.googleapis.com/maps/api/staticmap?size=640x480&format=PNG"
                + "&markers=color:red|label:@Localidade|@Latitude,@Longitude&key=AIzaSyD6DdGi_hMzj_pw9yAtveEY7GUNlu1oRGI";
        return url.replace("@Latitude", lat).replace("@Longitude", lon).replace("@Localidade", localidade).replace(" ", "%20");
    }

    private String mapa(String lat, String lon, String localidade, int quantidadeFotos) {
        String script = "<script type=\"text/javascript\">\n"
                + "	function initialize() {\n"
                + "\n"
                + "	  var myLatlng = new google.maps.LatLng(@Latitude, @Longitude);\n"
                + "	  var mapOptions = {\n"
                + "		zoom: 17,\n"
                + "		center: myLatlng,\n"
                + "		panControl: false,\n"
                + "		mapTypeControlOptions: {\n"
                + "		  mapTypeIds: [google.maps.MapTypeId.ROADMAP, 'map_style']\n"
                + "		}\n"
                + "	  }\n"
                + "\n"
                + "	  var map = new google.maps.Map(document.getElementById(\"mapa\"), mapOptions);\n"
                + "\n"
                + "	  var image = 'https://cdn1.iconfinder.com/data/icons/gpsmapicons/blue/gpsmapicons01.png';\n"
                + "	  var marcadorPersonalizado = new google.maps.Marker({\n"
                + "		  position: myLatlng,\n"
                + "		  map: map,\n"
                + "		  title: '@Localidade',\n"
                + "		  animation: google.maps.Animation.DROP\n"
                + "	  });\n"
                + "	}"
                + "\n"
                + "	function loadScript() {\n"
                + "	  var script = document.createElement(\"script\");\n"
                + "	  script.type = \"text/javascript\";\n"
                + "	  script.src = \"https://maps.googleapis.com/maps/api/js?key=AIzaSyD6DdGi_hMzj_pw9yAtveEY7GUNlu1oRGI&sensor=true&callback=initialize\";\n"
                + "	  document.body.appendChild(script);\n"
                + "	}\n"
                + "\n"
                + "	window.onload = loadScript;\n"
                + "       @ImagemRotates\n"
                + "       document.addEventListener(\"DOMContentLoaded\", function(){ \n"
                + "            @ImagemEventListener"
                + "       });"
                + "</script>";
        String rotates = "rotate_@ImagemNumero = 0; \n";
        String addEventListener = "document.getElementById(\"next_@ImagemNumero\").addEventListener(\"click\", function(){ \n"
                + "                 \n"
                + "                    if(rotate_@ImagemNumero== 360){rotate_@ImagemNumero = 0} \n"
                + "                 \n"
                + "                    rotate_@ImagemNumero = rotate_@ImagemNumero + 90; \n"
                + "                     \n"
                + "                    document.getElementById(\"imggirar_@ImagemNumero\").style.transform = \"rotate(\"+rotate_@ImagemNumero+\"deg)\"; \n"
                + "                    document.getElementById(\"trfoto\").style.height   = document.getElementById(\"imggirar_@ImagemNumero\").width+30; \n"
                + "                });\n"
                + "                 \n"
                + "                document.getElementById(\"back_@ImagemNumero\").addEventListener(\"click\", function(){ \n"
                + "                 \n"
                + "                    if(rotate_@ImagemNumero== -360){rotate_@ImagemNumero = 0} \n"
                + "                 \n"
                + "                    rotate_@ImagemNumero = rotate_@ImagemNumero + -90 ;\n"
                + "                     \n"
                + "                    document.getElementById(\"imggirar_@ImagemNumero\").style.transform = \"rotate(\"+rotate_@ImagemNumero+\"deg)\"; \n"
                + "                    document.getElementById(\"trfoto\").style.height   = document.getElementById(\"imggirar_@ImagemNumero\").width+30; \n"
                + "                });     \n";
        StringBuilder rotatesSB = new StringBuilder();
        StringBuilder eventsSB = new StringBuilder();
        for (int i = 1; i <= quantidadeFotos; i++) {
            rotatesSB.append(rotates.replace("@ImagemNumero", "" + i));
            eventsSB.append((addEventListener.replaceAll("@ImagemNumero", "" + i)));
        }
        return script.replace("@Latitude", lat).replace("@Longitude", lon).replace("@Localidade", localidade)
                .replace("@ImagemRotates", rotatesSB.toString()).replace("@ImagemEventListener", eventsSB.toString());
    }

    private String enviandoImagemMapa(String url, String param, String sequencia, String data) throws Exception {
        //Criando imagem
        URL u = new URL(url);
        InputStream in = new BufferedInputStream(u.openStream());
        ByteArrayOutputStream o = new ByteArrayOutputStream();
        byte[] buf = new byte[1024];
        int n = 0;
        while (-1 != (n = in.read(buf))) {
            o.write(buf, 0, n);
        }
        o.close();
        in.close();
        byte[] r = o.toByteArray();

        String caminho = "C:/xampp/htdocs/satellite/fotos/" + param + "/" + FuncoesString.RecortaString(data, 0, 10).replaceAll("-", "")
                + "/" + FuncoesString.preencheCom(sequencia.replace(".0", ""), "0", 8, 1);
        File diretorio = new File(caminho);
        if (!diretorio.exists()) {
            diretorio.mkdirs();  // cria diretórios caso não estejam criados
        }

        String nome = caminho + "/mapa.png";

        FileOutputStream fos = new FileOutputStream(nome);
        fos.write(r);
        fos.close();

        return nome;
    }

    //com 8 Digitos
    private String trataMatricula(String matricula) {
        if (matricula.length() == 1) {
            matricula = "0000000" + matricula;
        } else if (matricula.length() == 2) {
            matricula = "000000" + matricula;
        } else if (matricula.length() == 3) {
            matricula = "00000" + matricula;
        } else if (matricula.length() == 4) {
            matricula = "0000" + matricula;
        } else if (matricula.length() == 5) {
            matricula = "000" + matricula;
        } else if (matricula.length() == 6) {
            matricula = "00" + matricula;
        } else if (matricula.length() == 7) {
            matricula = "0" + matricula;
        }
        return matricula;
    }
    
    private String apagarWsSASTemp(){
        File diretorio = new File("C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobEWTemp");         
        FileFilter ff = new FileFilter() { 
            public boolean accept(File arquivo){ 
                return arquivo.getName().endsWith(".tmp") || 
                	   arquivo.getName().endsWith(".png") || 
                	   arquivo.getName().endsWith(".pdf"); 
            } 
        }; 
        
        File[] arquivos = diretorio.listFiles(ff); 
  
        if(arquivos != null){ 
            for(File arquivo : arquivos){ 
               arquivo.delete();  
            } 
        }
        return null;
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
