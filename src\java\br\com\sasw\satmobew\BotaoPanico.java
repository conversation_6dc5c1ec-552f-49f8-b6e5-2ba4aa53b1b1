/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satmobew;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.QueueFech;
import SasDaos.QueueFechDao;
import SasLibrary.ValidarUsuario;
import Xml.Xmls;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@WebServlet(name = "BotaoPanico", urlPatterns = {"/BotaoPanico"})
public class BotaoPanico extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;

    /**
     * Cria a pool de persistencias na criação da servlet
     */
    @Override
    public void init() {
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(BotaoPanico.class.getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter resp = response.getWriter();

        String resposta = "<?xml version=\"1.0\"?>";
        String param = request.getParameter("param");
        String codpessoa = request.getParameter("codpessoa");
        String senha = request.getParameter("senha");
        String dataAtual = request.getParameter("dataAtual");
        String horaAtual = request.getParameter("horaAtual");
        String operador = request.getParameter("operador");
        String codfil = request.getParameter("codfil");
        String local = request.getParameter("local");
        String latitude = request.getParameter("latitude");
        String longitude = request.getParameter("longitude");
        String precisao = request.getParameter("precisao");
        logerro = new ArquivoLog();

        if (null == dataAtual || dataAtual.equals("")) {
            dataAtual = getDataAtual("SQL");
        }
        if (null == horaAtual || horaAtual.equals("")) {
            horaAtual = getDataAtual("HORA");
        }

        try {
            // Gera o trace com os valores da requisição
            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), codpessoa, param, logerro);
            tStart = System.currentTimeMillis();

            //Cria a persistencia
            Persistencia persistencia = pool.getConexao(param);

            if (ValidarUsuario.ValidaUsuario(codpessoa, senha, persistencia)) {
                //Aqui  ocorre o processamento

                QueueFech queueFech = new QueueFech();
                queueFech.setCodFil(codfil);
                queueFech.setCodPessoa(codpessoa);
                queueFech.setOperador(RecortaAteEspaço(operador, 0 , 10));
                queueFech.setData(dataAtual);
                queueFech.setHora(horaAtual);
                queueFech.setDataHoraComando(dataAtual);
                queueFech.setSeqRota("0");
                queueFech.setHoraComando(horaAtual);
                queueFech.setLatitude(latitude);
                queueFech.setLongitude(longitude);
                queueFech.setPrecisao(precisao);
                queueFech.setTipoOperacao("1");
                queueFech.setComando_Ref("PANIC");
                queueFech.setComando_Crt("ENVIADO");
                queueFech.setFechIdentif(local);
                
                QueueFechDao queueFechDao = new QueueFechDao();
                queueFechDao.inserirPanico(queueFech, persistencia);
                resposta += Xmls.tag("resp", "1");// sucesso
            } else {
                resposta += Xmls.tag("resp", "2");//erro ao validar usuáio
            }
            resp.print(resposta);
            // Opcional: gera um trace com a resposta do servidor
            Trace.gerarTrace(getServletContext(), this.getServletName(), resposta, codpessoa, param, logerro);

            // Fecha a conexão
            persistencia.FechaConexao();

            // Calcula o tempo gasto pelo servidor entre criar a persistencia e enviar a respota de volta ao android e gera o trace disso.
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0) + " segundos.\r\n", codpessoa, param, logerro);
        } catch (Exception e) {
            Trace.gerarTrace(getServletContext(), this.getServletName(), "Falha - " + e.getMessage(), codpessoa, param, logerro);
            resp.print("<?xml version=\"1.0\"?>" + Xmls.tag("resp", "0"));
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

}
