/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Pe_Doctos;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Pe_DoctosDao {

    /**
     * Lista todos os documentos de uma pessoa pelo seu código
     *
     * @param codPessoa
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Pe_Doctos> listarDoctos(BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            List<Pe_Doctos> retorno = new ArrayList<>();
            String sql = " select * from Pe_Doctos where codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codPessoa);
            consulta.select();
            Pe_Doctos pe_Doctos;
            while (consulta.Proximo()) {
                pe_Doctos = new Pe_Doctos();
                pe_Doctos.setCodigo(consulta.getString("codigo"));
                pe_Doctos.setDescricao(consulta.getString("descricao"));
                pe_Doctos.setOrdem(consulta.getString("ordem"));
                pe_Doctos.setDt_alter(consulta.getString("dt_alter"));
                pe_Doctos.setHr_Alter(consulta.getString("hr_alter"));
                pe_Doctos.setOperador(consulta.getString("operador"));
                retorno.add(pe_Doctos);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Pe_DoctosDao.listarDoctos - " + e.getMessage() + "\r\n"
                    + " select * from Pe_Doctos where codigo = " + codPessoa);
        }
    }

    /**
     * Obtém o próximo sequencial disponível para documento
     *
     * @param codigo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String maxOrdem(String codigo, Persistencia persistencia) throws Exception {
        try {
            String retorno = "1";
            String sql = " SELECT isnull(MAX(Ordem),0)+1 as MaxOrdem "
                    + " FROM Pe_Doctos "
                    + " WHERE codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();
            while (consulta.Proximo()) {
                retorno = consulta.getString("MaxOrdem");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Pe_DoctosDao.maxOrdem - " + e.getMessage() + "\r\n"
                    + " SELECT isnull(MAX(Ordem),0)+1 as MaxOrdem "
                    + " FROM Pe_Doctos "
                    + " WHERE codigo = " + codigo);
        }
    }

    /**
     * Verifica a existência ou não de algum arquivo de mesmo nome para a
     * pessoa;
     *
     * @param codigo
     * @param descricao
     * @param persistencia
     * @return retorna a ordem do arquivo
     * @throws Exception
     */
    public String existeArquivo(String codigo, String descricao, Persistencia persistencia) throws Exception {
        try {
            String retorno = null;
            String sql = " SELECT ordem "
                    + " FROM Pe_Doctos "
                    + " WHERE codigo = ? and descricao = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.setString(descricao);
            consulta.select();
            while (consulta.Proximo()) {
                retorno = consulta.getString("ordem");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Pe_DoctosDao.maxOrdem - " + e.getMessage() + "\r\n"
                    + " SELECT * "
                    + " FROM Pe_Doctos "
                    + " WHERE codigo = " + codigo + "and descricao = " + descricao);
        }
    }

    /**
     * Insere um documento na tabela PE_Doctos
     *
     * @param pe_Doctos
     * @param persistencia
     * @throws Exception
     */
    public void inserirDocumento(Pe_Doctos pe_Doctos, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO Pe_Doctos (codigo, descricao, ordem, dt_alter, hr_alter, operador)"
                    + " VALUES (?, ?, ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pe_Doctos.getCodigo());
            consulta.setString(pe_Doctos.getDescricao());
            consulta.setString(pe_Doctos.getOrdem());
            consulta.setString(pe_Doctos.getDt_alter());
            consulta.setString(pe_Doctos.getHr_Alter());
            consulta.setString(pe_Doctos.getOperador());
            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Pe_DoctosDao.inserirDocumento - " + e.getMessage() + "\r\n"
                    + "  INSERT INTO Pe_Doctos (codigo, descricao, ordem, dt_alter, hr_alter, operador) "
                    + " VALUES (" + pe_Doctos.getCodigo() + ", " + pe_Doctos.getDescricao() + ", " + pe_Doctos.getOrdem() + ", " + pe_Doctos.getDt_alter() + ", "
                    + pe_Doctos.getHr_Alter() + ", " + pe_Doctos.getOperador() + ") ");
        }
    }

    /**
     * Atualiza um documento na tabela PE_Doctos
     *
     * @param pe_Doctos
     * @param persistencia
     * @throws Exception
     */
    public void atualizaDocumentos(Pe_Doctos pe_Doctos, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE Pe_Doctos SET dt_alter = ?, hr_alter = ?, operador = ? "
                    + " WHERE codigo = ? AND ordem = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pe_Doctos.getDt_alter());
            consulta.setString(pe_Doctos.getHr_Alter());
            consulta.setString(pe_Doctos.getOperador());
            consulta.setString(pe_Doctos.getCodigo());
            consulta.setString(pe_Doctos.getOrdem());
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Pe_DoctosDao.atualizaDocumentos - " + e.getMessage() + "\r\n"
                    + " UPDATE Pe_Doctos SET dt_alter = " + pe_Doctos.getDt_alter() + ", hr_alter = " + pe_Doctos.getHr_Alter() + ", "
                    + " operador = " + pe_Doctos.getOperador() + ""
                    + " WHERE codigo = " + pe_Doctos.getCodigo() + " AND ordem = " + pe_Doctos.getOrdem());
        }
    }

    /**
     * Exclui um documento no banco de dados
     *
     * @param pe_Doctos - estrutura de dados da classe Pe_Doctos
     * @param persistencia - Conexão ao Banco
     * @throws Exception
     */
    public void excluirDocumento(Pe_Doctos pe_Doctos, Persistencia persistencia) throws Exception {
        try {
            String sql = " DELETE "
                    + " FROM Pe_Doctos "
                    + " WHERE codigo = ? AND Ordem = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pe_Doctos.getCodigo());
            consulta.setString(pe_Doctos.getOrdem());
            consulta.delete();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Pe_DoctosDao.excluirDocumento - " + e.getMessage() + "\r\n"
                    + " DELETE "
                    + " FROM Pe_Doctos "
                    + " WHERE codigo = " + pe_Doctos.getCodigo() + " AND Ordem = " + pe_Doctos.getOrdem());
        }
    }
}
