/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.KMPrestador;
import SasBeans.PessoaTrajeto;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PessoaTrajetoDao {

    /**
     * Lista as posições do prestador no dia
     *
     * @param codPessoa
     * @param dataInicio
     * @param dataFim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<KMPrestador> obterRotaPrestador(String codPessoa, String dataInicio, String dataFim, Persistencia persistencia) throws Exception {
        try {
            List<KMPrestador> retorno = new ArrayList<>();
            String sql = "SELECT RowConstrainedResult.* FROM ( SELECT *, ROW_NUMBER() OVER (ORDER BY Data, HrCheg) RowNum \n"
                    + " FROM ((SELECT CONVERT(Varchar, Data, 112) Data, hora HrCheg, '' HrSaida, '0' Tipo, Latitude, Longitude, Acuracia, '' Nome,"
                    + " Distancia\n"
                    + "FROM rastrearEW\n"
                    + "WHERE Data between ? and ? AND CodPessoa = ? AND Distancia > 0.025)\n"
                    + "\n"
                    + "UNION\n"
                    + "\n"
                    + "(SELECT CONVERT(Varchar, DtCompet, 112) Data, HrCheg HrCheg, HrSaida HrSaida, Tipo Tipo,\n"
                    + "CASE WHEN Contatos.Latitude IS NOT NULL THEN Contatos.Latitude\n"
                    + "    WHEN Clientes.Latitude IS NOT NULL THEN Clientes.Latitude END Latitude,\n"
                    + "CASE WHEN Contatos.Longitude IS NOT NULL THEN Contatos.Longitude\n"
                    + "    WHEN Clientes.Longitude IS NOT NULL THEN Clientes.Longitude END Longitude,\n"
                    + "'0' Acuracia, \n"
                    + "CASE WHEN Contatos.Fantasia IS NOT NULL THEN Contatos.Fantasia\n"
                    + "    WHEN PstServ.Local IS NOT NULL THEN PstServ.Local\n"
                    + "    WHEN Clientes.NRed IS NOT NULL THEN Clientes.NRed END Nome, '0' Distancia \n"
                    + "FROM PessoaTrajeto \n"
                    + "LEFT JOIN Contatos ON Contatos.Codigo = PessoaTrajeto.CodContato\n"
                    + "                  AND Contatos.CodFil = PessoaTrajeto.CodFil\n"
                    + "LEFT JOIN Clientes ON Clientes.Codigo = PessoaTrajeto.CodCli\n"
                    + "                  AND Clientes.CodFil = PessoaTrajeto.CodFil\n"
                    + "LEFT JOIN PstServ ON PstServ.Secao = PessoaTrajeto.Secao\n"
                    + "                 AND PstServ.CodFil = PessoaTrajeto.CodFil\n"
                    + "WHERE DtCompet between ? and ? AND PessoaTrajeto.CodPessoa = ?))a ) AS RowConstrainedResult \n"
                    + " ORDER BY RowNum ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.setString(codPessoa);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.setString(codPessoa);
            consulta.select();
            PessoaTrajeto pessoaTrajeto;
            KMPrestador kMPrestador = new KMPrestador();
            String data = dataInicio;
            BigDecimal distancia = BigDecimal.ZERO;
            boolean pausado = true;
            while (consulta.Proximo()) {

                pessoaTrajeto = new PessoaTrajeto();

                pessoaTrajeto.setDtCompet(consulta.getString("Data"));
                pessoaTrajeto.setHrCheg(consulta.getString("HrCheg"));
                pessoaTrajeto.setHrSaida(consulta.getString("HrSaida"));

                pessoaTrajeto.setTipo(consulta.getString("Tipo"));
                pessoaTrajeto.setLatitude(consulta.getString("Latitude"));
                pessoaTrajeto.setLongitude(consulta.getString("Longitude"));
                pessoaTrajeto.setDescricao(consulta.getString("Nome"));

                // se a data for diferente, reinicia as contagens
                if (pessoaTrajeto.getDtCompet().equals(data)) {

                    // se a rota for pausada, só contabiliza a distância.
                    if (pessoaTrajeto.getTipo().equals("3")) {
                        pausado = true;
                        distancia = distancia.add(consulta.getBigDecimal("Distancia"));
//                        retorno.add(pessoaTrajeto);
                    } else {
                        if (pausado && (pessoaTrajeto.getTipo().equals("1") || pessoaTrajeto.getTipo().equals("2"))) {
                            pausado = false;
                        }

                        if (!pausado) {
                            // tipo = 0: posição rastrearEW
                            if (pessoaTrajeto.getTipo().equals("0")) {
                                // se a rota não estiver pausada, atualiza a distancia
                                distancia = distancia.add(consulta.getBigDecimal("Distancia"));
                            } else if (pessoaTrajeto.getTipo().equals("1")) { // tipo = 1: inicio de rota ou retomada da rota
                                // se não haver descricao, retomada de rota
                                if (pessoaTrajeto.getDescricao().equals("")) {

                                } else { // senão, início da rota do dia
                                    distancia = BigDecimal.ZERO;
                                    kMPrestador = new KMPrestador();
                                    kMPrestador.setData(pessoaTrajeto.getDtCompet());
                                    kMPrestador.setOrigem(pessoaTrajeto.getDescricao());
                                }
                            } else if (pessoaTrajeto.getTipo().equals("2")) { // tipo = 2: serviço normal
                                // insere o serviço como destino
                                kMPrestador.setDestino(pessoaTrajeto.getDescricao());
                                kMPrestador.setKmPercorrido(distancia);
                                retorno.add(kMPrestador);

                                // e prepara como origem do próximo
                                distancia = BigDecimal.ZERO;
                                kMPrestador = new KMPrestador();
                                kMPrestador.setData(pessoaTrajeto.getDtCompet());
                                kMPrestador.setOrigem(pessoaTrajeto.getDescricao());
                            }

                        }
                    }

                } else {
                    data = consulta.getString("Data");
                    pausado = true;
                    distancia = BigDecimal.ZERO;

                    if (pessoaTrajeto.getTipo().equals("3")) {
                        pausado = true;
                        distancia = distancia.add(consulta.getBigDecimal("Distancia"));
//                        retorno.add(pessoaTrajeto);
                    } else {
                        if (pausado && (pessoaTrajeto.getTipo().equals("1") || pessoaTrajeto.getTipo().equals("2"))) {
                            pausado = false;
                        }

                        if (!pausado) {
                            // tipo = 0: posição rastrearEW
                            if (pessoaTrajeto.getTipo().equals("0")) {
                                // se a rota não estiver pausada, atualiza a distancia
                                distancia = distancia.add(consulta.getBigDecimal("Distancia"));
                            } else if (pessoaTrajeto.getTipo().equals("1")) { // tipo = 1: inicio de rota ou retomada da rota
                                // se não haver descricao, retomada de rota
                                if (pessoaTrajeto.getDescricao().equals("")) {

                                } else { // senão, início da rota do dia
                                    distancia = BigDecimal.ZERO;
                                    kMPrestador = new KMPrestador();
                                    kMPrestador.setData(pessoaTrajeto.getDtCompet());
                                    kMPrestador.setOrigem(pessoaTrajeto.getDescricao());
                                }
                            } else if (pessoaTrajeto.getTipo().equals("2")) { // tipo = 2: serviço normal
                                // insere o serviço como destino
                                kMPrestador.setDestino(pessoaTrajeto.getDescricao());
                                kMPrestador.setKmPercorrido(distancia);
                                retorno.add(kMPrestador);

                                // e prepara como origem do próximo
                                distancia = BigDecimal.ZERO;
                                kMPrestador = new KMPrestador();
                                kMPrestador.setData(pessoaTrajeto.getDtCompet());
                                kMPrestador.setOrigem(pessoaTrajeto.getDescricao());
                            }

                        }
                    }
                }
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("LogsSatMobEWDao.obterRotaPrestador - " + e.getMessage() + "\r\n"
                    + "(SELECT Data Data, hora HrCheg, '' HrSaida, '0' Tipo, Latitude, Longitude, Acuracia, '' Nome\n"
                    + "FROM rastrearEW\n"
                    + "WHERE Data = " + " AND CodPessoa = " + codPessoa + ")\n"
                    + "\n"
                    + "UNION\n"
                    + "\n"
                    + "(SELECT DtCompet Data, HrCheg HrCheg, HrSaida HrSaida, Tipo Tipo,\n"
                    + "CASE WHEN Contatos.Latitude IS NOT NULL THEN Contatos.Latitude\n"
                    + "    WHEN Clientes.Latitude IS NOT NULL THEN Clientes.Latitude END Latitude,\n"
                    + "CASE WHEN Contatos.Longitude IS NOT NULL THEN Contatos.Longitude\n"
                    + "    WHEN Clientes.Longitude IS NOT NULL THEN Clientes.Longitude END Longitude,\n"
                    + "'0' Acuracia, \n"
                    + "CASE WHEN Contatos.Fantasia IS NOT NULL THEN Contatos.Fantasia\n"
                    + "    WHEN PstServ.Local IS NOT NULL THEN PstServ.Local\n"
                    + "    WHEN Clientes.NRed IS NOT NULL THEN Clientes.NRed END Nome \n"
                    + "FROM PessoaTrajeto \n"
                    + "LEFT JOIN Contatos ON Contatos.Codigo = PessoaTrajeto.CodContato\n"
                    + "                  AND Contatos.CodFil = PessoaTrajeto.CodFil\n"
                    + "LEFT JOIN Clientes ON Clientes.Codigo = PessoaTrajeto.CodCli\n"
                    + "                  AND Clientes.CodFil = PessoaTrajeto.CodFil\n"
                    + "LEFT JOIN PstServ ON PstServ.Secao = PessoaTrajeto.Secao\n"
                    + "                 AND PstServ.CodFil = PessoaTrajeto.CodFil\n"
                    + "WHERE DtCompet = " + " AND PessoaTrajeto.CodPessoa = " + codPessoa + ")\n"
                    + "\n"
                    + "ORDER BY HrCheg");
        }
    }

    /**
     * Finaliza um trajeto atualizando os horários de saida e tempo
     *
     * @param pessoaTrajeto
     * @param persistencia
     * @throws Exception
     */
    public void finalizaTrajeto(PessoaTrajeto pessoaTrajeto, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE PessoaTrajeto SET HrSaida = ?, Tempo = ?, Operador = ?, Dt_Alter = ?, Hr_Alter = ? "
                    + " WHERE CodPessoa = ? AND DtCompet = ? AND Ordem = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoaTrajeto.getHrSaida());
            consulta.setString(pessoaTrajeto.getTempo());
            consulta.setString(pessoaTrajeto.getOperador());
            consulta.setString(pessoaTrajeto.getDt_Alter());
            consulta.setString(pessoaTrajeto.getHr_Alter());
            consulta.setString(pessoaTrajeto.getCodPessoa());
            consulta.setString(pessoaTrajeto.getDtCompet());
            consulta.setString(pessoaTrajeto.getOrdem());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaTrajetoDao.finalizaTrajeto - " + e.getMessage() + "\r\n"
                    + " UPDATE PessoaTrajeto SET HrSaida = " + pessoaTrajeto.getHrSaida() + ", Tempo = " + pessoaTrajeto.getTempo() + ", "
                    + " Operador = " + pessoaTrajeto.getOperador() + ", Dt_Alter = " + pessoaTrajeto.getDt_Alter() + ", Hr_Alter = " + pessoaTrajeto.getHr_Alter()
                    + " WHERE CodPessoa = " + pessoaTrajeto.getCodPessoa() + " AND DtCompet = " + pessoaTrajeto.getDtCompet() + " "
                    + " AND Ordem = " + pessoaTrajeto.getOrdem());
        }
    }

    /**
     * Insere uma nova entrada na tabela PessoaTrajeto e retorna a ordem
     *
     * @param pessoaTrajeto
     * @param persistencia
     * @return ordem
     * @throws Exception
     */
    public String inserirTrajeto(PessoaTrajeto pessoaTrajeto, Persistencia persistencia) throws Exception {
        try {
            String sqlOrdem = " SELECT ISNULL(MAX(Ordem),0) + 1 Ordem "
                    + " FROM PessoaTrajeto "
                    + " WHERE CodPessoa = ? AND DtCompet = ? ";
            String sql = "INSERT INTO PessoaTrajeto (CodPessoa, DtCompet, Ordem, Tipo, HrCheg, HrSaida, Tempo, CodContato, "
                    + " CodCli, Secao, CodFil, Operador, Dt_Alter, Hr_Alter) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            for (int i = 0; i < 20; i++) {
                Consulta consultaOrdem = new Consulta(sqlOrdem, persistencia);
                consultaOrdem.setString(pessoaTrajeto.getCodPessoa());
                consultaOrdem.setString(pessoaTrajeto.getDtCompet());
                consultaOrdem.select();
                while (consultaOrdem.Proximo()) {
                    pessoaTrajeto.setOrdem(consultaOrdem.getString("Ordem"));
                }
                consultaOrdem.close();
                Consulta consulta = new Consulta(sql, persistencia);
                consulta.setString(pessoaTrajeto.getCodPessoa());
                consulta.setString(pessoaTrajeto.getDtCompet());
                consulta.setString(pessoaTrajeto.getOrdem());
                consulta.setString(pessoaTrajeto.getTipo());
                consulta.setString(pessoaTrajeto.getHrCheg());
                consulta.setString(pessoaTrajeto.getHrSaida());
                consulta.setString(pessoaTrajeto.getTempo());
                consulta.setString(pessoaTrajeto.getCodContato());
                consulta.setString(pessoaTrajeto.getCodCli());
                consulta.setString(pessoaTrajeto.getSecao());
                consulta.setString(pessoaTrajeto.getCodFil());
                consulta.setString(pessoaTrajeto.getOperador());
                consulta.setString(pessoaTrajeto.getDt_Alter());
                consulta.setString(pessoaTrajeto.getHr_Alter());
                consulta.insert();
                consulta.close();
                return pessoaTrajeto.getOrdem();
            }
            return null;
        } catch (Exception e) {
            throw new Exception("PessoaTrajetoDao.inserirTrajeto - " + e.getMessage() + "\r\n"
                    + "INSERT INTO PessoaTrajeto (CodPessoa, DtCompet, Ordem, Tipo, HrCheg, HrSaida, Tempo, CodContato, "
                    + " CodCli, Secao, CodFil, Operador, Dt_Alter, Hr_Alter) VALUES (" + pessoaTrajeto.getCodPessoa() + "," + pessoaTrajeto.getDtCompet() + ","
                    + pessoaTrajeto.getOrdem() + "," + pessoaTrajeto.getTipo() + "," + pessoaTrajeto.getHrCheg() + "," + pessoaTrajeto.getHrSaida() + ","
                    + pessoaTrajeto.getTempo() + "," + pessoaTrajeto.getCodContato() + "," + pessoaTrajeto.getCodCli() + "," + pessoaTrajeto.getSecao() + ","
                    + pessoaTrajeto.getCodFil() + "," + pessoaTrajeto.getOperador() + "," + pessoaTrajeto.getDt_Alter() + "," + pessoaTrajeto.getHr_Alter());
        }
    }

    /**
     * Lista a rota feita por um pessoa em um dia.
     *
     * @param codPessoa
     * @param dtCompet
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PessoaTrajeto> obterRota(String codPessoa, String dtCompet, Persistencia persistencia) throws Exception {
        try {
            List<PessoaTrajeto> retorno = new ArrayList<>();
            String sql = "SELECT PessoaTrajeto.CodPessoa, CONVERT(VARCHAR, PessoaTrajeto.DtCompet, 112) DtCompet, PessoaTrajeto.Ordem, PessoaTrajeto.Tipo, \n"
                    + "PessoaTrajeto.HrCheg, PessoaTrajeto.HrSaida, PessoaTrajeto.Tempo, PessoaTrajeto.CodContato, PessoaTrajeto.CodCli, \n"
                    + "PessoaTrajeto.Secao, PessoaTrajeto.CodFil, PessoaTrajeto.Operador, CONVERT(VARCHAR, PessoaTrajeto.Dt_Alter, 112) Dt_Alter,\n"
                    + "PessoaTrajeto.Hr_Alter,\n"
                    + "CASE WHEN Clientes.Ende IS NOT NULL THEN Clientes.Ende\n"
                    + "     WHEN Contatos.Endereco IS NOT NULL THEN Contatos.Endereco END Endereco,\n"
                    + "CASE WHEN PstServ.Local IS NOT NULL THEN PstServ.Local\n"
                    + "     WHEN Clientes.NRed IS NOT NULL THEN Clientes.NRed\n"
                    + "     WHEN Contatos.Nome IS NOT NULL THEN Contatos.Nome END Descricao\n"
                    + "\n"
                    + "FROM PessoaTrajeto \n"
                    + "LEFT JOIN Clientes ON Clientes.Codigo = PessoaTrajeto.CodCli\n"
                    + "                  AND Clientes.CodFil = PessoaTrajeto.CodFil\n"
                    + "LEFT JOIN PstServ ON PstServ.Secao = PessoaTrajeto.Secao\n"
                    + "                 AND PstServ.CodFil = PessoaTrajeto.CodFil\n"
                    + "LEFT JOIN Contatos ON Contatos.Codigo = PessoaTrajeto.CodContato\n"
                    + "                  AND Contatos.CodFil = PessoaTrajeto.CodFil\n"
                    + "WHERE PessoaTrajeto.CodPessoa = ? AND PessoaTrajeto.DtCompet = ?\n"
                    + "ORDER BY PessoaTrajeto.Ordem ASC";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codPessoa);
            consulta.setString(dtCompet);
            consulta.select();
            PessoaTrajeto pessoaTrajeto;
            while (consulta.Proximo()) {
                pessoaTrajeto = new PessoaTrajeto();
                pessoaTrajeto.setDescricao(consulta.getString("Descricao"));
                pessoaTrajeto.setEndereco(consulta.getString("Endereco"));
                pessoaTrajeto.setCodPessoa(consulta.getString("CodPessoa"));
                pessoaTrajeto.setDtCompet(consulta.getString("DtCompet"));
                pessoaTrajeto.setOrdem(consulta.getString("Ordem"));
                pessoaTrajeto.setTipo(consulta.getString("Tipo"));
                pessoaTrajeto.setHrCheg(consulta.getString("HrCheg"));
                pessoaTrajeto.setHrSaida(consulta.getString("HrSaida"));
                pessoaTrajeto.setTempo(consulta.getString("Tempo"));
                pessoaTrajeto.setCodContato(consulta.getString("CodContato"));
                pessoaTrajeto.setCodCli(consulta.getString("CodCli"));
                pessoaTrajeto.setSecao(consulta.getString("Secao"));
                pessoaTrajeto.setCodFil(consulta.getString("CodFil"));
                pessoaTrajeto.setOperador(consulta.getString("Operador"));
                pessoaTrajeto.setDt_Alter(consulta.getString("Dt_Alter"));
                pessoaTrajeto.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(pessoaTrajeto);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaTrajetoDao.obterRota - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM PessoaTrajeto "
                    + " WHERE CodPessoa = " + codPessoa + " AND DtCompet = " + dtCompet
                    + " ORDER BY Ordem ASC ");
        }
    }

    /**
     * Lista a rota feita por um pessoa em um dia.
     *
     * @param codPessoa
     * @param dtCompet
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PessoaTrajeto> obterRotaPosicoes(String codPessoa, String dtCompet, Persistencia persistencia) throws Exception {
        try {
            List<PessoaTrajeto> retorno = new ArrayList<>();
            String sql = "SELECT case when Tipo = '0' Then RowNum else Ordem end NewOrder,"
                    + " RowConstrainedResult.* FROM ( SELECT *, ROW_NUMBER() OVER ( ORDER BY HrCheg) RowNum \n"
                    + "FROM (\n"
                    + "\n"
                    + "(SELECT rastrearEW.CodPessoa, CONVERT(Varchar, Data, 112) DtCompet, '' Ordem, hora HrCheg, '' HrSaida, \n"
                    + "    '0' Tipo, Latitude, Longitude, '' Endereco, Acuracia, '' Descricao, \n"
                    + "'' CodContato, '' CodCli, '' Secao, '' CodFil, '0' Tempo,"
                    + " '' Operador, '' Dt_Alter, '' Hr_Alter\n"
                    + "FROM rastrearEW\n"
                    + "WHERE Data = ? AND CodPessoa = ? AND Acuracia < 50)\n"
                    + "\n"
                    + "UNION\n"
                    + "\n"
                    + "(SELECT  PessoaTrajeto.CodPessoa, CONVERT(Varchar, DtCompet, 112) DtCompet, PessoaTrajeto.Ordem, HrCheg HrCheg, \n"
                    + "    HrSaida HrSaida, Tipo Tipo,\n"
                    + "CASE WHEN Contatos.Latitude IS NOT NULL THEN Contatos.Latitude\n"
                    + "   WHEN Clientes.Latitude IS NOT NULL THEN Clientes.Latitude END Latitude,\n"
                    + "CASE WHEN Contatos.Longitude IS NOT NULL THEN Contatos.Longitude\n"
                    + "   WHEN Clientes.Longitude IS NOT NULL THEN Clientes.Longitude END Longitude,\n"
                    + "CASE WHEN Clientes.Ende IS NOT NULL THEN Clientes.Ende\n"
                    + "     WHEN Contatos.Endereco IS NOT NULL THEN Contatos.Endereco END Endereco,\n"
                    + "'0' Acuracia, \n"
                    + "CASE WHEN Contatos.Fantasia IS NOT NULL THEN Contatos.Fantasia\n"
                    + "   WHEN PstServ.Local IS NOT NULL THEN PstServ.Local\n"
                    + "   WHEN Clientes.NRed IS NOT NULL THEN Clientes.NRed END Descricao,\n"
                    + "PessoaTrajeto.CodContato, PessoaTrajeto.CodCli, PessoaTrajeto.Secao, PessoaTrajeto.CodFil, PessoaTrajeto.Tempo,"
                    + " PessoaTrajeto.Operador, PessoaTrajeto.Dt_Alter, PessoaTrajeto.Hr_Alter\n"
                    + "FROM PessoaTrajeto \n"
                    + "LEFT JOIN Contatos ON Contatos.Codigo = PessoaTrajeto.CodContato\n"
                    + "                 AND Contatos.CodFil = PessoaTrajeto.CodFil\n"
                    + "LEFT JOIN Clientes ON Clientes.Codigo = PessoaTrajeto.CodCli\n"
                    + "                 AND Clientes.CodFil = PessoaTrajeto.CodFil\n"
                    + "LEFT JOIN PstServ ON PstServ.Secao = PessoaTrajeto.Secao\n"
                    + "                AND PstServ.CodFil = PessoaTrajeto.CodFil\n"
                    + "WHERE DtCompet = ? AND PessoaTrajeto.CodPessoa = ?)\n"
                    + ")a ) AS RowConstrainedResult \n"
                    + "ORDER BY RowNum ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtCompet);
            consulta.setString(codPessoa);
            consulta.setString(dtCompet);
            consulta.setString(codPessoa);
            consulta.select();
            PessoaTrajeto pessoaTrajeto;
            boolean pausado = true;
            while (consulta.Proximo()) {
                pessoaTrajeto = new PessoaTrajeto();
                pessoaTrajeto.setDescricao(consulta.getString("Descricao"));
                pessoaTrajeto.setEndereco(consulta.getString("Endereco"));
                pessoaTrajeto.setCodPessoa(consulta.getString("CodPessoa"));
                pessoaTrajeto.setDtCompet(consulta.getString("DtCompet"));
                pessoaTrajeto.setOrdem(consulta.getString("NewOrder"));
                pessoaTrajeto.setTipo(consulta.getString("Tipo"));
                pessoaTrajeto.setHrCheg(consulta.getString("HrCheg"));
                pessoaTrajeto.setHrSaida(consulta.getString("HrSaida"));
                pessoaTrajeto.setTempo(consulta.getString("Tempo"));
                pessoaTrajeto.setCodContato(consulta.getString("CodContato"));
                pessoaTrajeto.setCodCli(consulta.getString("CodCli"));
                pessoaTrajeto.setSecao(consulta.getString("Secao"));
                pessoaTrajeto.setCodFil(consulta.getString("CodFil"));
                pessoaTrajeto.setOperador(consulta.getString("Operador"));
                pessoaTrajeto.setDt_Alter(consulta.getString("Dt_Alter"));
                pessoaTrajeto.setHr_Alter(consulta.getString("Hr_Alter"));
                pessoaTrajeto.setLatitude(consulta.getString("Latitude"));
                pessoaTrajeto.setLongitude(consulta.getString("Longitude"));

                if (pessoaTrajeto.getTipo().equals("4")) {
                    retorno.add(pessoaTrajeto);
                    break;
                } else if (pessoaTrajeto.getTipo().equals("3")) {
                    pausado = true;
                    retorno.add(pessoaTrajeto);
                } else {
                    if (pausado && (pessoaTrajeto.getTipo().equals("1") || pessoaTrajeto.getTipo().equals("2"))) {
                        pausado = false;
                    }

                    if (!pausado) {
                        retorno.add(pessoaTrajeto);
                    }
                }
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaTrajetoDao.obterRota - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM PessoaTrajeto "
                    + " WHERE CodPessoa = " + codPessoa + " AND DtCompet = " + dtCompet
                    + " ORDER BY Ordem ASC ");
        }
    }
}
